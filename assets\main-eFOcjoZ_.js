import{s as yo,d as fi,u as ne,a as wo,c as _e,p as mn,r as z,w as Qn,h as di,n as Xt,i as je,b as ze,_ as sn,o as at,e as fe,f as ce,g as L,j as h,k as y,l as O,m as xo,F as He,q as re,t as bo,v as Pe,x as W,y as et,z as Fe,A as Eo,B as pi,C as cr,D as fr,E as Q,G as So,H as Gt,I as Zn,J as hi,K as Jn,L as jn,M as ko,N as Ro,O as Co,P as $o,Q as Po,R as Vo,S as Mo,T as Ao,U as No,V as Io}from"./index-xpKufulQ.js";/*!
  * vue-router v4.5.1
  * (c) 2025 <PERSON>
  * @license MIT
  */const We=typeof document<"u";function mi(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function To(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&mi(e.default)}const J=Object.assign;function _n(e,t){const n={};for(const r in t){const i=t[r];n[r]=ke(i)?i.map(e):e(i)}return n}const vt=()=>{},ke=Array.isArray,_i=/#/g,Uo=/&/g,Do=/\//g,Oo=/=/g,Lo=/\?/g,gi=/\+/g,Fo=/%5B/g,qo=/%5D/g,vi=/%5E/g,zo=/%60/g,yi=/%7B/g,Ho=/%7C/g,wi=/%7D/g,Bo=/%20/g;function er(e){return encodeURI(""+e).replace(Ho,"|").replace(Fo,"[").replace(qo,"]")}function Xo(e){return er(e).replace(yi,"{").replace(wi,"}").replace(vi,"^")}function kn(e){return er(e).replace(gi,"%2B").replace(Bo,"+").replace(_i,"%23").replace(Uo,"%26").replace(zo,"`").replace(yi,"{").replace(wi,"}").replace(vi,"^")}function Go(e){return kn(e).replace(Oo,"%3D")}function Yo(e){return er(e).replace(_i,"%23").replace(Lo,"%3F")}function Ko(e){return e==null?"":Yo(e).replace(Do,"%2F")}function wt(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Wo=/\/$/,Qo=e=>e.replace(Wo,"");function gn(e,t,n="/"){let r,i={},o="",l="";const s=t.indexOf("#");let a=t.indexOf("?");return s<a&&s>=0&&(a=-1),a>-1&&(r=t.slice(0,a),o=t.slice(a+1,s>-1?s:t.length),i=e(o)),s>-1&&(r=r||t.slice(0,s),l=t.slice(s,t.length)),r=el(r??t,n),{fullPath:r+(o&&"?")+o+l,path:r,query:i,hash:wt(l)}}function Zo(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function dr(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Jo(e,t,n){const r=t.matched.length-1,i=n.matched.length-1;return r>-1&&r===i&&tt(t.matched[r],n.matched[i])&&xi(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function tt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function xi(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!jo(e[n],t[n]))return!1;return!0}function jo(e,t){return ke(e)?pr(e,t):ke(t)?pr(t,e):e===t}function pr(e,t){return ke(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function el(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),i=r[r.length-1];(i===".."||i===".")&&r.push("");let o=n.length-1,l,s;for(l=0;l<r.length;l++)if(s=r[l],s!==".")if(s==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+r.slice(l).join("/")}const Ue={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var xt;(function(e){e.pop="pop",e.push="push"})(xt||(xt={}));var yt;(function(e){e.back="back",e.forward="forward",e.unknown=""})(yt||(yt={}));function tl(e){if(!e)if(We){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Qo(e)}const nl=/^[^#]+#/;function rl(e,t){return e.replace(nl,"#")+t}function il(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const un=()=>({left:window.scrollX,top:window.scrollY});function ol(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),i=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!i)return;t=il(i,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function hr(e,t){return(history.state?history.state.position-t:-1)+e}const Rn=new Map;function ll(e,t){Rn.set(e,t)}function al(e){const t=Rn.get(e);return Rn.delete(e),t}let sl=()=>location.protocol+"//"+location.host;function bi(e,t){const{pathname:n,search:r,hash:i}=t,o=e.indexOf("#");if(o>-1){let s=i.includes(e.slice(o))?e.slice(o).length:1,a=i.slice(s);return a[0]!=="/"&&(a="/"+a),dr(a,"")}return dr(n,e)+r+i}function ul(e,t,n,r){let i=[],o=[],l=null;const s=({state:d})=>{const p=bi(e,location),m=n.value,k=t.value;let C=0;if(d){if(n.value=p,t.value=d,l&&l===m){l=null;return}C=k?d.position-k.position:0}else r(p);i.forEach(A=>{A(n.value,m,{delta:C,type:xt.pop,direction:C?C>0?yt.forward:yt.back:yt.unknown})})};function a(){l=n.value}function u(d){i.push(d);const p=()=>{const m=i.indexOf(d);m>-1&&i.splice(m,1)};return o.push(p),p}function c(){const{history:d}=window;d.state&&d.replaceState(J({},d.state,{scroll:un()}),"")}function f(){for(const d of o)d();o=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:a,listen:u,destroy:f}}function mr(e,t,n,r=!1,i=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:i?un():null}}function cl(e){const{history:t,location:n}=window,r={value:bi(e,n)},i={value:t.state};i.value||o(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(a,u,c){const f=e.indexOf("#"),d=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+a:sl()+e+a;try{t[c?"replaceState":"pushState"](u,"",d),i.value=u}catch(p){console.error(p),n[c?"replace":"assign"](d)}}function l(a,u){const c=J({},t.state,mr(i.value.back,a,i.value.forward,!0),u,{position:i.value.position});o(a,c,!0),r.value=a}function s(a,u){const c=J({},i.value,t.state,{forward:a,scroll:un()});o(c.current,c,!0);const f=J({},mr(r.value,a,null),{position:c.position+1},u);o(a,f,!1),r.value=a}return{location:r,state:i,push:s,replace:l}}function fl(e){e=tl(e);const t=cl(e),n=ul(e,t.state,t.location,t.replace);function r(o,l=!0){l||n.pauseListeners(),history.go(o)}const i=J({location:"",base:e,go:r,createHref:rl.bind(null,e)},t,n);return Object.defineProperty(i,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(i,"state",{enumerable:!0,get:()=>t.state.value}),i}function dl(e){return typeof e=="string"||e&&typeof e=="object"}function Ei(e){return typeof e=="string"||typeof e=="symbol"}const Si=Symbol("");var _r;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(_r||(_r={}));function nt(e,t){return J(new Error,{type:e,[Si]:!0},t)}function Ae(e,t){return e instanceof Error&&Si in e&&(t==null||!!(e.type&t))}const gr="[^/]+?",pl={sensitive:!1,strict:!1,start:!0,end:!0},hl=/[.+*?^${}()[\]/\\]/g;function ml(e,t){const n=J({},pl,t),r=[];let i=n.start?"^":"";const o=[];for(const u of e){const c=u.length?[]:[90];n.strict&&!u.length&&(i+="/");for(let f=0;f<u.length;f++){const d=u[f];let p=40+(n.sensitive?.25:0);if(d.type===0)f||(i+="/"),i+=d.value.replace(hl,"\\$&"),p+=40;else if(d.type===1){const{value:m,repeatable:k,optional:C,regexp:A}=d;o.push({name:m,repeatable:k,optional:C});const P=A||gr;if(P!==gr){p+=10;try{new RegExp(`(${P})`)}catch(E){throw new Error(`Invalid custom RegExp for param "${m}" (${P}): `+E.message)}}let S=k?`((?:${P})(?:/(?:${P}))*)`:`(${P})`;f||(S=C&&u.length<2?`(?:/${S})`:"/"+S),C&&(S+="?"),i+=S,p+=20,C&&(p+=-8),k&&(p+=-20),P===".*"&&(p+=-50)}c.push(p)}r.push(c)}if(n.strict&&n.end){const u=r.length-1;r[u][r[u].length-1]+=.7000000000000001}n.strict||(i+="/?"),n.end?i+="$":n.strict&&!i.endsWith("/")&&(i+="(?:/|$)");const l=new RegExp(i,n.sensitive?"":"i");function s(u){const c=u.match(l),f={};if(!c)return null;for(let d=1;d<c.length;d++){const p=c[d]||"",m=o[d-1];f[m.name]=p&&m.repeatable?p.split("/"):p}return f}function a(u){let c="",f=!1;for(const d of e){(!f||!c.endsWith("/"))&&(c+="/"),f=!1;for(const p of d)if(p.type===0)c+=p.value;else if(p.type===1){const{value:m,repeatable:k,optional:C}=p,A=m in u?u[m]:"";if(ke(A)&&!k)throw new Error(`Provided param "${m}" is an array but it is not repeatable (* or + modifiers)`);const P=ke(A)?A.join("/"):A;if(!P)if(C)d.length<2&&(c.endsWith("/")?c=c.slice(0,-1):f=!0);else throw new Error(`Missing required param "${m}"`);c+=P}}return c||"/"}return{re:l,score:r,keys:o,parse:s,stringify:a}}function _l(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function ki(e,t){let n=0;const r=e.score,i=t.score;for(;n<r.length&&n<i.length;){const o=_l(r[n],i[n]);if(o)return o;n++}if(Math.abs(i.length-r.length)===1){if(vr(r))return 1;if(vr(i))return-1}return i.length-r.length}function vr(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const gl={type:0,value:""},vl=/[a-zA-Z0-9_]/;function yl(e){if(!e)return[[]];if(e==="/")return[[gl]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(p){throw new Error(`ERR (${n})/"${u}": ${p}`)}let n=0,r=n;const i=[];let o;function l(){o&&i.push(o),o=[]}let s=0,a,u="",c="";function f(){u&&(n===0?o.push({type:0,value:u}):n===1||n===2||n===3?(o.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:u,regexp:c,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),u="")}function d(){u+=a}for(;s<e.length;){if(a=e[s++],a==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:a==="/"?(u&&f(),l()):a===":"?(f(),n=1):d();break;case 4:d(),n=r;break;case 1:a==="("?n=2:vl.test(a)?d():(f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&s--);break;case 2:a===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+a:n=3:c+=a;break;case 3:f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&s--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),l(),i}function wl(e,t,n){const r=ml(yl(e.path),n),i=J(r,{record:e,parent:t,children:[],alias:[]});return t&&!i.record.aliasOf==!t.record.aliasOf&&t.children.push(i),i}function xl(e,t){const n=[],r=new Map;t=br({strict:!1,end:!0,sensitive:!1},t);function i(f){return r.get(f)}function o(f,d,p){const m=!p,k=wr(f);k.aliasOf=p&&p.record;const C=br(t,f),A=[k];if("alias"in f){const E=typeof f.alias=="string"?[f.alias]:f.alias;for(const M of E)A.push(wr(J({},k,{components:p?p.record.components:k.components,path:M,aliasOf:p?p.record:k})))}let P,S;for(const E of A){const{path:M}=E;if(d&&M[0]!=="/"){const V=d.record.path,x=V[V.length-1]==="/"?"":"/";E.path=d.record.path+(M&&x+M)}if(P=wl(E,d,C),p?p.alias.push(P):(S=S||P,S!==P&&S.alias.push(P),m&&f.name&&!xr(P)&&l(f.name)),Ri(P)&&a(P),k.children){const V=k.children;for(let x=0;x<V.length;x++)o(V[x],P,p&&p.children[x])}p=p||P}return S?()=>{l(S)}:vt}function l(f){if(Ei(f)){const d=r.get(f);d&&(r.delete(f),n.splice(n.indexOf(d),1),d.children.forEach(l),d.alias.forEach(l))}else{const d=n.indexOf(f);d>-1&&(n.splice(d,1),f.record.name&&r.delete(f.record.name),f.children.forEach(l),f.alias.forEach(l))}}function s(){return n}function a(f){const d=Sl(f,n);n.splice(d,0,f),f.record.name&&!xr(f)&&r.set(f.record.name,f)}function u(f,d){let p,m={},k,C;if("name"in f&&f.name){if(p=r.get(f.name),!p)throw nt(1,{location:f});C=p.record.name,m=J(yr(d.params,p.keys.filter(S=>!S.optional).concat(p.parent?p.parent.keys.filter(S=>S.optional):[]).map(S=>S.name)),f.params&&yr(f.params,p.keys.map(S=>S.name))),k=p.stringify(m)}else if(f.path!=null)k=f.path,p=n.find(S=>S.re.test(k)),p&&(m=p.parse(k),C=p.record.name);else{if(p=d.name?r.get(d.name):n.find(S=>S.re.test(d.path)),!p)throw nt(1,{location:f,currentLocation:d});C=p.record.name,m=J({},d.params,f.params),k=p.stringify(m)}const A=[];let P=p;for(;P;)A.unshift(P.record),P=P.parent;return{name:C,path:k,params:m,matched:A,meta:El(A)}}e.forEach(f=>o(f));function c(){n.length=0,r.clear()}return{addRoute:o,resolve:u,removeRoute:l,clearRoutes:c,getRoutes:s,getRecordMatcher:i}}function yr(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function wr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:bl(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function bl(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function xr(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function El(e){return e.reduce((t,n)=>J(t,n.meta),{})}function br(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function Sl(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;ki(e,t[o])<0?r=o:n=o+1}const i=kl(e);return i&&(r=t.lastIndexOf(i,r-1)),r}function kl(e){let t=e;for(;t=t.parent;)if(Ri(t)&&ki(e,t)===0)return t}function Ri({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Rl(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let i=0;i<r.length;++i){const o=r[i].replace(gi," "),l=o.indexOf("="),s=wt(l<0?o:o.slice(0,l)),a=l<0?null:wt(o.slice(l+1));if(s in t){let u=t[s];ke(u)||(u=t[s]=[u]),u.push(a)}else t[s]=a}return t}function Er(e){let t="";for(let n in e){const r=e[n];if(n=Go(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(ke(r)?r.map(o=>o&&kn(o)):[r&&kn(r)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function Cl(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=ke(r)?r.map(i=>i==null?null:""+i):r==null?r:""+r)}return t}const $l=Symbol(""),Sr=Symbol(""),cn=Symbol(""),Ci=Symbol(""),Cn=Symbol("");function ut(){let e=[];function t(r){return e.push(r),()=>{const i=e.indexOf(r);i>-1&&e.splice(i,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function De(e,t,n,r,i,o=l=>l()){const l=r&&(r.enterCallbacks[i]=r.enterCallbacks[i]||[]);return()=>new Promise((s,a)=>{const u=d=>{d===!1?a(nt(4,{from:n,to:t})):d instanceof Error?a(d):dl(d)?a(nt(2,{from:t,to:d})):(l&&r.enterCallbacks[i]===l&&typeof d=="function"&&l.push(d),s())},c=o(()=>e.call(r&&r.instances[i],t,n,u));let f=Promise.resolve(c);e.length<3&&(f=f.then(u)),f.catch(d=>a(d))})}function vn(e,t,n,r,i=o=>o()){const o=[];for(const l of e)for(const s in l.components){let a=l.components[s];if(!(t!=="beforeRouteEnter"&&!l.instances[s]))if(mi(a)){const c=(a.__vccOpts||a)[t];c&&o.push(De(c,n,r,l,s,i))}else{let u=a();o.push(()=>u.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${s}" at "${l.path}"`);const f=To(c)?c.default:c;l.mods[s]=c,l.components[s]=f;const p=(f.__vccOpts||f)[t];return p&&De(p,n,r,l,s,i)()}))}}return o}function kr(e){const t=je(cn),n=je(Ci),r=_e(()=>{const a=ne(e.to);return t.resolve(a)}),i=_e(()=>{const{matched:a}=r.value,{length:u}=a,c=a[u-1],f=n.matched;if(!c||!f.length)return-1;const d=f.findIndex(tt.bind(null,c));if(d>-1)return d;const p=Rr(a[u-2]);return u>1&&Rr(c)===p&&f[f.length-1].path!==p?f.findIndex(tt.bind(null,a[u-2])):d}),o=_e(()=>i.value>-1&&Nl(n.params,r.value.params)),l=_e(()=>i.value>-1&&i.value===n.matched.length-1&&xi(n.params,r.value.params));function s(a={}){if(Al(a)){const u=t[ne(e.replace)?"replace":"push"](ne(e.to)).catch(vt);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:r,href:_e(()=>r.value.href),isActive:o,isExactActive:l,navigate:s}}function Pl(e){return e.length===1?e[0]:e}const Vl=fi({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:kr,setup(e,{slots:t}){const n=ze(kr(e)),{options:r}=je(cn),i=_e(()=>({[Cr(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[Cr(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&Pl(t.default(n));return e.custom?o:di("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:i.value},o)}}}),Ml=Vl;function Al(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Nl(e,t){for(const n in t){const r=t[n],i=e[n];if(typeof r=="string"){if(r!==i)return!1}else if(!ke(i)||i.length!==r.length||r.some((o,l)=>o!==i[l]))return!1}return!0}function Rr(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Cr=(e,t,n)=>e??t??n,Il=fi({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=je(Cn),i=_e(()=>e.route||r.value),o=je(Sr,0),l=_e(()=>{let u=ne(o);const{matched:c}=i.value;let f;for(;(f=c[u])&&!f.components;)u++;return u}),s=_e(()=>i.value.matched[l.value]);mn(Sr,_e(()=>l.value+1)),mn($l,s),mn(Cn,i);const a=z();return Qn(()=>[a.value,s.value,e.name],([u,c,f],[d,p,m])=>{c&&(c.instances[f]=u,p&&p!==c&&u&&u===d&&(c.leaveGuards.size||(c.leaveGuards=p.leaveGuards),c.updateGuards.size||(c.updateGuards=p.updateGuards))),u&&c&&(!p||!tt(c,p)||!d)&&(c.enterCallbacks[f]||[]).forEach(k=>k(u))},{flush:"post"}),()=>{const u=i.value,c=e.name,f=s.value,d=f&&f.components[c];if(!d)return $r(n.default,{Component:d,route:u});const p=f.props[c],m=p?p===!0?u.params:typeof p=="function"?p(u):p:null,C=di(d,J({},m,t,{onVnodeUnmounted:A=>{A.component.isUnmounted&&(f.instances[c]=null)},ref:a}));return $r(n.default,{Component:C,route:u})||C}}});function $r(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Tl=Il;function Ul(e){const t=xl(e.routes,e),n=e.parseQuery||Rl,r=e.stringifyQuery||Er,i=e.history,o=ut(),l=ut(),s=ut(),a=yo(Ue);let u=Ue;We&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=_n.bind(null,R=>""+R),f=_n.bind(null,Ko),d=_n.bind(null,wt);function p(R,U){let I,q;return Ei(R)?(I=t.getRecordMatcher(R),q=U):q=R,t.addRoute(q,I)}function m(R){const U=t.getRecordMatcher(R);U&&t.removeRoute(U)}function k(){return t.getRoutes().map(R=>R.record)}function C(R){return!!t.getRecordMatcher(R)}function A(R,U){if(U=J({},U||a.value),typeof R=="string"){const X=gn(n,R,U.path),ue=t.resolve({path:X.path},U),st=i.createHref(X.fullPath);return J(X,ue,{params:d(ue.params),hash:wt(X.hash),redirectedFrom:void 0,href:st})}let I;if(R.path!=null)I=J({},R,{path:gn(n,R.path,U.path).path});else{const X=J({},R.params);for(const ue in X)X[ue]==null&&delete X[ue];I=J({},R,{params:f(X)}),U.params=f(U.params)}const q=t.resolve(I,U),ee=R.hash||"";q.params=c(d(q.params));const ae=Zo(r,J({},R,{hash:Xo(ee),path:q.path})),Y=i.createHref(ae);return J({fullPath:ae,hash:ee,query:r===Er?Cl(R.query):R.query||{}},q,{redirectedFrom:void 0,href:Y})}function P(R){return typeof R=="string"?gn(n,R,a.value.path):J({},R)}function S(R,U){if(u!==R)return nt(8,{from:U,to:R})}function E(R){return x(R)}function M(R){return E(J(P(R),{replace:!0}))}function V(R){const U=R.matched[R.matched.length-1];if(U&&U.redirect){const{redirect:I}=U;let q=typeof I=="function"?I(R):I;return typeof q=="string"&&(q=q.includes("?")||q.includes("#")?q=P(q):{path:q},q.params={}),J({query:R.query,hash:R.hash,params:q.path!=null?{}:R.params},q)}}function x(R,U){const I=u=A(R),q=a.value,ee=R.state,ae=R.force,Y=R.replace===!0,X=V(I);if(X)return x(J(P(X),{state:typeof X=="object"?J({},ee,X.state):ee,force:ae,replace:Y}),U||I);const ue=I;ue.redirectedFrom=U;let st;return!ae&&Jo(r,q,I)&&(st=nt(16,{to:ue,from:q}),B(q,q,!0,!1)),(st?Promise.resolve(st):v(ue,q)).catch(me=>Ae(me)?Ae(me,2)?me:$(me):Z(me,ue,q)).then(me=>{if(me){if(Ae(me,2))return x(J({replace:Y},P(me.to),{state:typeof me.to=="object"?J({},ee,me.to.state):ee,force:ae}),U||ue)}else me=T(ue,q,!0,Y,ee);return w(ue,q,me),me})}function g(R,U){const I=S(R,U);return I?Promise.reject(I):Promise.resolve()}function _(R){const U=H.values().next().value;return U&&typeof U.runWithContext=="function"?U.runWithContext(R):R()}function v(R,U){let I;const[q,ee,ae]=Dl(R,U);I=vn(q.reverse(),"beforeRouteLeave",R,U);for(const X of q)X.leaveGuards.forEach(ue=>{I.push(De(ue,R,U))});const Y=g.bind(null,R,U);return I.push(Y),Ke(I).then(()=>{I=[];for(const X of o.list())I.push(De(X,R,U));return I.push(Y),Ke(I)}).then(()=>{I=vn(ee,"beforeRouteUpdate",R,U);for(const X of ee)X.updateGuards.forEach(ue=>{I.push(De(ue,R,U))});return I.push(Y),Ke(I)}).then(()=>{I=[];for(const X of ae)if(X.beforeEnter)if(ke(X.beforeEnter))for(const ue of X.beforeEnter)I.push(De(ue,R,U));else I.push(De(X.beforeEnter,R,U));return I.push(Y),Ke(I)}).then(()=>(R.matched.forEach(X=>X.enterCallbacks={}),I=vn(ae,"beforeRouteEnter",R,U,_),I.push(Y),Ke(I))).then(()=>{I=[];for(const X of l.list())I.push(De(X,R,U));return I.push(Y),Ke(I)}).catch(X=>Ae(X,8)?X:Promise.reject(X))}function w(R,U,I){s.list().forEach(q=>_(()=>q(R,U,I)))}function T(R,U,I,q,ee){const ae=S(R,U);if(ae)return ae;const Y=U===Ue,X=We?history.state:{};I&&(q||Y?i.replace(R.fullPath,J({scroll:Y&&X&&X.scroll},ee)):i.push(R.fullPath,ee)),a.value=R,B(R,U,I,Y),$()}let N;function D(){N||(N=i.listen((R,U,I)=>{if(!pe.listening)return;const q=A(R),ee=V(q);if(ee){x(J(ee,{replace:!0,force:!0}),q).catch(vt);return}u=q;const ae=a.value;We&&ll(hr(ae.fullPath,I.delta),un()),v(q,ae).catch(Y=>Ae(Y,12)?Y:Ae(Y,2)?(x(J(P(Y.to),{force:!0}),q).then(X=>{Ae(X,20)&&!I.delta&&I.type===xt.pop&&i.go(-1,!1)}).catch(vt),Promise.reject()):(I.delta&&i.go(-I.delta,!1),Z(Y,q,ae))).then(Y=>{Y=Y||T(q,ae,!1),Y&&(I.delta&&!Ae(Y,8)?i.go(-I.delta,!1):I.type===xt.pop&&Ae(Y,20)&&i.go(-1,!1)),w(q,ae,Y)}).catch(vt)}))}let b=ut(),F=ut(),G;function Z(R,U,I){$(R);const q=F.list();return q.length?q.forEach(ee=>ee(R,U,I)):console.error(R),Promise.reject(R)}function j(){return G&&a.value!==Ue?Promise.resolve():new Promise((R,U)=>{b.add([R,U])})}function $(R){return G||(G=!R,D(),b.list().forEach(([U,I])=>R?I(R):U()),b.reset()),R}function B(R,U,I,q){const{scrollBehavior:ee}=e;if(!We||!ee)return Promise.resolve();const ae=!I&&al(hr(R.fullPath,0))||(q||!I)&&history.state&&history.state.scroll||null;return Xt().then(()=>ee(R,U,ae)).then(Y=>Y&&ol(Y)).catch(Y=>Z(Y,R,U))}const he=R=>i.go(R);let de;const H=new Set,pe={currentRoute:a,listening:!0,addRoute:p,removeRoute:m,clearRoutes:t.clearRoutes,hasRoute:C,getRoutes:k,resolve:A,options:e,push:E,replace:M,go:he,back:()=>he(-1),forward:()=>he(1),beforeEach:o.add,beforeResolve:l.add,afterEach:s.add,onError:F.add,isReady:j,install(R){const U=this;R.component("RouterLink",Ml),R.component("RouterView",Tl),R.config.globalProperties.$router=U,Object.defineProperty(R.config.globalProperties,"$route",{enumerable:!0,get:()=>ne(a)}),We&&!de&&a.value===Ue&&(de=!0,E(i.location).catch(ee=>{}));const I={};for(const ee in Ue)Object.defineProperty(I,ee,{get:()=>a.value[ee],enumerable:!0});R.provide(cn,U),R.provide(Ci,wo(I)),R.provide(Cn,a);const q=R.unmount;H.add(R),R.unmount=function(){H.delete(R),H.size<1&&(u=Ue,N&&N(),N=null,a.value=Ue,de=!1,G=!1),q()}}};function Ke(R){return R.reduce((U,I)=>U.then(()=>_(I)),Promise.resolve())}return pe}function Dl(e,t){const n=[],r=[],i=[],o=Math.max(t.matched.length,e.matched.length);for(let l=0;l<o;l++){const s=t.matched[l];s&&(e.matched.find(u=>tt(u,s))?r.push(s):n.push(s));const a=e.matched[l];a&&(t.matched.find(u=>tt(u,a))||i.push(a))}return[n,r,i]}function Ol(){return je(cn)}const Ll={class:"app-container"},Fl={class:"grid-container"},ql={class:"header"},zl={class:"header-left"},Hl={class:"user-info"},Bl={class:"el-dropdown-link"},Xl={class:"user-name"},Gl={class:"sidebar"},Yl={class:"content",ref:"contentRef"},Kl={__name:"App",setup(e){const t=Ol(),n=z(""),r=z([]),i=z(!1),o=z(""),l=z(null);at(async()=>{await s(),await a(),t.push("/unit-management")});const s=async()=>{const V=await fe.post("/api/get_user_info.php");n.value=V.user.name},a=async()=>{const V=await fe.post("/api/get_user_app.php");r.value=V.data},u=V=>{console.log("点击的菜单对应的路由是:",V),t.push(V)},c=()=>{i.value=!i.value},f=z(!1),d=z({oldPassword:"",newPassword:"",confirmPassword:""}),p=z(null),m=z(!1),k=z(!1),C=()=>{m.value=!m.value},A=()=>{k.value=!k.value},P=async V=>{V==="logout"?(await fe.get("/api/logout.php"),window.location.href="login.html"):V==="changePassword"&&(f.value=!0)},S=async()=>{p.value&&await p.value.validate(V=>{if(V){if(d.value.newPassword!==d.value.confirmPassword){Q.error("两次输入的密码不一致");return}const x=new FormData;x.append("old_password",d.value.oldPassword),x.append("new_password",d.value.newPassword),fe.post("/api/change_password.php",x).then(()=>{Q.success("密码修改成功，请重新登录"),f.value=!1,d.value={oldPassword:"",newPassword:"",confirmPassword:""},setTimeout(()=>{window.location.href="login.html"},3e3)}).catch(()=>{Q.error("密码修改失败")})}})},E=ze({oldPassword:[{required:!0,message:"请输入旧密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"}],confirmPassword:[{required:!0,message:"请输入确认新密码",trigger:"blur"},{validator:(V,x,g)=>{x!==d.value.newPassword?g(new Error("两次输入的密码不一致")):g()},trigger:"blur"}]}),M=()=>{const V=l.value;V&&(document.fullscreenElement?document.exitFullscreen():V.requestFullscreen().catch(x=>{console.error("全屏失败:",x),Q.error("全屏功能不支持")}))};return(V,x)=>{const g=O("el-button"),_=O("el-icon"),v=O("el-avatar"),w=O("el-dropdown-item"),T=O("el-dropdown-menu"),N=O("el-dropdown"),D=O("el-menu-item"),b=O("el-menu"),F=O("router-view"),G=O("el-input"),Z=O("el-form-item"),j=O("el-form"),$=O("el-dialog");return re(),ce(He,null,[L("div",Ll,[L("div",Fl,[L("div",ql,[L("div",zl,[h(g,{onClick:c,type:"text",class:"menu-btn"},{default:y(()=>x[5]||(x[5]=[L("i",{class:"el-icon-menu"},null,-1)])),_:1,__:[5]}),x[6]||(x[6]=L("img",{src:xo,alt:"Logo",class:"header-logo"},null,-1)),x[7]||(x[7]=L("span",{class:"logo"},"CloudPivot",-1))])]),L("div",Hl,[h(N,{onCommand:P},{dropdown:y(()=>[h(T,null,{default:y(()=>[h(w,{command:"profile"},{default:y(()=>x[8]||(x[8]=[W("个人信息")])),_:1,__:[8]}),h(w,{command:"changePassword"},{default:y(()=>x[9]||(x[9]=[W("修改密码")])),_:1,__:[9]}),h(w,{command:"logout"},{default:y(()=>x[10]||(x[10]=[W("退出登录")])),_:1,__:[10]})]),_:1})]),default:y(()=>[L("span",Bl,[h(v,{size:"small"},{default:y(()=>[h(_,null,{default:y(()=>[h(ne(bo),{style:{color:"#409EFF"}})]),_:1})]),_:1}),L("span",Xl,Pe(n.value),1)])]),_:1})]),L("div",Gl,[h(b,{"default-active":o.value,class:"el-menu-vertical",mode:"vertical",collapse:i.value,onOpen:V.handleOpen,onClose:V.handleClose},{default:y(()=>[(re(!0),ce(He,null,et(r.value,B=>(re(),Fe(D,{key:B.id,index:B.id.toString(),router:B.url,onClick:he=>u(B.url)},{title:y(()=>[L("span",null,Pe(B.application_name),1)]),_:2},1032,["index","router","onClick"]))),128))]),_:1},8,["default-active","collapse","onOpen","onClose"])]),L("div",Yl,[h(g,{onClick:M,type:"text",class:"fullscreen-btn"},{default:y(()=>[h(_,null,{default:y(()=>[h(ne(Eo))]),_:1})]),_:1}),L("div",{class:"fullscreen-target",ref_key:"fullscreenTargetRef",ref:l},[h(F,{ref:"routerViewRef"},null,512)],512)],512)])]),h($,{modelValue:f.value,"onUpdate:modelValue":x[4]||(x[4]=B=>f.value=B),width:"400px"},{default:y(()=>[h(j,{model:d.value,ref_key:"passwordFormRef",ref:p,rules:E,"label-width":"120px",onSubmit:pi(S,["prevent"])},{default:y(()=>[h(Z,{label:"旧密码",prop:"oldPassword",required:""},{default:y(()=>[h(G,{modelValue:d.value.oldPassword,"onUpdate:modelValue":x[0]||(x[0]=B=>d.value.oldPassword=B),type:"password",placeholder:"请输入旧密码"},null,8,["modelValue"])]),_:1}),h(Z,{label:"新密码",prop:"newPassword",required:""},{default:y(()=>[h(G,{modelValue:d.value.newPassword,"onUpdate:modelValue":x[1]||(x[1]=B=>d.value.newPassword=B),type:m.value?"text":"password",placeholder:"请输入新密码"},{suffix:y(()=>[h(g,{icon:m.value?ne(cr):ne(fr),onClick:C,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),h(Z,{label:"确认新密码",prop:"confirmPassword",required:""},{default:y(()=>[h(G,{modelValue:d.value.confirmPassword,"onUpdate:modelValue":x[2]||(x[2]=B=>d.value.confirmPassword=B),type:k.value?"text":"password",placeholder:"请输入确认新密码"},{suffix:y(()=>[h(g,{icon:k.value?ne(cr):ne(fr),onClick:A,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),h(Z,null,{default:y(()=>[h(g,{type:"primary","native-type":"submit"},{default:y(()=>x[11]||(x[11]=[W("确定")])),_:1,__:[11]}),h(g,{onClick:x[3]||(x[3]=B=>f.value=!1)},{default:y(()=>x[12]||(x[12]=[W("取消")])),_:1,__:[12]})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])],64)}}},Wl=sn(Kl,[["__scopeId","data-v-727c4254"]]),Ql={class:"unit-management"},Zl={style:{"text-align":"right",margin:"10px"}},Jl={__name:"UnitManagement",setup(e){const t=z(!1),n={expandTrigger:"hover",checkStrictly:!0,value:"id",label:"unit_name",children:"children"},r=_=>{if(_&&_.length>0){const v=_[_.length-1],w=o(v);w&&(i.parentId=v,f.value=w.children||[])}else i.parentId=null,f.value=[]},i=ze({id:null,name:"",parentId:null,parentIdPath:[],code:"",sort:"top"});Qn(()=>i.parentId,(_,v)=>{if(console.log("parentId 发生变化，旧值: ",v,"新值: ",_),_){const w=o(_);w&&(f.value=w.children||[])}else f.value=[]},{immediate:!1});const o=(_,v=l.value)=>{for(let w=0;w<v.length;w++){if(v[w].id===_)return v[w];if(v[w].children){const T=o(_,v[w].children);if(T)return T}}return null},l=z([]),s=z(new Set),a=_e(()=>{const _=[],v=(w,T=0,N=null)=>{w.forEach(D=>{_.push({...D,level:T,parentId:N,expanded:s.value.has(D.id)}),D.children&&v(D.children,T+1,D.id)})};return v(l.value),_}),u=_e(()=>{const _=[],v=w=>{if(w.level===0)return!0;let T=a.value.find(N=>N.id===w.parentId);for(;T;){if(!T.expanded)return!1;T=a.value.find(N=>N.id===T.parentId)}return!0};return a.value.forEach(w=>{v(w)&&_.push(w)}),_}),c=z(!1),f=z([]),d=z(null);at(async()=>{await P()});const p=z(!1),m=_=>{const v=[];function w(T,N){for(const D of T){const b=[...N,D.id];if(D.id===_)return v.push(...b),!0;if(D.children&&D.children.length>0&&w(D.children,b))return!0}return!1}return w(l.value,[]),v},k=_=>{if(d.value&&d.value.resetFields(),t.value=!1,i.id=null,i.name="",i.code="",i.parentId=null,i.parentIdPath=[],i.sort="top",p.value=!!_,_){const v=o(_);v&&(i.parentId=_,console.log("parentId位置一:",_),i.parentIdPath=m(_),f.value=v.children||[])}else i.parentId=null,i.parentIdPath=[],f.value=[];c.value=!0,console.log("dialogVisible 已设为 true")},C=_=>{d.value&&d.value.resetFields(),t.value=!0,i.id=_.id,i.name=_.unit_name,i.code=_.code,i.parentId=_.parent_id,i.parentIdPath=m(_.parent_id);const v=o(_.parent_id);v?f.value=v.children||[]:f.value=l.value,f.value.some(w=>w.id===_.sort_after_id)?i.sort=_.sort_after_id:i.sort="top",console.log("formData.sort:",i.sort),c.value=!0},A=()=>{d.value.validate(async _=>{if(_)try{let v=0;i.sort!=="top"&&(v=i.sort);const w=new FormData;i.id?(w.append("action","edit"),w.append("id",i.id)):w.append("action","add"),w.append("unit_name",i.name),w.append("code",i.code),console.log("formData.parentId:",i.parentId),w.append("parent_id",i.parentId||null),w.append("after_unit_id",v);const T=await fe.post("api/unit_manage.php",w,{headers:{"Content-Type":"multipart/form-data"}});T.status===1?(await P(),c.value=!1,Q.success(i.id?"编辑成功":"新增成功")):Q.error(T.message)}catch(v){console.error("保存单位失败:",v),Q.error("保存单位失败，请稍后重试")}})},P=async()=>{const _=await fe.post("api/get_unit_info.php");l.value=[_.data],console.log("获取单位数据成功:",l.value);const v=w=>{w.forEach(T=>{T.children&&T.children.length>0&&(s.value.add(T.id),v(T.children))})};v(l.value)},S=_=>{Co.confirm(`确定要删除 ${_.unit_name} 吗？`,"Warning",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const v=new FormData;v.append("action","del"),v.append("id",_.id),await fe.post("api/unit_manage.php",v),Q.success("删除成功"),await P()})},E=_=>{const v=new Set(s.value);v.has(_.id)?v.delete(_.id):v.add(_.id),s.value=v},M=_=>((_.parentId?o(_.parentId):{children:l.value}).children||[]).findIndex(N=>N.id===_.id)===0,V=_=>{const w=(_.parentId?o(_.parentId):{children:l.value}).children||[];return w.findIndex(N=>N.id===_.id)===w.length-1},x=async _=>{const v=new FormData;v.append("action","edit"),v.append("id",_.id),v.append("sort_order",_.sort_order-1),v.append("unit_name",_.unit_name),v.append("code",_.code),v.append("parent_id",_.parentId),await fe.post("api/unit_manage.php",v),await P()},g=async _=>{const v=new FormData;v.append("action","edit"),v.append("id",_.id),v.append("sort_order",_.sort_order+1),v.append("unit_name",_.unit_name),v.append("code",_.code),v.append("parent_id",_.parentId),await fe.post("api/unit_manage.php",v),await P()};return(_,v)=>{const w=O("el-button"),T=O("el-col"),N=O("el-row"),D=O("el-table-column"),b=O("el-icon"),F=O("el-button-group"),G=O("el-table"),Z=O("el-input"),j=O("el-form-item"),$=O("el-cascader"),B=O("el-option"),he=O("el-select"),de=O("el-form");return re(),ce("div",Ql,[h(N,null,{default:y(()=>[h(T,{span:24},{default:y(()=>[L("div",Zl,[h(w,{type:"primary",round:"",onClick:v[0]||(v[0]=H=>k(null))},{default:y(()=>v[7]||(v[7]=[W("添加单位")])),_:1,__:[7]})])]),_:1})]),_:1}),h(G,{data:u.value,style:{width:"100%"},border:""},{default:y(()=>[h(D,{label:"操作",width:"60",align:"center"},{default:y(H=>[H.row.children&&H.row.children.length?(re(),Fe(w,{key:0,size:"mini",type:"text",onClick:pe=>E(H.row)},{default:y(()=>[W(Pe(H.row.expanded?"-":"+"),1)]),_:2},1032,["onClick"])):Gt("",!0)]),_:1}),h(D,{prop:"unit_name",label:"单位名称"},{default:y(H=>[L("span",{style:Zn({paddingLeft:`${H.row.level*20}px`})},Pe(H.row.unit_name),5)]),_:1}),h(D,{prop:"code",label:"单位编号",width:"250"}),h(D,{label:"操作",width:"350"},{default:y(H=>[h(F,null,{default:y(()=>[h(w,{size:"mini",type:"primary",onClick:pe=>k(H.row.id)},{default:y(()=>[h(b,null,{default:y(()=>[h(ne(hi))]),_:1})]),_:2},1032,["onClick"]),h(w,{size:"mini",type:"warning",onClick:pe=>C(H.row)},{default:y(()=>[h(b,null,{default:y(()=>[h(ne(Jn))]),_:1})]),_:2},1032,["onClick"]),h(w,{size:"mini",type:"danger",onClick:pe=>S(H.row)},{default:y(()=>[h(b,null,{default:y(()=>[h(ne(jn))]),_:1})]),_:2},1032,["onClick"]),h(w,{size:"mini",type:"info",onClick:pe=>x(H.row),disabled:M(H.row)},{default:y(()=>[h(b,null,{default:y(()=>[h(ne(ko))]),_:1})]),_:2},1032,["onClick","disabled"]),h(w,{size:"mini",type:"info",onClick:pe=>g(H.row),disabled:V(H.row)},{default:y(()=>[h(b,null,{default:y(()=>[h(ne(Ro))]),_:1})]),_:2},1032,["onClick","disabled"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),h(ne(So),{modelValue:c.value,"onUpdate:modelValue":v[6]||(v[6]=H=>c.value=H),title:"",width:"450px","close-on-click-modal":!1},{footer:y(()=>[h(w,{onClick:v[5]||(v[5]=H=>c.value=!1)},{default:y(()=>v[8]||(v[8]=[W("取消")])),_:1,__:[8]}),h(w,{type:"primary",onClick:A},{default:y(()=>v[9]||(v[9]=[W("确定")])),_:1,__:[9]})]),default:y(()=>[h(de,{model:i,ref_key:"formRef",ref:d,"label-width":"130px"},{default:y(()=>[h(j,{label:"单位名称",prop:"name",required:""},{default:y(()=>[h(Z,{modelValue:i.name,"onUpdate:modelValue":v[1]||(v[1]=H=>i.name=H),required:""},null,8,["modelValue"])]),_:1}),h(j,{label:"单位编码",prop:"code",required:""},{default:y(()=>[h(Z,{modelValue:i.code,"onUpdate:modelValue":v[2]||(v[2]=H=>i.code=H),required:""},null,8,["modelValue"])]),_:1}),h(j,{label:"上级单位",prop:"parentId"},{default:y(()=>[h($,{modelValue:i.parentIdPath,"onUpdate:modelValue":v[3]||(v[3]=H=>i.parentIdPath=H),options:l.value,props:n,onChange:r,style:{width:"100%"},"show-all-levels":!1,placeholder:"请选择上级单位"},null,8,["modelValue","options"])]),_:1}),t.value?Gt("",!0):(re(),Fe(j,{key:0,label:"排序",prop:"sort",required:""},{default:y(()=>[h(he,{modelValue:i.sort,"onUpdate:modelValue":v[4]||(v[4]=H=>i.sort=H),placeholder:"请选择排序位置"},{default:y(()=>[h(B,{label:"置于 最前",value:"top"}),(re(!0),ce(He,null,et(f.value,H=>(re(),Fe(B,{key:H.id,label:`置于 ${H.unit_name} 之后`,value:H.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}))]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},jl={class:"user-management-container"},ea={class:"left-panel"},ta=["onClick"],na={class:"right-panel"},ra={class:"action-buttons"},ia={class:"form-row"},oa={class:"form-row"},la={class:"form-row"},aa={class:"form-row"},sa={class:"form-row"},ua={class:"form-row"},ca={class:"form-row"},fa={class:"form-row"},da={class:"dialog-footer"},pa={__name:"UserManagement",setup(e){const t=z([]),n=z([]),r=z(""),i=z([]),o=z([]),l=z(!1),s=_e(()=>i.value),a=_e(()=>r.value?n.value.filter(x=>x.show&&x.unit_name.toLowerCase().includes(r.value.toLowerCase())):n.value.filter(x=>x.show)),u=ze({name:"",id_number:"",phone:"",archive_birthdate:null,gender:null,short_code:"",alt_phone_1:"",alt_phone_2:"",landline:"",organization_unit:null,work_unit:null,employment_date:null,political_status:"",party_join_date:null,personnel_type:"",police_number:"",is_assisting_officer:null,employment_status:"",job_rank:"",current_rank_date:null,position:"",current_position_date:null,sorted_order:null,remark:""}),c={name:[{required:!0,message:"",trigger:"blur"}],id_number:[{required:!1,message:"",trigger:"blur"},{pattern:/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,message:"请输入正确的身份证号",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],archive_birthdate:[{required:!0,message:"请选择档案出生时间",trigger:"change"}]},f=z(null);at(async()=>{try{const x=await fe.post("api/get_unit_info.php");x.status===1?(t.value=[x.data],d(x.data),console.log("获取部门数据成功:",t.value)):Q.error(x.message)}catch(x){console.error("获取部门数据失败:",x),Q.error("获取部门数据失败，请稍后重试")}});const d=(x,g=0,_=null)=>{const v={...x,level:g,expanded:x.children&&x.children.length>0,parent:_,indent:g*20,show:!0};n.value.push(v),x.children&&x.children.length>0&&x.children.forEach(w=>{d(w,g+1,v)})},p=x=>{x.expanded=!x.expanded;const g=n.value.indexOf(x)+1;let _=x.level+1;for(let v=g;v<n.value.length;v++){const w=n.value[v];if(w.level<=x.level)break;w.level===_?w.show=x.expanded:w.level>_&&(w.show=x.expanded&&n.value[v-1].show)}},m=async x=>{console.log("点击的部门:",x)},k=()=>{l.value=!0,f.value&&f.value.resetFields()},C=()=>{if(o.value.length===0){Q.warning("请先选择要删除的用户");return}Q.success(`已删除 ${o.value.length} 个用户`),o.value=[]},A=x=>{console.log("编辑用户:",x)},P=x=>{console.log("查看用户详情:",x)},S=x=>{console.log("删除用户:",x)},E=x=>{o.value=x},M=x=>{console.log("点击的部门名称:",x)},V=async()=>{f.value&&await f.value.validate(async x=>{if(x)try{console.log("提交的用户数据:",u),Q.success("模拟新增用户成功"),l.value=!1}catch(g){console.error("新增用户失败:",g),Q.error("新增用户失败，请稍后重试")}})};return(x,g)=>{const _=O("el-input"),v=O("el-button"),w=O("el-table-column"),T=O("el-table"),N=O("el-form-item"),D=O("el-date-picker"),b=O("el-option"),F=O("el-select"),G=O("el-cascader"),Z=O("el-form"),j=O("el-dialog");return re(),ce("div",jl,[L("div",ea,[h(_,{modelValue:r.value,"onUpdate:modelValue":g[0]||(g[0]=$=>r.value=$),placeholder:"搜索单位",class:"department-search"},null,8,["modelValue"]),h(T,{data:a.value,border:"",class:"department-table",onRowClick:m},{default:y(()=>[h(w,{label:"操作",width:"60"},{default:y(({row:$})=>[$.children&&$.children.length>0?(re(),Fe(v,{key:0,type:"text",size:"small",onClick:pi(B=>p($),["stop"])},{default:y(()=>[W(Pe($.expanded?"-":"+"),1)]),_:2},1032,["onClick"])):Gt("",!0)]),_:1}),h(w,{prop:"unit_name",label:"单位名称"},{default:y(({row:$})=>[L("span",{class:"indent",style:Zn({width:`${$.indent}px`})},null,4),L("span",{onClick:B=>M($),style:{cursor:"pointer"}},Pe($.unit_name),9,ta)]),_:1})]),_:1},8,["data"])]),L("div",na,[L("div",ra,[h(v,{type:"primary",onClick:k},{default:y(()=>g[27]||(g[27]=[W("新增")])),_:1,__:[27]}),h(v,{type:"danger",onClick:C},{default:y(()=>g[28]||(g[28]=[W("删除")])),_:1,__:[28]})]),h(T,{data:s.value,border:"",class:"user-table",onSelectionChange:E},{default:y(()=>[h(w,{type:"selection",width:"55"}),h(w,{prop:"name",label:"姓名"}),h(w,{prop:"id_number",label:"身份证号",width:"150"}),h(w,{prop:"phone",label:"手机号码"}),h(w,{prop:"gender",label:"性别"}),h(w,{prop:"short_code",label:"短号"}),h(w,{prop:"organization_unit",label:"编制单位"}),h(w,{prop:"work_unit",label:"工作单位"}),h(w,{prop:"personnel_type",label:"人员身份"}),h(w,{prop:"employment_status",label:"人员状态"}),h(w,{prop:"remark",label:"备注"}),h(w,{label:"操作",width:"200"},{default:y(({row:$})=>[h(v,{type:"primary",onClick:B=>A($)},{default:y(()=>g[29]||(g[29]=[W("编辑")])),_:2,__:[29]},1032,["onClick"]),h(v,{type:"success",onClick:B=>P($)},{default:y(()=>g[30]||(g[30]=[W("详情")])),_:2,__:[30]},1032,["onClick"]),h(v,{type:"danger",onClick:B=>S($)},{default:y(()=>g[31]||(g[31]=[W("删除")])),_:2,__:[31]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),h(j,{modelValue:l.value,"onUpdate:modelValue":g[26]||(g[26]=$=>l.value=$),title:"",width:"1000px"},{footer:y(()=>[L("span",da,[h(v,{onClick:g[25]||(g[25]=$=>l.value=!1)},{default:y(()=>g[33]||(g[33]=[W("取消")])),_:1,__:[33]}),h(v,{type:"primary",onClick:V},{default:y(()=>g[34]||(g[34]=[W("确定")])),_:1,__:[34]})])]),default:y(()=>[h(Z,{model:u,rules:c,ref_key:"newUserFormRef",ref:f,"label-width":"120px",inline:!1,class:"user-form"},{default:y(()=>[L("div",ia,[h(N,{label:"姓名",prop:"name",style:{flex:"1"}},{default:y(()=>[h(_,{modelValue:u.name,"onUpdate:modelValue":g[1]||(g[1]=$=>u.name=$)},null,8,["modelValue"])]),_:1}),h(N,{label:"身份证号",prop:"id_number",style:{flex:"1"}},{default:y(()=>[h(_,{modelValue:u.id_number,"onUpdate:modelValue":g[2]||(g[2]=$=>u.id_number=$),maxlength:"18"},null,8,["modelValue"])]),_:1}),h(N,{label:"手机号码",prop:"phone",style:{flex:"1"}},{default:y(()=>[h(_,{modelValue:u.phone,"onUpdate:modelValue":g[3]||(g[3]=$=>u.phone=$),maxlength:"11"},null,8,["modelValue"])]),_:1})]),L("div",oa,[h(N,{label:"档案出生日期",prop:"archive_birthdate",style:{flex:"1"}},{default:y(()=>[h(D,{modelValue:u.archive_birthdate,"onUpdate:modelValue":g[4]||(g[4]=$=>u.archive_birthdate=$),type:"date"},null,8,["modelValue"])]),_:1}),h(N,{label:"性别",prop:"gender",style:{flex:"1"}},{default:y(()=>[h(F,{modelValue:u.gender,"onUpdate:modelValue":g[5]||(g[5]=$=>u.gender=$)},{default:y(()=>[h(b,{label:"未知",value:"0"}),h(b,{label:"男",value:"1"}),h(b,{label:"女",value:"2"})]),_:1},8,["modelValue"])]),_:1}),h(N,{label:"备注",prop:"remark",style:{flex:"1"}},{default:y(()=>[h(_,{modelValue:u.remark,"onUpdate:modelValue":g[6]||(g[6]=$=>u.remark=$)},null,8,["modelValue"])]),_:1})]),L("div",la,[h(N,{label:"短号",prop:"short_code",style:{flex:"1"}},{default:y(()=>[h(_,{modelValue:u.short_code,"onUpdate:modelValue":g[7]||(g[7]=$=>u.short_code=$)},null,8,["modelValue"]),g[32]||(g[32]=L("div",{class:""},null,-1))]),_:1,__:[32]}),h(N,{label:"手机号码2",prop:"alt_phone_1",style:{flex:"1"}},{default:y(()=>[h(_,{modelValue:u.alt_phone_1,"onUpdate:modelValue":g[8]||(g[8]=$=>u.alt_phone_1=$)},null,8,["modelValue"])]),_:1}),h(N,{label:"手机号码3",prop:"alt_phone_2",style:{flex:"1"}},{default:y(()=>[h(_,{modelValue:u.alt_phone_2,"onUpdate:modelValue":g[9]||(g[9]=$=>u.alt_phone_2=$)},null,8,["modelValue"])]),_:1})]),L("div",aa,[h(N,{label:"座机",prop:"landline",style:{flex:"1"}},{default:y(()=>[h(_,{modelValue:u.landline,"onUpdate:modelValue":g[10]||(g[10]=$=>u.landline=$)},null,8,["modelValue"])]),_:1}),h(N,{label:"编制单位",prop:"organization_unit",style:{flex:"1"},rules:[{required:!0,message:"请选择编制单位",trigger:"change"}]},{default:y(()=>[h(G,{modelValue:u.organization_unit,"onUpdate:modelValue":g[11]||(g[11]=$=>u.organization_unit=$),options:x.organizationOptions,props:{expandTrigger:"hover"},placeholder:"请选择编制单位"},null,8,["modelValue","options"])]),_:1}),h(N,{label:"工作单位",prop:"work_unit",style:{flex:"1"},rules:[{required:!0,message:"请选择工作单位",trigger:"change"}]},{default:y(()=>[h(G,{modelValue:u.work_unit,"onUpdate:modelValue":g[12]||(g[12]=$=>u.work_unit=$),options:x.workUnitOptions,props:{expandTrigger:"hover"},placeholder:"请选择工作单位"},null,8,["modelValue","options"])]),_:1})]),L("div",sa,[h(N,{label:"参工时间",prop:"employment_date",style:{flex:"1"}},{default:y(()=>[h(D,{modelValue:u.employment_date,"onUpdate:modelValue":g[13]||(g[13]=$=>u.employment_date=$),type:"date"},null,8,["modelValue"])]),_:1}),h(N,{label:"政治面貌",prop:"political_status",style:{flex:"1"}},{default:y(()=>[h(F,{modelValue:u.political_status,"onUpdate:modelValue":g[14]||(g[14]=$=>u.political_status=$),placeholder:"请选择政治面貌"},{default:y(()=>[h(b,{label:"中共党员",value:"中共党员"}),h(b,{label:"中共预备党员",value:"中共预备党员"}),h(b,{label:"共青团员",value:"共青团员"}),h(b,{label:"民主党派",value:"民主党派"}),h(b,{label:"无党派人士",value:"无党派人士"}),h(b,{label:"群众",value:"群众"})]),_:1},8,["modelValue"])]),_:1}),h(N,{label:"加入组织时间",prop:"party_join_date",style:{flex:"1"}},{default:y(()=>[h(D,{modelValue:u.party_join_date,"onUpdate:modelValue":g[15]||(g[15]=$=>u.party_join_date=$),type:"date"},null,8,["modelValue"])]),_:1})]),L("div",ua,[h(N,{label:"人员身份",prop:"personnel_type",style:{flex:"1"}},{default:y(()=>[h(F,{modelValue:u.personnel_type,"onUpdate:modelValue":g[16]||(g[16]=$=>u.personnel_type=$)},{default:y(()=>[h(b,{label:"民警",value:"民警"}),h(b,{label:"职工",value:"职工"}),h(b,{label:"辅警",value:"辅警"}),h(b,{label:"机关工勤",value:"机关工勤"}),h(b,{label:"其他",value:"其他"})]),_:1},8,["modelValue"])]),_:1}),h(N,{label:"警号/辅警号",prop:"police_number",style:{flex:"1"}},{default:y(()=>[h(_,{modelValue:u.police_number,"onUpdate:modelValue":g[17]||(g[17]=$=>u.police_number=$)},null,8,["modelValue"])]),_:1}),h(N,{label:"带辅民警",prop:"is_assisting_officer",style:{flex:"1"}},{default:y(()=>[h(F,{modelValue:u.is_assisting_officer,"onUpdate:modelValue":g[18]||(g[18]=$=>u.is_assisting_officer=$)},{default:y(()=>[h(b,{label:"否",value:"0"}),h(b,{label:"是",value:"1"})]),_:1},8,["modelValue"])]),_:1})]),L("div",ca,[h(N,{label:"人员状态",prop:"employment_status",style:{flex:"1"}},{default:y(()=>[h(F,{modelValue:u.employment_status,"onUpdate:modelValue":g[19]||(g[19]=$=>u.employment_status=$)},{default:y(()=>[h(b,{label:"在职",value:"在职"}),h(b,{label:"调离",value:"调离"}),h(b,{label:"退休",value:"退休"}),h(b,{label:"开除",value:"开除"}),h(b,{label:"借调出局",value:"借调出局"})]),_:1},8,["modelValue"])]),_:1}),h(N,{label:"职级",prop:"job_rank",style:{flex:"1"}},{default:y(()=>[h(F,{modelValue:u.job_rank,"onUpdate:modelValue":g[20]||(g[20]=$=>u.job_rank=$)},{default:y(()=>[h(b,{label:"初级",value:"初级"}),h(b,{label:"中级",value:"中级"}),h(b,{label:"高级",value:"高级"})]),_:1},8,["modelValue"])]),_:1}),h(N,{label:"任现职级时间",prop:"current_rank_date",style:{flex:"1"}},{default:y(()=>[h(D,{modelValue:u.current_rank_date,"onUpdate:modelValue":g[21]||(g[21]=$=>u.current_rank_date=$),type:"date"},null,8,["modelValue"])]),_:1})]),L("div",fa,[h(N,{label:"职务",prop:"position",style:{flex:"1"}},{default:y(()=>[h(_,{modelValue:u.position,"onUpdate:modelValue":g[22]||(g[22]=$=>u.position=$)},null,8,["modelValue"])]),_:1}),h(N,{label:"任现职务时间",prop:"current_position_date",style:{flex:"1"}},{default:y(()=>[h(D,{modelValue:u.current_position_date,"onUpdate:modelValue":g[23]||(g[23]=$=>u.current_position_date=$),type:"date"},null,8,["modelValue"])]),_:1}),h(N,{label:"人员排序",prop:"sorted_order",style:{flex:"1"}},{default:y(()=>[h(_,{modelValue:u.sorted_order,"onUpdate:modelValue":g[24]||(g[24]=$=>u.sorted_order=$),modelModifiers:{number:!0},type:"number"},null,8,["modelValue"])]),_:1})])]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},ha=sn(pa,[["__scopeId","data-v-2d0c2652"]]);class Be{constructor(){this._partials=new Float64Array(32),this._n=0}add(t){const n=this._partials;let r=0;for(let i=0;i<this._n&&i<32;i++){const o=n[i],l=t+o,s=Math.abs(t)<Math.abs(o)?t-(l-o):o-(l-t);s&&(n[r++]=s),t=l}return n[r]=t,this._n=r+1,this}valueOf(){const t=this._partials;let n=this._n,r,i,o,l=0;if(n>0){for(l=t[--n];n>0&&(r=l,i=t[--n],l=r+i,o=i-(l-r),!o););n>0&&(o<0&&t[n-1]<0||o>0&&t[n-1]>0)&&(i=o*2,r=l+i,i==r-l&&(l=r))}return l}}function*ma(e){for(const t of e)yield*t}function $i(e){return Array.from(ma(e))}var _a={value:()=>{}};function Pi(){for(var e=0,t=arguments.length,n={},r;e<t;++e){if(!(r=arguments[e]+"")||r in n||/[\s.]/.test(r))throw new Error("illegal type: "+r);n[r]=[]}return new qt(n)}function qt(e){this._=e}function ga(e,t){return e.trim().split(/^|\s+/).map(function(n){var r="",i=n.indexOf(".");if(i>=0&&(r=n.slice(i+1),n=n.slice(0,i)),n&&!t.hasOwnProperty(n))throw new Error("unknown type: "+n);return{type:n,name:r}})}qt.prototype=Pi.prototype={constructor:qt,on:function(e,t){var n=this._,r=ga(e+"",n),i,o=-1,l=r.length;if(arguments.length<2){for(;++o<l;)if((i=(e=r[o]).type)&&(i=va(n[i],e.name)))return i;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++o<l;)if(i=(e=r[o]).type)n[i]=Pr(n[i],e.name,t);else if(t==null)for(i in n)n[i]=Pr(n[i],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new qt(e)},call:function(e,t){if((i=arguments.length-2)>0)for(var n=new Array(i),r=0,i,o;r<i;++r)n[r]=arguments[r+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(o=this._[e],r=0,i=o.length;r<i;++r)o[r].value.apply(t,n)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var r=this._[e],i=0,o=r.length;i<o;++i)r[i].value.apply(t,n)}};function va(e,t){for(var n=0,r=e.length,i;n<r;++n)if((i=e[n]).name===t)return i.value}function Pr(e,t,n){for(var r=0,i=e.length;r<i;++r)if(e[r].name===t){e[r]=_a,e=e.slice(0,r).concat(e.slice(r+1));break}return n!=null&&e.push({name:t,value:n}),e}var $n="http://www.w3.org/1999/xhtml";const Vr={svg:"http://www.w3.org/2000/svg",xhtml:$n,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function fn(e){var t=e+="",n=t.indexOf(":");return n>=0&&(t=e.slice(0,n))!=="xmlns"&&(e=e.slice(n+1)),Vr.hasOwnProperty(t)?{space:Vr[t],local:e}:e}function ya(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===$n&&t.documentElement.namespaceURI===$n?t.createElement(e):t.createElementNS(n,e)}}function wa(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function Vi(e){var t=fn(e);return(t.local?wa:ya)(t)}function xa(){}function tr(e){return e==null?xa:function(){return this.querySelector(e)}}function ba(e){typeof e!="function"&&(e=tr(e));for(var t=this._groups,n=t.length,r=new Array(n),i=0;i<n;++i)for(var o=t[i],l=o.length,s=r[i]=new Array(l),a,u,c=0;c<l;++c)(a=o[c])&&(u=e.call(a,a.__data__,c,o))&&("__data__"in a&&(u.__data__=a.__data__),s[c]=u);return new we(r,this._parents)}function Ea(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function Sa(){return[]}function Mi(e){return e==null?Sa:function(){return this.querySelectorAll(e)}}function ka(e){return function(){return Ea(e.apply(this,arguments))}}function Ra(e){typeof e=="function"?e=ka(e):e=Mi(e);for(var t=this._groups,n=t.length,r=[],i=[],o=0;o<n;++o)for(var l=t[o],s=l.length,a,u=0;u<s;++u)(a=l[u])&&(r.push(e.call(a,a.__data__,u,l)),i.push(a));return new we(r,i)}function Ai(e){return function(){return this.matches(e)}}function Ni(e){return function(t){return t.matches(e)}}var Ca=Array.prototype.find;function $a(e){return function(){return Ca.call(this.children,e)}}function Pa(){return this.firstElementChild}function Va(e){return this.select(e==null?Pa:$a(typeof e=="function"?e:Ni(e)))}var Ma=Array.prototype.filter;function Aa(){return Array.from(this.children)}function Na(e){return function(){return Ma.call(this.children,e)}}function Ia(e){return this.selectAll(e==null?Aa:Na(typeof e=="function"?e:Ni(e)))}function Ta(e){typeof e!="function"&&(e=Ai(e));for(var t=this._groups,n=t.length,r=new Array(n),i=0;i<n;++i)for(var o=t[i],l=o.length,s=r[i]=[],a,u=0;u<l;++u)(a=o[u])&&e.call(a,a.__data__,u,o)&&s.push(a);return new we(r,this._parents)}function Ii(e){return new Array(e.length)}function Ua(){return new we(this._enter||this._groups.map(Ii),this._parents)}function Yt(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}Yt.prototype={constructor:Yt,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function Da(e){return function(){return e}}function Oa(e,t,n,r,i,o){for(var l=0,s,a=t.length,u=o.length;l<u;++l)(s=t[l])?(s.__data__=o[l],r[l]=s):n[l]=new Yt(e,o[l]);for(;l<a;++l)(s=t[l])&&(i[l]=s)}function La(e,t,n,r,i,o,l){var s,a,u=new Map,c=t.length,f=o.length,d=new Array(c),p;for(s=0;s<c;++s)(a=t[s])&&(d[s]=p=l.call(a,a.__data__,s,t)+"",u.has(p)?i[s]=a:u.set(p,a));for(s=0;s<f;++s)p=l.call(e,o[s],s,o)+"",(a=u.get(p))?(r[s]=a,a.__data__=o[s],u.delete(p)):n[s]=new Yt(e,o[s]);for(s=0;s<c;++s)(a=t[s])&&u.get(d[s])===a&&(i[s]=a)}function Fa(e){return e.__data__}function qa(e,t){if(!arguments.length)return Array.from(this,Fa);var n=t?La:Oa,r=this._parents,i=this._groups;typeof e!="function"&&(e=Da(e));for(var o=i.length,l=new Array(o),s=new Array(o),a=new Array(o),u=0;u<o;++u){var c=r[u],f=i[u],d=f.length,p=za(e.call(c,c&&c.__data__,u,r)),m=p.length,k=s[u]=new Array(m),C=l[u]=new Array(m),A=a[u]=new Array(d);n(c,f,k,C,A,p,t);for(var P=0,S=0,E,M;P<m;++P)if(E=k[P]){for(P>=S&&(S=P+1);!(M=C[S])&&++S<m;);E._next=M||null}}return l=new we(l,r),l._enter=s,l._exit=a,l}function za(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function Ha(){return new we(this._exit||this._groups.map(Ii),this._parents)}function Ba(e,t,n){var r=this.enter(),i=this,o=this.exit();return typeof e=="function"?(r=e(r),r&&(r=r.selection())):r=r.append(e+""),t!=null&&(i=t(i),i&&(i=i.selection())),n==null?o.remove():n(o),r&&i?r.merge(i).order():i}function Xa(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,i=n.length,o=r.length,l=Math.min(i,o),s=new Array(i),a=0;a<l;++a)for(var u=n[a],c=r[a],f=u.length,d=s[a]=new Array(f),p,m=0;m<f;++m)(p=u[m]||c[m])&&(d[m]=p);for(;a<i;++a)s[a]=n[a];return new we(s,this._parents)}function Ga(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r=e[t],i=r.length-1,o=r[i],l;--i>=0;)(l=r[i])&&(o&&l.compareDocumentPosition(o)^4&&o.parentNode.insertBefore(l,o),o=l);return this}function Ya(e){e||(e=Ka);function t(f,d){return f&&d?e(f.__data__,d.__data__):!f-!d}for(var n=this._groups,r=n.length,i=new Array(r),o=0;o<r;++o){for(var l=n[o],s=l.length,a=i[o]=new Array(s),u,c=0;c<s;++c)(u=l[c])&&(a[c]=u);a.sort(t)}return new we(i,this._parents).order()}function Ka(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function Wa(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function Qa(){return Array.from(this)}function Za(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],i=0,o=r.length;i<o;++i){var l=r[i];if(l)return l}return null}function Ja(){let e=0;for(const t of this)++e;return e}function ja(){return!this.node()}function es(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var i=t[n],o=0,l=i.length,s;o<l;++o)(s=i[o])&&e.call(s,s.__data__,o,i);return this}function ts(e){return function(){this.removeAttribute(e)}}function ns(e){return function(){this.removeAttributeNS(e.space,e.local)}}function rs(e,t){return function(){this.setAttribute(e,t)}}function is(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function os(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttribute(e):this.setAttribute(e,n)}}function ls(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function as(e,t){var n=fn(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((t==null?n.local?ns:ts:typeof t=="function"?n.local?ls:os:n.local?is:rs)(n,t))}function Ti(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function ss(e){return function(){this.style.removeProperty(e)}}function us(e,t,n){return function(){this.style.setProperty(e,t,n)}}function cs(e,t,n){return function(){var r=t.apply(this,arguments);r==null?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}function fs(e,t,n){return arguments.length>1?this.each((t==null?ss:typeof t=="function"?cs:us)(e,t,n??"")):rt(this.node(),e)}function rt(e,t){return e.style.getPropertyValue(t)||Ti(e).getComputedStyle(e,null).getPropertyValue(t)}function ds(e){return function(){delete this[e]}}function ps(e,t){return function(){this[e]=t}}function hs(e,t){return function(){var n=t.apply(this,arguments);n==null?delete this[e]:this[e]=n}}function ms(e,t){return arguments.length>1?this.each((t==null?ds:typeof t=="function"?hs:ps)(e,t)):this.node()[e]}function Ui(e){return e.trim().split(/^|\s+/)}function nr(e){return e.classList||new Di(e)}function Di(e){this._node=e,this._names=Ui(e.getAttribute("class")||"")}Di.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function Oi(e,t){for(var n=nr(e),r=-1,i=t.length;++r<i;)n.add(t[r])}function Li(e,t){for(var n=nr(e),r=-1,i=t.length;++r<i;)n.remove(t[r])}function _s(e){return function(){Oi(this,e)}}function gs(e){return function(){Li(this,e)}}function vs(e,t){return function(){(t.apply(this,arguments)?Oi:Li)(this,e)}}function ys(e,t){var n=Ui(e+"");if(arguments.length<2){for(var r=nr(this.node()),i=-1,o=n.length;++i<o;)if(!r.contains(n[i]))return!1;return!0}return this.each((typeof t=="function"?vs:t?_s:gs)(n,t))}function ws(){this.textContent=""}function xs(e){return function(){this.textContent=e}}function bs(e){return function(){var t=e.apply(this,arguments);this.textContent=t??""}}function Es(e){return arguments.length?this.each(e==null?ws:(typeof e=="function"?bs:xs)(e)):this.node().textContent}function Ss(){this.innerHTML=""}function ks(e){return function(){this.innerHTML=e}}function Rs(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t??""}}function Cs(e){return arguments.length?this.each(e==null?Ss:(typeof e=="function"?Rs:ks)(e)):this.node().innerHTML}function $s(){this.nextSibling&&this.parentNode.appendChild(this)}function Ps(){return this.each($s)}function Vs(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Ms(){return this.each(Vs)}function As(e){var t=typeof e=="function"?e:Vi(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function Ns(){return null}function Is(e,t){var n=typeof e=="function"?e:Vi(e),r=t==null?Ns:typeof t=="function"?t:tr(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})}function Ts(){var e=this.parentNode;e&&e.removeChild(this)}function Us(){return this.each(Ts)}function Ds(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Os(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Ls(e){return this.select(e?Os:Ds)}function Fs(e){return arguments.length?this.property("__data__",e):this.node().__data__}function qs(e){return function(t){e.call(this,t,this.__data__)}}function zs(e){return e.trim().split(/^|\s+/).map(function(t){var n="",r=t.indexOf(".");return r>=0&&(n=t.slice(r+1),t=t.slice(0,r)),{type:t,name:n}})}function Hs(e){return function(){var t=this.__on;if(t){for(var n=0,r=-1,i=t.length,o;n<i;++n)o=t[n],(!e.type||o.type===e.type)&&o.name===e.name?this.removeEventListener(o.type,o.listener,o.options):t[++r]=o;++r?t.length=r:delete this.__on}}}function Bs(e,t,n){return function(){var r=this.__on,i,o=qs(t);if(r){for(var l=0,s=r.length;l<s;++l)if((i=r[l]).type===e.type&&i.name===e.name){this.removeEventListener(i.type,i.listener,i.options),this.addEventListener(i.type,i.listener=o,i.options=n),i.value=t;return}}this.addEventListener(e.type,o,n),i={type:e.type,name:e.name,value:t,listener:o,options:n},r?r.push(i):this.__on=[i]}}function Xs(e,t,n){var r=zs(e+""),i,o=r.length,l;if(arguments.length<2){var s=this.node().__on;if(s){for(var a=0,u=s.length,c;a<u;++a)for(i=0,c=s[a];i<o;++i)if((l=r[i]).type===c.type&&l.name===c.name)return c.value}return}for(s=t?Bs:Hs,i=0;i<o;++i)this.each(s(r[i],t,n));return this}function Fi(e,t,n){var r=Ti(e),i=r.CustomEvent;typeof i=="function"?i=new i(t,n):(i=r.document.createEvent("Event"),n?(i.initEvent(t,n.bubbles,n.cancelable),i.detail=n.detail):i.initEvent(t,!1,!1)),e.dispatchEvent(i)}function Gs(e,t){return function(){return Fi(this,e,t)}}function Ys(e,t){return function(){return Fi(this,e,t.apply(this,arguments))}}function Ks(e,t){return this.each((typeof t=="function"?Ys:Gs)(e,t))}function*Ws(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],i=0,o=r.length,l;i<o;++i)(l=r[i])&&(yield l)}var qi=[null];function we(e,t){this._groups=e,this._parents=t}function Vt(){return new we([[document.documentElement]],qi)}function Qs(){return this}we.prototype=Vt.prototype={constructor:we,select:ba,selectAll:Ra,selectChild:Va,selectChildren:Ia,filter:Ta,data:qa,enter:Ua,exit:Ha,join:Ba,merge:Xa,selection:Qs,order:Ga,sort:Ya,call:Wa,nodes:Qa,node:Za,size:Ja,empty:ja,each:es,attr:as,style:fs,property:ms,classed:ys,text:Es,html:Cs,raise:Ps,lower:Ms,append:As,insert:Is,remove:Us,clone:Ls,datum:Fs,on:Xs,dispatch:Ks,[Symbol.iterator]:Ws};function At(e){return typeof e=="string"?new we([[document.querySelector(e)]],[document.documentElement]):new we([[e]],qi)}function rr(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function zi(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function Mt(){}var bt=.7,Kt=1/bt,Je="\\s*([+-]?\\d+)\\s*",Et="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Ve="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Zs=/^#([0-9a-f]{3,8})$/,Js=new RegExp(`^rgb\\(${Je},${Je},${Je}\\)$`),js=new RegExp(`^rgb\\(${Ve},${Ve},${Ve}\\)$`),eu=new RegExp(`^rgba\\(${Je},${Je},${Je},${Et}\\)$`),tu=new RegExp(`^rgba\\(${Ve},${Ve},${Ve},${Et}\\)$`),nu=new RegExp(`^hsl\\(${Et},${Ve},${Ve}\\)$`),ru=new RegExp(`^hsla\\(${Et},${Ve},${Ve},${Et}\\)$`),Mr={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};rr(Mt,St,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Ar,formatHex:Ar,formatHex8:iu,formatHsl:ou,formatRgb:Nr,toString:Nr});function Ar(){return this.rgb().formatHex()}function iu(){return this.rgb().formatHex8()}function ou(){return Hi(this).formatHsl()}function Nr(){return this.rgb().formatRgb()}function St(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=Zs.exec(e))?(n=t[1].length,t=parseInt(t[1],16),n===6?Ir(t):n===3?new ge(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):n===8?Nt(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):n===4?Nt(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=Js.exec(e))?new ge(t[1],t[2],t[3],1):(t=js.exec(e))?new ge(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=eu.exec(e))?Nt(t[1],t[2],t[3],t[4]):(t=tu.exec(e))?Nt(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=nu.exec(e))?Dr(t[1],t[2]/100,t[3]/100,1):(t=ru.exec(e))?Dr(t[1],t[2]/100,t[3]/100,t[4]):Mr.hasOwnProperty(e)?Ir(Mr[e]):e==="transparent"?new ge(NaN,NaN,NaN,0):null}function Ir(e){return new ge(e>>16&255,e>>8&255,e&255,1)}function Nt(e,t,n,r){return r<=0&&(e=t=n=NaN),new ge(e,t,n,r)}function lu(e){return e instanceof Mt||(e=St(e)),e?(e=e.rgb(),new ge(e.r,e.g,e.b,e.opacity)):new ge}function Pn(e,t,n,r){return arguments.length===1?lu(e):new ge(e,t,n,r??1)}function ge(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}rr(ge,Pn,zi(Mt,{brighter(e){return e=e==null?Kt:Math.pow(Kt,e),new ge(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?bt:Math.pow(bt,e),new ge(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new ge(qe(this.r),qe(this.g),qe(this.b),Wt(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Tr,formatHex:Tr,formatHex8:au,formatRgb:Ur,toString:Ur}));function Tr(){return`#${Le(this.r)}${Le(this.g)}${Le(this.b)}`}function au(){return`#${Le(this.r)}${Le(this.g)}${Le(this.b)}${Le((isNaN(this.opacity)?1:this.opacity)*255)}`}function Ur(){const e=Wt(this.opacity);return`${e===1?"rgb(":"rgba("}${qe(this.r)}, ${qe(this.g)}, ${qe(this.b)}${e===1?")":`, ${e})`}`}function Wt(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function qe(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Le(e){return e=qe(e),(e<16?"0":"")+e.toString(16)}function Dr(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new Se(e,t,n,r)}function Hi(e){if(e instanceof Se)return new Se(e.h,e.s,e.l,e.opacity);if(e instanceof Mt||(e=St(e)),!e)return new Se;if(e instanceof Se)return e;e=e.rgb();var t=e.r/255,n=e.g/255,r=e.b/255,i=Math.min(t,n,r),o=Math.max(t,n,r),l=NaN,s=o-i,a=(o+i)/2;return s?(t===o?l=(n-r)/s+(n<r)*6:n===o?l=(r-t)/s+2:l=(t-n)/s+4,s/=a<.5?o+i:2-o-i,l*=60):s=a>0&&a<1?0:l,new Se(l,s,a,e.opacity)}function su(e,t,n,r){return arguments.length===1?Hi(e):new Se(e,t,n,r??1)}function Se(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}rr(Se,su,zi(Mt,{brighter(e){return e=e==null?Kt:Math.pow(Kt,e),new Se(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?bt:Math.pow(bt,e),new Se(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,i=2*n-r;return new ge(yn(e>=240?e-240:e+120,i,r),yn(e,i,r),yn(e<120?e+240:e-120,i,r),this.opacity)},clamp(){return new Se(Or(this.h),It(this.s),It(this.l),Wt(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Wt(this.opacity);return`${e===1?"hsl(":"hsla("}${Or(this.h)}, ${It(this.s)*100}%, ${It(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Or(e){return e=(e||0)%360,e<0?e+360:e}function It(e){return Math.max(0,Math.min(1,e||0))}function yn(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}const Bi=e=>()=>e;function uu(e,t){return function(n){return e+n*t}}function cu(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(r){return Math.pow(e+r*t,n)}}function fu(e){return(e=+e)==1?Xi:function(t,n){return n-t?cu(t,n,e):Bi(isNaN(t)?n:t)}}function Xi(e,t){var n=t-e;return n?uu(e,n):Bi(isNaN(e)?t:e)}const Lr=function e(t){var n=fu(t);function r(i,o){var l=n((i=Pn(i)).r,(o=Pn(o)).r),s=n(i.g,o.g),a=n(i.b,o.b),u=Xi(i.opacity,o.opacity);return function(c){return i.r=l(c),i.g=s(c),i.b=a(c),i.opacity=u(c),i+""}}return r.gamma=e,r}(1);function Oe(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var Vn=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,wn=new RegExp(Vn.source,"g");function du(e){return function(){return e}}function pu(e){return function(t){return e(t)+""}}function hu(e,t){var n=Vn.lastIndex=wn.lastIndex=0,r,i,o,l=-1,s=[],a=[];for(e=e+"",t=t+"";(r=Vn.exec(e))&&(i=wn.exec(t));)(o=i.index)>n&&(o=t.slice(n,o),s[l]?s[l]+=o:s[++l]=o),(r=r[0])===(i=i[0])?s[l]?s[l]+=i:s[++l]=i:(s[++l]=null,a.push({i:l,x:Oe(r,i)})),n=wn.lastIndex;return n<t.length&&(o=t.slice(n),s[l]?s[l]+=o:s[++l]=o),s.length<2?a[0]?pu(a[0].x):du(t):(t=a.length,function(u){for(var c=0,f;c<t;++c)s[(f=a[c]).i]=f.x(u);return s.join("")})}var Fr=180/Math.PI,Mn={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Gi(e,t,n,r,i,o){var l,s,a;return(l=Math.sqrt(e*e+t*t))&&(e/=l,t/=l),(a=e*n+t*r)&&(n-=e*a,r-=t*a),(s=Math.sqrt(n*n+r*r))&&(n/=s,r/=s,a/=s),e*r<t*n&&(e=-e,t=-t,a=-a,l=-l),{translateX:i,translateY:o,rotate:Math.atan2(t,e)*Fr,skewX:Math.atan(a)*Fr,scaleX:l,scaleY:s}}var Tt;function mu(e){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?Mn:Gi(t.a,t.b,t.c,t.d,t.e,t.f)}function _u(e){return e==null||(Tt||(Tt=document.createElementNS("http://www.w3.org/2000/svg","g")),Tt.setAttribute("transform",e),!(e=Tt.transform.baseVal.consolidate()))?Mn:(e=e.matrix,Gi(e.a,e.b,e.c,e.d,e.e,e.f))}function Yi(e,t,n,r){function i(u){return u.length?u.pop()+" ":""}function o(u,c,f,d,p,m){if(u!==f||c!==d){var k=p.push("translate(",null,t,null,n);m.push({i:k-4,x:Oe(u,f)},{i:k-2,x:Oe(c,d)})}else(f||d)&&p.push("translate("+f+t+d+n)}function l(u,c,f,d){u!==c?(u-c>180?c+=360:c-u>180&&(u+=360),d.push({i:f.push(i(f)+"rotate(",null,r)-2,x:Oe(u,c)})):c&&f.push(i(f)+"rotate("+c+r)}function s(u,c,f,d){u!==c?d.push({i:f.push(i(f)+"skewX(",null,r)-2,x:Oe(u,c)}):c&&f.push(i(f)+"skewX("+c+r)}function a(u,c,f,d,p,m){if(u!==f||c!==d){var k=p.push(i(p)+"scale(",null,",",null,")");m.push({i:k-4,x:Oe(u,f)},{i:k-2,x:Oe(c,d)})}else(f!==1||d!==1)&&p.push(i(p)+"scale("+f+","+d+")")}return function(u,c){var f=[],d=[];return u=e(u),c=e(c),o(u.translateX,u.translateY,c.translateX,c.translateY,f,d),l(u.rotate,c.rotate,f,d),s(u.skewX,c.skewX,f,d),a(u.scaleX,u.scaleY,c.scaleX,c.scaleY,f,d),u=c=null,function(p){for(var m=-1,k=d.length,C;++m<k;)f[(C=d[m]).i]=C.x(p);return f.join("")}}}var gu=Yi(mu,"px, ","px)","deg)"),vu=Yi(_u,", ",")",")"),it=0,ft=0,ct=0,Ki=1e3,Qt,dt,Zt=0,Xe=0,dn=0,kt=typeof performance=="object"&&performance.now?performance:Date,Wi=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function ir(){return Xe||(Wi(yu),Xe=kt.now()+dn)}function yu(){Xe=0}function Jt(){this._call=this._time=this._next=null}Jt.prototype=Qi.prototype={constructor:Jt,restart:function(e,t,n){if(typeof e!="function")throw new TypeError("callback is not a function");n=(n==null?ir():+n)+(t==null?0:+t),!this._next&&dt!==this&&(dt?dt._next=this:Qt=this,dt=this),this._call=e,this._time=n,An()},stop:function(){this._call&&(this._call=null,this._time=1/0,An())}};function Qi(e,t,n){var r=new Jt;return r.restart(e,t,n),r}function wu(){ir(),++it;for(var e=Qt,t;e;)(t=Xe-e._time)>=0&&e._call.call(void 0,t),e=e._next;--it}function qr(){Xe=(Zt=kt.now())+dn,it=ft=0;try{wu()}finally{it=0,bu(),Xe=0}}function xu(){var e=kt.now(),t=e-Zt;t>Ki&&(dn-=t,Zt=e)}function bu(){for(var e,t=Qt,n,r=1/0;t;)t._call?(r>t._time&&(r=t._time),e=t,t=t._next):(n=t._next,t._next=null,t=e?e._next=n:Qt=n);dt=e,An(r)}function An(e){if(!it){ft&&(ft=clearTimeout(ft));var t=e-Xe;t>24?(e<1/0&&(ft=setTimeout(qr,e-kt.now()-dn)),ct&&(ct=clearInterval(ct))):(ct||(Zt=kt.now(),ct=setInterval(xu,Ki)),it=1,Wi(qr))}}function zr(e,t,n){var r=new Jt;return t=t==null?0:+t,r.restart(i=>{r.stop(),e(i+t)},t,n),r}var Eu=Pi("start","end","cancel","interrupt"),Su=[],Zi=0,Hr=1,Nn=2,zt=3,Br=4,In=5,Ht=6;function pn(e,t,n,r,i,o){var l=e.__transition;if(!l)e.__transition={};else if(n in l)return;ku(e,n,{name:t,index:r,group:i,on:Eu,tween:Su,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:Zi})}function or(e,t){var n=Re(e,t);if(n.state>Zi)throw new Error("too late; already scheduled");return n}function Me(e,t){var n=Re(e,t);if(n.state>zt)throw new Error("too late; already running");return n}function Re(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function ku(e,t,n){var r=e.__transition,i;r[t]=n,n.timer=Qi(o,0,n.time);function o(u){n.state=Hr,n.timer.restart(l,n.delay,n.time),n.delay<=u&&l(u-n.delay)}function l(u){var c,f,d,p;if(n.state!==Hr)return a();for(c in r)if(p=r[c],p.name===n.name){if(p.state===zt)return zr(l);p.state===Br?(p.state=Ht,p.timer.stop(),p.on.call("interrupt",e,e.__data__,p.index,p.group),delete r[c]):+c<t&&(p.state=Ht,p.timer.stop(),p.on.call("cancel",e,e.__data__,p.index,p.group),delete r[c])}if(zr(function(){n.state===zt&&(n.state=Br,n.timer.restart(s,n.delay,n.time),s(u))}),n.state=Nn,n.on.call("start",e,e.__data__,n.index,n.group),n.state===Nn){for(n.state=zt,i=new Array(d=n.tween.length),c=0,f=-1;c<d;++c)(p=n.tween[c].value.call(e,e.__data__,n.index,n.group))&&(i[++f]=p);i.length=f+1}}function s(u){for(var c=u<n.duration?n.ease.call(null,u/n.duration):(n.timer.restart(a),n.state=In,1),f=-1,d=i.length;++f<d;)i[f].call(e,c);n.state===In&&(n.on.call("end",e,e.__data__,n.index,n.group),a())}function a(){n.state=Ht,n.timer.stop(),delete r[t];for(var u in r)return;delete e.__transition}}function Ru(e,t){var n=e.__transition,r,i,o=!0,l;if(n){t=t==null?null:t+"";for(l in n){if((r=n[l]).name!==t){o=!1;continue}i=r.state>Nn&&r.state<In,r.state=Ht,r.timer.stop(),r.on.call(i?"interrupt":"cancel",e,e.__data__,r.index,r.group),delete n[l]}o&&delete e.__transition}}function Cu(e){return this.each(function(){Ru(this,e)})}function $u(e,t){var n,r;return function(){var i=Me(this,e),o=i.tween;if(o!==n){r=n=o;for(var l=0,s=r.length;l<s;++l)if(r[l].name===t){r=r.slice(),r.splice(l,1);break}}i.tween=r}}function Pu(e,t,n){var r,i;if(typeof n!="function")throw new Error;return function(){var o=Me(this,e),l=o.tween;if(l!==r){i=(r=l).slice();for(var s={name:t,value:n},a=0,u=i.length;a<u;++a)if(i[a].name===t){i[a]=s;break}a===u&&i.push(s)}o.tween=i}}function Vu(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r=Re(this.node(),n).tween,i=0,o=r.length,l;i<o;++i)if((l=r[i]).name===e)return l.value;return null}return this.each((t==null?$u:Pu)(n,e,t))}function lr(e,t,n){var r=e._id;return e.each(function(){var i=Me(this,r);(i.value||(i.value={}))[t]=n.apply(this,arguments)}),function(i){return Re(i,r).value[t]}}function Ji(e,t){var n;return(typeof t=="number"?Oe:t instanceof St?Lr:(n=St(t))?(t=n,Lr):hu)(e,t)}function Mu(e){return function(){this.removeAttribute(e)}}function Au(e){return function(){this.removeAttributeNS(e.space,e.local)}}function Nu(e,t,n){var r,i=n+"",o;return function(){var l=this.getAttribute(e);return l===i?null:l===r?o:o=t(r=l,n)}}function Iu(e,t,n){var r,i=n+"",o;return function(){var l=this.getAttributeNS(e.space,e.local);return l===i?null:l===r?o:o=t(r=l,n)}}function Tu(e,t,n){var r,i,o;return function(){var l,s=n(this),a;return s==null?void this.removeAttribute(e):(l=this.getAttribute(e),a=s+"",l===a?null:l===r&&a===i?o:(i=a,o=t(r=l,s)))}}function Uu(e,t,n){var r,i,o;return function(){var l,s=n(this),a;return s==null?void this.removeAttributeNS(e.space,e.local):(l=this.getAttributeNS(e.space,e.local),a=s+"",l===a?null:l===r&&a===i?o:(i=a,o=t(r=l,s)))}}function Du(e,t){var n=fn(e),r=n==="transform"?vu:Ji;return this.attrTween(e,typeof t=="function"?(n.local?Uu:Tu)(n,r,lr(this,"attr."+e,t)):t==null?(n.local?Au:Mu)(n):(n.local?Iu:Nu)(n,r,t))}function Ou(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function Lu(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function Fu(e,t){var n,r;function i(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&Lu(e,o)),n}return i._value=t,i}function qu(e,t){var n,r;function i(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&Ou(e,o)),n}return i._value=t,i}function zu(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!="function")throw new Error;var r=fn(e);return this.tween(n,(r.local?Fu:qu)(r,t))}function Hu(e,t){return function(){or(this,e).delay=+t.apply(this,arguments)}}function Bu(e,t){return t=+t,function(){or(this,e).delay=t}}function Xu(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?Hu:Bu)(t,e)):Re(this.node(),t).delay}function Gu(e,t){return function(){Me(this,e).duration=+t.apply(this,arguments)}}function Yu(e,t){return t=+t,function(){Me(this,e).duration=t}}function Ku(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?Gu:Yu)(t,e)):Re(this.node(),t).duration}function Wu(e,t){if(typeof t!="function")throw new Error;return function(){Me(this,e).ease=t}}function Qu(e){var t=this._id;return arguments.length?this.each(Wu(t,e)):Re(this.node(),t).ease}function Zu(e,t){return function(){var n=t.apply(this,arguments);if(typeof n!="function")throw new Error;Me(this,e).ease=n}}function Ju(e){if(typeof e!="function")throw new Error;return this.each(Zu(this._id,e))}function ju(e){typeof e!="function"&&(e=Ai(e));for(var t=this._groups,n=t.length,r=new Array(n),i=0;i<n;++i)for(var o=t[i],l=o.length,s=r[i]=[],a,u=0;u<l;++u)(a=o[u])&&e.call(a,a.__data__,u,o)&&s.push(a);return new Te(r,this._parents,this._name,this._id)}function ec(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,r=t.length,i=n.length,o=Math.min(r,i),l=new Array(r),s=0;s<o;++s)for(var a=t[s],u=n[s],c=a.length,f=l[s]=new Array(c),d,p=0;p<c;++p)(d=a[p]||u[p])&&(f[p]=d);for(;s<r;++s)l[s]=t[s];return new Te(l,this._parents,this._name,this._id)}function tc(e){return(e+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||t==="start"})}function nc(e,t,n){var r,i,o=tc(t)?or:Me;return function(){var l=o(this,e),s=l.on;s!==r&&(i=(r=s).copy()).on(t,n),l.on=i}}function rc(e,t){var n=this._id;return arguments.length<2?Re(this.node(),n).on.on(e):this.each(nc(n,e,t))}function ic(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}function oc(){return this.on("end.remove",ic(this._id))}function lc(e){var t=this._name,n=this._id;typeof e!="function"&&(e=tr(e));for(var r=this._groups,i=r.length,o=new Array(i),l=0;l<i;++l)for(var s=r[l],a=s.length,u=o[l]=new Array(a),c,f,d=0;d<a;++d)(c=s[d])&&(f=e.call(c,c.__data__,d,s))&&("__data__"in c&&(f.__data__=c.__data__),u[d]=f,pn(u[d],t,n,d,u,Re(c,n)));return new Te(o,this._parents,t,n)}function ac(e){var t=this._name,n=this._id;typeof e!="function"&&(e=Mi(e));for(var r=this._groups,i=r.length,o=[],l=[],s=0;s<i;++s)for(var a=r[s],u=a.length,c,f=0;f<u;++f)if(c=a[f]){for(var d=e.call(c,c.__data__,f,a),p,m=Re(c,n),k=0,C=d.length;k<C;++k)(p=d[k])&&pn(p,t,n,k,d,m);o.push(d),l.push(c)}return new Te(o,l,t,n)}var sc=Vt.prototype.constructor;function uc(){return new sc(this._groups,this._parents)}function cc(e,t){var n,r,i;return function(){var o=rt(this,e),l=(this.style.removeProperty(e),rt(this,e));return o===l?null:o===n&&l===r?i:i=t(n=o,r=l)}}function ji(e){return function(){this.style.removeProperty(e)}}function fc(e,t,n){var r,i=n+"",o;return function(){var l=rt(this,e);return l===i?null:l===r?o:o=t(r=l,n)}}function dc(e,t,n){var r,i,o;return function(){var l=rt(this,e),s=n(this),a=s+"";return s==null&&(a=s=(this.style.removeProperty(e),rt(this,e))),l===a?null:l===r&&a===i?o:(i=a,o=t(r=l,s))}}function pc(e,t){var n,r,i,o="style."+t,l="end."+o,s;return function(){var a=Me(this,e),u=a.on,c=a.value[o]==null?s||(s=ji(t)):void 0;(u!==n||i!==c)&&(r=(n=u).copy()).on(l,i=c),a.on=r}}function hc(e,t,n){var r=(e+="")=="transform"?gu:Ji;return t==null?this.styleTween(e,cc(e,r)).on("end.style."+e,ji(e)):typeof t=="function"?this.styleTween(e,dc(e,r,lr(this,"style."+e,t))).each(pc(this._id,e)):this.styleTween(e,fc(e,r,t),n).on("end.style."+e,null)}function mc(e,t,n){return function(r){this.style.setProperty(e,t.call(this,r),n)}}function _c(e,t,n){var r,i;function o(){var l=t.apply(this,arguments);return l!==i&&(r=(i=l)&&mc(e,l,n)),r}return o._value=t,o}function gc(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;return this.tween(r,_c(e,t,n??""))}function vc(e){return function(){this.textContent=e}}function yc(e){return function(){var t=e(this);this.textContent=t??""}}function wc(e){return this.tween("text",typeof e=="function"?yc(lr(this,"text",e)):vc(e==null?"":e+""))}function xc(e){return function(t){this.textContent=e.call(this,t)}}function bc(e){var t,n;function r(){var i=e.apply(this,arguments);return i!==n&&(t=(n=i)&&xc(i)),t}return r._value=e,r}function Ec(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,bc(e))}function Sc(){for(var e=this._name,t=this._id,n=eo(),r=this._groups,i=r.length,o=0;o<i;++o)for(var l=r[o],s=l.length,a,u=0;u<s;++u)if(a=l[u]){var c=Re(a,t);pn(a,e,n,u,l,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new Te(r,this._parents,e,n)}function kc(){var e,t,n=this,r=n._id,i=n.size();return new Promise(function(o,l){var s={value:l},a={value:function(){--i===0&&o()}};n.each(function(){var u=Me(this,r),c=u.on;c!==e&&(t=(e=c).copy(),t._.cancel.push(s),t._.interrupt.push(s),t._.end.push(a)),u.on=t}),i===0&&o()})}var Rc=0;function Te(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}function eo(){return++Rc}var Ne=Vt.prototype;Te.prototype={constructor:Te,select:lc,selectAll:ac,selectChild:Ne.selectChild,selectChildren:Ne.selectChildren,filter:ju,merge:ec,selection:uc,transition:Sc,call:Ne.call,nodes:Ne.nodes,node:Ne.node,size:Ne.size,empty:Ne.empty,each:Ne.each,on:rc,attr:Du,attrTween:zu,style:hc,styleTween:gc,text:wc,textTween:Ec,remove:oc,tween:Vu,delay:Xu,duration:Ku,ease:Qu,easeVarying:Ju,end:kc,[Symbol.iterator]:Ne[Symbol.iterator]};function Cc(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var $c={time:null,delay:0,duration:250,ease:Cc};function Pc(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}function Vc(e){var t,n;e instanceof Te?(t=e._id,e=e._name):(t=eo(),(n=$c).time=ir(),e=e==null?null:e+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var l=r[o],s=l.length,a,u=0;u<s;++u)(a=l[u])&&pn(a,e,t,u,l,n||Pc(a,t));return new Te(r,this._parents,e,t)}Vt.prototype.interrupt=Cu;Vt.prototype.transition=Vc;function Mc(e){if(!e.ok)throw new Error(e.status+" "+e.statusText);if(!(e.status===204||e.status===205))return e.json()}function Ac(e,t){return fetch(e,t).then(Mc)}var te=1e-6,K=Math.PI,ve=K/2,Xr=K/4,xe=K*2,ye=180/K,se=K/180,ie=Math.abs,to=Math.atan,Rt=Math.atan2,oe=Math.cos,Nc=Math.exp,Ic=Math.log,le=Math.sin,Tc=Math.sign||function(e){return e>0?1:e<0?-1:0},Ye=Math.sqrt,Uc=Math.tan;function Dc(e){return e>1?0:e<-1?K:Math.acos(e)}function Ct(e){return e>1?ve:e<-1?-ve:Math.asin(e)}function Ee(){}function jt(e,t){e&&Yr.hasOwnProperty(e.type)&&Yr[e.type](e,t)}var Gr={Feature:function(e,t){jt(e.geometry,t)},FeatureCollection:function(e,t){for(var n=e.features,r=-1,i=n.length;++r<i;)jt(n[r].geometry,t)}},Yr={Sphere:function(e,t){t.sphere()},Point:function(e,t){e=e.coordinates,t.point(e[0],e[1],e[2])},MultiPoint:function(e,t){for(var n=e.coordinates,r=-1,i=n.length;++r<i;)e=n[r],t.point(e[0],e[1],e[2])},LineString:function(e,t){Tn(e.coordinates,t,0)},MultiLineString:function(e,t){for(var n=e.coordinates,r=-1,i=n.length;++r<i;)Tn(n[r],t,0)},Polygon:function(e,t){Kr(e.coordinates,t)},MultiPolygon:function(e,t){for(var n=e.coordinates,r=-1,i=n.length;++r<i;)Kr(n[r],t)},GeometryCollection:function(e,t){for(var n=e.geometries,r=-1,i=n.length;++r<i;)jt(n[r],t)}};function Tn(e,t,n){var r=-1,i=e.length-n,o;for(t.lineStart();++r<i;)o=e[r],t.point(o[0],o[1],o[2]);t.lineEnd()}function Kr(e,t){var n=-1,r=e.length;for(t.polygonStart();++n<r;)Tn(e[n],t,1);t.polygonEnd()}function Qe(e,t){e&&Gr.hasOwnProperty(e.type)?Gr[e.type](e,t):jt(e,t)}function Un(e){return[Rt(e[1],e[0]),Ct(e[2])]}function ot(e){var t=e[0],n=e[1],r=oe(n);return[r*oe(t),r*le(t),le(n)]}function Ut(e,t){return e[0]*t[0]+e[1]*t[1]+e[2]*t[2]}function en(e,t){return[e[1]*t[2]-e[2]*t[1],e[2]*t[0]-e[0]*t[2],e[0]*t[1]-e[1]*t[0]]}function xn(e,t){e[0]+=t[0],e[1]+=t[1],e[2]+=t[2]}function Dt(e,t){return[e[0]*t,e[1]*t,e[2]*t]}function Dn(e){var t=Ye(e[0]*e[0]+e[1]*e[1]+e[2]*e[2]);e[0]/=t,e[1]/=t,e[2]/=t}function On(e,t){function n(r,i){return r=e(r,i),t(r[0],r[1])}return e.invert&&t.invert&&(n.invert=function(r,i){return r=t.invert(r,i),r&&e.invert(r[0],r[1])}),n}function Ln(e,t){return ie(e)>K&&(e-=Math.round(e/xe)*xe),[e,t]}Ln.invert=Ln;function no(e,t,n){return(e%=xe)?t||n?On(Qr(e),Zr(t,n)):Qr(e):t||n?Zr(t,n):Ln}function Wr(e){return function(t,n){return t+=e,ie(t)>K&&(t-=Math.round(t/xe)*xe),[t,n]}}function Qr(e){var t=Wr(e);return t.invert=Wr(-e),t}function Zr(e,t){var n=oe(e),r=le(e),i=oe(t),o=le(t);function l(s,a){var u=oe(a),c=oe(s)*u,f=le(s)*u,d=le(a),p=d*n+c*r;return[Rt(f*i-p*o,c*n-d*r),Ct(p*i+f*o)]}return l.invert=function(s,a){var u=oe(a),c=oe(s)*u,f=le(s)*u,d=le(a),p=d*i-f*o;return[Rt(f*i+d*o,c*n+p*r),Ct(p*n-c*r)]},l}function Oc(e){e=no(e[0]*se,e[1]*se,e.length>2?e[2]*se:0);function t(n){return n=e(n[0]*se,n[1]*se),n[0]*=ye,n[1]*=ye,n}return t.invert=function(n){return n=e.invert(n[0]*se,n[1]*se),n[0]*=ye,n[1]*=ye,n},t}function Lc(e,t,n,r,i,o){if(n){var l=oe(t),s=le(t),a=r*n;i==null?(i=t+r*xe,o=t-a/2):(i=Jr(l,i),o=Jr(l,o),(r>0?i<o:i>o)&&(i+=r*xe));for(var u,c=i;r>0?c>o:c<o;c-=a)u=Un([l,-s*oe(c),-s*le(c)]),e.point(u[0],u[1])}}function Jr(e,t){t=ot(t),t[0]-=e,Dn(t);var n=Dc(-t[1]);return((-t[2]<0?-n:n)+xe-te)%xe}function ro(){var e=[],t;return{point:function(n,r,i){t.push([n,r,i])},lineStart:function(){e.push(t=[])},lineEnd:Ee,rejoin:function(){e.length>1&&e.push(e.pop().concat(e.shift()))},result:function(){var n=e;return e=[],t=null,n}}}function Bt(e,t){return ie(e[0]-t[0])<te&&ie(e[1]-t[1])<te}function Ot(e,t,n,r){this.x=e,this.z=t,this.o=n,this.e=r,this.v=!1,this.n=this.p=null}function io(e,t,n,r,i){var o=[],l=[],s,a;if(e.forEach(function(m){if(!((k=m.length-1)<=0)){var k,C=m[0],A=m[k],P;if(Bt(C,A)){if(!C[2]&&!A[2]){for(i.lineStart(),s=0;s<k;++s)i.point((C=m[s])[0],C[1]);i.lineEnd();return}A[0]+=2*te}o.push(P=new Ot(C,m,null,!0)),l.push(P.o=new Ot(C,null,P,!1)),o.push(P=new Ot(A,m,null,!1)),l.push(P.o=new Ot(A,null,P,!0))}}),!!o.length){for(l.sort(t),jr(o),jr(l),s=0,a=l.length;s<a;++s)l[s].e=n=!n;for(var u=o[0],c,f;;){for(var d=u,p=!0;d.v;)if((d=d.n)===u)return;c=d.z,i.lineStart();do{if(d.v=d.o.v=!0,d.e){if(p)for(s=0,a=c.length;s<a;++s)i.point((f=c[s])[0],f[1]);else r(d.x,d.n.x,1,i);d=d.n}else{if(p)for(c=d.p.z,s=c.length-1;s>=0;--s)i.point((f=c[s])[0],f[1]);else r(d.x,d.p.x,-1,i);d=d.p}d=d.o,c=d.z,p=!p}while(!d.v);i.lineEnd()}}}function jr(e){if(t=e.length){for(var t,n=0,r=e[0],i;++n<t;)r.n=i=e[n],i.p=r,r=i;r.n=i=e[0],i.p=r}}function bn(e){return ie(e[0])<=K?e[0]:Tc(e[0])*((ie(e[0])+K)%xe-K)}function Fc(e,t){var n=bn(t),r=t[1],i=le(r),o=[le(n),-oe(n),0],l=0,s=0,a=new Be;i===1?r=ve+te:i===-1&&(r=-ve-te);for(var u=0,c=e.length;u<c;++u)if(d=(f=e[u]).length)for(var f,d,p=f[d-1],m=bn(p),k=p[1]/2+Xr,C=le(k),A=oe(k),P=0;P<d;++P,m=E,C=V,A=x,p=S){var S=f[P],E=bn(S),M=S[1]/2+Xr,V=le(M),x=oe(M),g=E-m,_=g>=0?1:-1,v=_*g,w=v>K,T=C*V;if(a.add(Rt(T*_*le(v),A*x+T*oe(v))),l+=w?g+_*xe:g,w^m>=n^E>=n){var N=en(ot(p),ot(S));Dn(N);var D=en(o,N);Dn(D);var b=(w^g>=0?-1:1)*Ct(D[2]);(r>b||r===b&&(N[0]||N[1]))&&(s+=w^g>=0?1:-1)}}return(l<-1e-6||l<te&&a<-1e-12)^s&1}function oo(e,t,n,r){return function(i){var o=t(i),l=ro(),s=t(l),a=!1,u,c,f,d={point:p,lineStart:k,lineEnd:C,polygonStart:function(){d.point=A,d.lineStart=P,d.lineEnd=S,c=[],u=[]},polygonEnd:function(){d.point=p,d.lineStart=k,d.lineEnd=C,c=$i(c);var E=Fc(u,r);c.length?(a||(i.polygonStart(),a=!0),io(c,zc,E,n,i)):E&&(a||(i.polygonStart(),a=!0),i.lineStart(),n(null,null,1,i),i.lineEnd()),a&&(i.polygonEnd(),a=!1),c=u=null},sphere:function(){i.polygonStart(),i.lineStart(),n(null,null,1,i),i.lineEnd(),i.polygonEnd()}};function p(E,M){e(E,M)&&i.point(E,M)}function m(E,M){o.point(E,M)}function k(){d.point=m,o.lineStart()}function C(){d.point=p,o.lineEnd()}function A(E,M){f.push([E,M]),s.point(E,M)}function P(){s.lineStart(),f=[]}function S(){A(f[0][0],f[0][1]),s.lineEnd();var E=s.clean(),M=l.result(),V,x=M.length,g,_,v;if(f.pop(),u.push(f),f=null,!!x){if(E&1){if(_=M[0],(g=_.length-1)>0){for(a||(i.polygonStart(),a=!0),i.lineStart(),V=0;V<g;++V)i.point((v=_[V])[0],v[1]);i.lineEnd()}return}x>1&&E&2&&M.push(M.pop().concat(M.shift())),c.push(M.filter(qc))}}return d}}function qc(e){return e.length>1}function zc(e,t){return((e=e.x)[0]<0?e[1]-ve-te:ve-e[1])-((t=t.x)[0]<0?t[1]-ve-te:ve-t[1])}const ei=oo(function(){return!0},Hc,Xc,[-K,-ve]);function Hc(e){var t=NaN,n=NaN,r=NaN,i;return{lineStart:function(){e.lineStart(),i=1},point:function(o,l){var s=o>0?K:-K,a=ie(o-t);ie(a-K)<te?(e.point(t,n=(n+l)/2>0?ve:-ve),e.point(r,n),e.lineEnd(),e.lineStart(),e.point(s,n),e.point(o,n),i=0):r!==s&&a>=K&&(ie(t-r)<te&&(t-=r*te),ie(o-s)<te&&(o-=s*te),n=Bc(t,n,o,l),e.point(r,n),e.lineEnd(),e.lineStart(),e.point(s,n),i=0),e.point(t=o,n=l),r=s},lineEnd:function(){e.lineEnd(),t=n=NaN},clean:function(){return 2-i}}}function Bc(e,t,n,r){var i,o,l=le(e-n);return ie(l)>te?to((le(t)*(o=oe(r))*le(n)-le(r)*(i=oe(t))*le(e))/(i*o*l)):(t+r)/2}function Xc(e,t,n,r){var i;if(e==null)i=n*ve,r.point(-K,i),r.point(0,i),r.point(K,i),r.point(K,0),r.point(K,-i),r.point(0,-i),r.point(-K,-i),r.point(-K,0),r.point(-K,i);else if(ie(e[0]-t[0])>te){var o=e[0]<t[0]?K:-K;i=n*o/2,r.point(-o,i),r.point(0,i),r.point(o,i)}else r.point(t[0],t[1])}function Gc(e){var t=oe(e),n=2*se,r=t>0,i=ie(t)>te;function o(c,f,d,p){Lc(p,e,n,d,c,f)}function l(c,f){return oe(c)*oe(f)>t}function s(c){var f,d,p,m,k;return{lineStart:function(){m=p=!1,k=1},point:function(C,A){var P=[C,A],S,E=l(C,A),M=r?E?0:u(C,A):E?u(C+(C<0?K:-K),A):0;if(!f&&(m=p=E)&&c.lineStart(),E!==p&&(S=a(f,P),(!S||Bt(f,S)||Bt(P,S))&&(P[2]=1)),E!==p)k=0,E?(c.lineStart(),S=a(P,f),c.point(S[0],S[1])):(S=a(f,P),c.point(S[0],S[1],2),c.lineEnd()),f=S;else if(i&&f&&r^E){var V;!(M&d)&&(V=a(P,f,!0))&&(k=0,r?(c.lineStart(),c.point(V[0][0],V[0][1]),c.point(V[1][0],V[1][1]),c.lineEnd()):(c.point(V[1][0],V[1][1]),c.lineEnd(),c.lineStart(),c.point(V[0][0],V[0][1],3)))}E&&(!f||!Bt(f,P))&&c.point(P[0],P[1]),f=P,p=E,d=M},lineEnd:function(){p&&c.lineEnd(),f=null},clean:function(){return k|(m&&p)<<1}}}function a(c,f,d){var p=ot(c),m=ot(f),k=[1,0,0],C=en(p,m),A=Ut(C,C),P=C[0],S=A-P*P;if(!S)return!d&&c;var E=t*A/S,M=-t*P/S,V=en(k,C),x=Dt(k,E),g=Dt(C,M);xn(x,g);var _=V,v=Ut(x,_),w=Ut(_,_),T=v*v-w*(Ut(x,x)-1);if(!(T<0)){var N=Ye(T),D=Dt(_,(-v-N)/w);if(xn(D,x),D=Un(D),!d)return D;var b=c[0],F=f[0],G=c[1],Z=f[1],j;F<b&&(j=b,b=F,F=j);var $=F-b,B=ie($-K)<te,he=B||$<te;if(!B&&Z<G&&(j=G,G=Z,Z=j),he?B?G+Z>0^D[1]<(ie(D[0]-b)<te?G:Z):G<=D[1]&&D[1]<=Z:$>K^(b<=D[0]&&D[0]<=F)){var de=Dt(_,(-v+N)/w);return xn(de,x),[D,Un(de)]}}}function u(c,f){var d=r?e:K-e,p=0;return c<-d?p|=1:c>d&&(p|=2),f<-d?p|=4:f>d&&(p|=8),p}return oo(l,s,o,r?[0,-e]:[-K,e-K])}function Yc(e,t,n,r,i,o){var l=e[0],s=e[1],a=t[0],u=t[1],c=0,f=1,d=a-l,p=u-s,m;if(m=n-l,!(!d&&m>0)){if(m/=d,d<0){if(m<c)return;m<f&&(f=m)}else if(d>0){if(m>f)return;m>c&&(c=m)}if(m=i-l,!(!d&&m<0)){if(m/=d,d<0){if(m>f)return;m>c&&(c=m)}else if(d>0){if(m<c)return;m<f&&(f=m)}if(m=r-s,!(!p&&m>0)){if(m/=p,p<0){if(m<c)return;m<f&&(f=m)}else if(p>0){if(m>f)return;m>c&&(c=m)}if(m=o-s,!(!p&&m<0)){if(m/=p,p<0){if(m>f)return;m>c&&(c=m)}else if(p>0){if(m<c)return;m<f&&(f=m)}return c>0&&(e[0]=l+c*d,e[1]=s+c*p),f<1&&(t[0]=l+f*d,t[1]=s+f*p),!0}}}}}var Lt=1e9,Ft=-1e9;function Kc(e,t,n,r){function i(u,c){return e<=u&&u<=n&&t<=c&&c<=r}function o(u,c,f,d){var p=0,m=0;if(u==null||(p=l(u,f))!==(m=l(c,f))||a(u,c)<0^f>0)do d.point(p===0||p===3?e:n,p>1?r:t);while((p=(p+f+4)%4)!==m);else d.point(c[0],c[1])}function l(u,c){return ie(u[0]-e)<te?c>0?0:3:ie(u[0]-n)<te?c>0?2:1:ie(u[1]-t)<te?c>0?1:0:c>0?3:2}function s(u,c){return a(u.x,c.x)}function a(u,c){var f=l(u,1),d=l(c,1);return f!==d?f-d:f===0?c[1]-u[1]:f===1?u[0]-c[0]:f===2?u[1]-c[1]:c[0]-u[0]}return function(u){var c=u,f=ro(),d,p,m,k,C,A,P,S,E,M,V,x={point:g,lineStart:T,lineEnd:N,polygonStart:v,polygonEnd:w};function g(b,F){i(b,F)&&c.point(b,F)}function _(){for(var b=0,F=0,G=p.length;F<G;++F)for(var Z=p[F],j=1,$=Z.length,B=Z[0],he,de,H=B[0],pe=B[1];j<$;++j)he=H,de=pe,B=Z[j],H=B[0],pe=B[1],de<=r?pe>r&&(H-he)*(r-de)>(pe-de)*(e-he)&&++b:pe<=r&&(H-he)*(r-de)<(pe-de)*(e-he)&&--b;return b}function v(){c=f,d=[],p=[],V=!0}function w(){var b=_(),F=V&&b,G=(d=$i(d)).length;(F||G)&&(u.polygonStart(),F&&(u.lineStart(),o(null,null,1,u),u.lineEnd()),G&&io(d,s,b,o,u),u.polygonEnd()),c=u,d=p=m=null}function T(){x.point=D,p&&p.push(m=[]),M=!0,E=!1,P=S=NaN}function N(){d&&(D(k,C),A&&E&&f.rejoin(),d.push(f.result())),x.point=g,E&&c.lineEnd()}function D(b,F){var G=i(b,F);if(p&&m.push([b,F]),M)k=b,C=F,A=G,M=!1,G&&(c.lineStart(),c.point(b,F));else if(G&&E)c.point(b,F);else{var Z=[P=Math.max(Ft,Math.min(Lt,P)),S=Math.max(Ft,Math.min(Lt,S))],j=[b=Math.max(Ft,Math.min(Lt,b)),F=Math.max(Ft,Math.min(Lt,F))];Yc(Z,j,e,t,n,r)?(E||(c.lineStart(),c.point(Z[0],Z[1])),c.point(j[0],j[1]),G||c.lineEnd(),V=!1):G&&(c.lineStart(),c.point(b,F),V=!1)}P=b,S=F,E=G}return x}}const Fn=e=>e;var En=new Be,qn=new Be,lo,ao,zn,Hn,Ie={point:Ee,lineStart:Ee,lineEnd:Ee,polygonStart:function(){Ie.lineStart=Wc,Ie.lineEnd=Zc},polygonEnd:function(){Ie.lineStart=Ie.lineEnd=Ie.point=Ee,En.add(ie(qn)),qn=new Be},result:function(){var e=En/2;return En=new Be,e}};function Wc(){Ie.point=Qc}function Qc(e,t){Ie.point=so,lo=zn=e,ao=Hn=t}function so(e,t){qn.add(Hn*e-zn*t),zn=e,Hn=t}function Zc(){so(lo,ao)}var lt=1/0,tn=lt,$t=-lt,nn=$t,rn={point:Jc,lineStart:Ee,lineEnd:Ee,polygonStart:Ee,polygonEnd:Ee,result:function(){var e=[[lt,tn],[$t,nn]];return $t=nn=-(tn=lt=1/0),e}};function Jc(e,t){e<lt&&(lt=e),e>$t&&($t=e),t<tn&&(tn=t),t>nn&&(nn=t)}var Bn=0,Xn=0,pt=0,on=0,ln=0,Ze=0,Gn=0,Yn=0,ht=0,uo,co,Ce,$e,be={point:Ge,lineStart:ti,lineEnd:ni,polygonStart:function(){be.lineStart=tf,be.lineEnd=nf},polygonEnd:function(){be.point=Ge,be.lineStart=ti,be.lineEnd=ni},result:function(){var e=ht?[Gn/ht,Yn/ht]:Ze?[on/Ze,ln/Ze]:pt?[Bn/pt,Xn/pt]:[NaN,NaN];return Bn=Xn=pt=on=ln=Ze=Gn=Yn=ht=0,e}};function Ge(e,t){Bn+=e,Xn+=t,++pt}function ti(){be.point=jc}function jc(e,t){be.point=ef,Ge(Ce=e,$e=t)}function ef(e,t){var n=e-Ce,r=t-$e,i=Ye(n*n+r*r);on+=i*(Ce+e)/2,ln+=i*($e+t)/2,Ze+=i,Ge(Ce=e,$e=t)}function ni(){be.point=Ge}function tf(){be.point=rf}function nf(){fo(uo,co)}function rf(e,t){be.point=fo,Ge(uo=Ce=e,co=$e=t)}function fo(e,t){var n=e-Ce,r=t-$e,i=Ye(n*n+r*r);on+=i*(Ce+e)/2,ln+=i*($e+t)/2,Ze+=i,i=$e*e-Ce*t,Gn+=i*(Ce+e),Yn+=i*($e+t),ht+=i*3,Ge(Ce=e,$e=t)}function po(e){this._context=e}po.prototype={_radius:4.5,pointRadius:function(e){return this._radius=e,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){this._line===0&&this._context.closePath(),this._point=NaN},point:function(e,t){switch(this._point){case 0:{this._context.moveTo(e,t),this._point=1;break}case 1:{this._context.lineTo(e,t);break}default:{this._context.moveTo(e+this._radius,t),this._context.arc(e,t,this._radius,0,xe);break}}},result:Ee};var Kn=new Be,Sn,ho,mo,mt,_t,Pt={point:Ee,lineStart:function(){Pt.point=of},lineEnd:function(){Sn&&_o(ho,mo),Pt.point=Ee},polygonStart:function(){Sn=!0},polygonEnd:function(){Sn=null},result:function(){var e=+Kn;return Kn=new Be,e}};function of(e,t){Pt.point=_o,ho=mt=e,mo=_t=t}function _o(e,t){mt-=e,_t-=t,Kn.add(Ye(mt*mt+_t*_t)),mt=e,_t=t}let ri,an,ii,oi;class li{constructor(t){this._append=t==null?go:lf(t),this._radius=4.5,this._=""}pointRadius(t){return this._radius=+t,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){this._line===0&&(this._+="Z"),this._point=NaN}point(t,n){switch(this._point){case 0:{this._append`M${t},${n}`,this._point=1;break}case 1:{this._append`L${t},${n}`;break}default:{if(this._append`M${t},${n}`,this._radius!==ii||this._append!==an){const r=this._radius,i=this._;this._="",this._append`m0,${r}a${r},${r} 0 1,1 0,${-2*r}a${r},${r} 0 1,1 0,${2*r}z`,ii=r,an=this._append,oi=this._,this._=i}this._+=oi;break}}}result(){const t=this._;return this._="",t.length?t:null}}function go(e){let t=1;this._+=e[0];for(const n=e.length;t<n;++t)this._+=arguments[t]+e[t]}function lf(e){const t=Math.floor(e);if(!(t>=0))throw new RangeError(`invalid digits: ${e}`);if(t>15)return go;if(t!==ri){const n=10**t;ri=t,an=function(i){let o=1;this._+=i[0];for(const l=i.length;o<l;++o)this._+=Math.round(arguments[o]*n)/n+i[o]}}return an}function ai(e,t){let n=3,r=4.5,i,o;function l(s){return s&&(typeof r=="function"&&o.pointRadius(+r.apply(this,arguments)),Qe(s,i(o))),o.result()}return l.area=function(s){return Qe(s,i(Ie)),Ie.result()},l.measure=function(s){return Qe(s,i(Pt)),Pt.result()},l.bounds=function(s){return Qe(s,i(rn)),rn.result()},l.centroid=function(s){return Qe(s,i(be)),be.result()},l.projection=function(s){return arguments.length?(i=s==null?(e=null,Fn):(e=s).stream,l):e},l.context=function(s){return arguments.length?(o=s==null?(t=null,new li(n)):new po(t=s),typeof r!="function"&&o.pointRadius(r),l):t},l.pointRadius=function(s){return arguments.length?(r=typeof s=="function"?s:(o.pointRadius(+s),+s),l):r},l.digits=function(s){if(!arguments.length)return n;if(s==null)n=null;else{const a=Math.floor(s);if(!(a>=0))throw new RangeError(`invalid digits: ${s}`);n=a}return t===null&&(o=new li(n)),l},l.projection(e).digits(n).context(t)}function ar(e){return function(t){var n=new Wn;for(var r in e)n[r]=e[r];return n.stream=t,n}}function Wn(){}Wn.prototype={constructor:Wn,point:function(e,t){this.stream.point(e,t)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};function sr(e,t,n){var r=e.clipExtent&&e.clipExtent();return e.scale(150).translate([0,0]),r!=null&&e.clipExtent(null),Qe(n,e.stream(rn)),t(rn.result()),r!=null&&e.clipExtent(r),e}function vo(e,t,n){return sr(e,function(r){var i=t[1][0]-t[0][0],o=t[1][1]-t[0][1],l=Math.min(i/(r[1][0]-r[0][0]),o/(r[1][1]-r[0][1])),s=+t[0][0]+(i-l*(r[1][0]+r[0][0]))/2,a=+t[0][1]+(o-l*(r[1][1]+r[0][1]))/2;e.scale(150*l).translate([s,a])},n)}function af(e,t,n){return vo(e,[[0,0],t],n)}function sf(e,t,n){return sr(e,function(r){var i=+t,o=i/(r[1][0]-r[0][0]),l=(i-o*(r[1][0]+r[0][0]))/2,s=-o*r[0][1];e.scale(150*o).translate([l,s])},n)}function uf(e,t,n){return sr(e,function(r){var i=+t,o=i/(r[1][1]-r[0][1]),l=-o*r[0][0],s=(i-o*(r[1][1]+r[0][1]))/2;e.scale(150*o).translate([l,s])},n)}var si=16,cf=oe(30*se);function ui(e,t){return+t?df(e,t):ff(e)}function ff(e){return ar({point:function(t,n){t=e(t,n),this.stream.point(t[0],t[1])}})}function df(e,t){function n(r,i,o,l,s,a,u,c,f,d,p,m,k,C){var A=u-r,P=c-i,S=A*A+P*P;if(S>4*t&&k--){var E=l+d,M=s+p,V=a+m,x=Ye(E*E+M*M+V*V),g=Ct(V/=x),_=ie(ie(V)-1)<te||ie(o-f)<te?(o+f)/2:Rt(M,E),v=e(_,g),w=v[0],T=v[1],N=w-r,D=T-i,b=P*N-A*D;(b*b/S>t||ie((A*N+P*D)/S-.5)>.3||l*d+s*p+a*m<cf)&&(n(r,i,o,l,s,a,w,T,_,E/=x,M/=x,V,k,C),C.point(w,T),n(w,T,_,E,M,V,u,c,f,d,p,m,k,C))}}return function(r){var i,o,l,s,a,u,c,f,d,p,m,k,C={point:A,lineStart:P,lineEnd:E,polygonStart:function(){r.polygonStart(),C.lineStart=M},polygonEnd:function(){r.polygonEnd(),C.lineStart=P}};function A(g,_){g=e(g,_),r.point(g[0],g[1])}function P(){f=NaN,C.point=S,r.lineStart()}function S(g,_){var v=ot([g,_]),w=e(g,_);n(f,d,c,p,m,k,f=w[0],d=w[1],c=g,p=v[0],m=v[1],k=v[2],si,r),r.point(f,d)}function E(){C.point=A,r.lineEnd()}function M(){P(),C.point=V,C.lineEnd=x}function V(g,_){S(i=g,_),o=f,l=d,s=p,a=m,u=k,C.point=S}function x(){n(f,d,c,p,m,k,o,l,i,s,a,u,si,r),C.lineEnd=E,E()}return C}}var pf=ar({point:function(e,t){this.stream.point(e*se,t*se)}});function hf(e){return ar({point:function(t,n){var r=e(t,n);return this.stream.point(r[0],r[1])}})}function mf(e,t,n,r,i){function o(l,s){return l*=r,s*=i,[t+e*l,n-e*s]}return o.invert=function(l,s){return[(l-t)/e*r,(n-s)/e*i]},o}function ci(e,t,n,r,i,o){if(!o)return mf(e,t,n,r,i);var l=oe(o),s=le(o),a=l*e,u=s*e,c=l/e,f=s/e,d=(s*n-l*t)/e,p=(s*t+l*n)/e;function m(k,C){return k*=r,C*=i,[a*k-u*C+t,n-u*k-a*C]}return m.invert=function(k,C){return[r*(c*k-f*C+d),i*(p-f*k-c*C)]},m}function _f(e){return gf(function(){return e})()}function gf(e){var t,n=150,r=480,i=250,o=0,l=0,s=0,a=0,u=0,c,f=0,d=1,p=1,m=null,k=ei,C=null,A,P,S,E=Fn,M=.5,V,x,g,_,v;function w(b){return g(b[0]*se,b[1]*se)}function T(b){return b=g.invert(b[0],b[1]),b&&[b[0]*ye,b[1]*ye]}w.stream=function(b){return _&&v===b?_:_=pf(hf(c)(k(V(E(v=b)))))},w.preclip=function(b){return arguments.length?(k=b,m=void 0,D()):k},w.postclip=function(b){return arguments.length?(E=b,C=A=P=S=null,D()):E},w.clipAngle=function(b){return arguments.length?(k=+b?Gc(m=b*se):(m=null,ei),D()):m*ye},w.clipExtent=function(b){return arguments.length?(E=b==null?(C=A=P=S=null,Fn):Kc(C=+b[0][0],A=+b[0][1],P=+b[1][0],S=+b[1][1]),D()):C==null?null:[[C,A],[P,S]]},w.scale=function(b){return arguments.length?(n=+b,N()):n},w.translate=function(b){return arguments.length?(r=+b[0],i=+b[1],N()):[r,i]},w.center=function(b){return arguments.length?(o=b[0]%360*se,l=b[1]%360*se,N()):[o*ye,l*ye]},w.rotate=function(b){return arguments.length?(s=b[0]%360*se,a=b[1]%360*se,u=b.length>2?b[2]%360*se:0,N()):[s*ye,a*ye,u*ye]},w.angle=function(b){return arguments.length?(f=b%360*se,N()):f*ye},w.reflectX=function(b){return arguments.length?(d=b?-1:1,N()):d<0},w.reflectY=function(b){return arguments.length?(p=b?-1:1,N()):p<0},w.precision=function(b){return arguments.length?(V=ui(x,M=b*b),D()):Ye(M)},w.fitExtent=function(b,F){return vo(w,b,F)},w.fitSize=function(b,F){return af(w,b,F)},w.fitWidth=function(b,F){return sf(w,b,F)},w.fitHeight=function(b,F){return uf(w,b,F)};function N(){var b=ci(n,0,0,d,p,f).apply(null,t(o,l)),F=ci(n,r-b[0],i-b[1],d,p,f);return c=no(s,a,u),x=On(t,F),g=On(c,x),V=ui(x,M),D()}function D(){return _=v=null,w}return function(){return t=e.apply(this,arguments),w.invert=t.invert&&T,N()}}function ur(e,t){return[e,Ic(Uc((ve+t)/2))]}ur.invert=function(e,t){return[e,2*to(Nc(t))-ve]};function vf(){return yf(ur).scale(961/xe)}function yf(e){var t=_f(e),n=t.center,r=t.scale,i=t.translate,o=t.clipExtent,l=null,s,a,u;t.scale=function(f){return arguments.length?(r(f),c()):r()},t.translate=function(f){return arguments.length?(i(f),c()):i()},t.center=function(f){return arguments.length?(n(f),c()):n()},t.clipExtent=function(f){return arguments.length?(f==null?l=s=a=u=null:(l=+f[0][0],s=+f[0][1],a=+f[1][0],u=+f[1][1]),c()):l==null?null:[[l,s],[a,u]]};function c(){var f=K*r(),d=t(Oc(t.rotate()).invert([0,0]));return o(l==null?[[d[0]-f,d[1]-f],[d[0]+f,d[1]+f]]:e===ur?[[Math.max(d[0]-f,l),s],[Math.min(d[0]+f,a),u]]:[[l,Math.max(d[1]-f,s)],[a,Math.min(d[1]+f,u)]])}return c()}function gt(e,t,n){this.k=e,this.x=t,this.y=n}gt.prototype={constructor:gt,scale:function(e){return e===1?this:new gt(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new gt(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};gt.prototype;const wf={class:"clock-container"},xf={class:"holographic-clock",viewBox:"0 0 100 100"},bf={class:"clock-markers"},Ef=["x1","y1","x2","y2"],Sf={class:"clock-hands"},kf=["x2","y2"],Rf=["x2","y2"],Cf=["x2","y2"],$f=["id"],Pf=["id"],Vf={__name:"VisualScreen",setup(e){const t=z(null),n=z(null),r=z(0),i=z(!1),o=new Map,l=z({x:0,y:0}),s=z(0),a=z(0),u=z(0),c=z(0);let f;const d=z([{id:"china",url:"https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json",center:[104,35],scaleFactor:.75,duration:5e3},{id:"sichuan",url:"https://geo.datav.aliyun.com/areas_v3/bound/510000_full.json",center:[103,30],scaleFactor:3,duration:5e3},{id:"guangan",url:"https://geo.datav.aliyun.com/areas_v3/bound/511600_full.json",center:[106.6,30.5],scaleFactor:30,duration:5e3},{id:"yuechi",url:"https://geo.datav.aliyun.com/areas_v3/bound/511621.json",center:[106.4,30.5],scaleFactor:50,duration:5e3}]),p={longitude:106.43,latitude:30.55},m=()=>t.value?{width:t.value.clientWidth,height:t.value.clientHeight}:{width:window.innerWidth,height:window.innerHeight},k=()=>{const g=new Date;s.value=g.getHours()%12,a.value=g.getMinutes(),u.value=g.getSeconds(),c.value=g.getMilliseconds()},C=g=>{if(o.has(g.id))return;const _=m(),v=At(`#${g.id}-map`).attr("viewBox",`0 0 ${_.width} ${_.height}`).attr("preserveAspectRatio","xMidYMid meet");Ac(g.url).then(w=>{const T=vf().center(g.center).scale(Math.min(_.width,_.height)*g.scaleFactor).translate([_.width/2,_.height/2]);o.set(g.id,{projection:T,data:w}),v.selectAll(".boundary").data(w.features).enter().append("path").attr("class","boundary").attr("d",ai().projection(T)),A(g.id)}).catch(w=>console.error("地图加载失败:",w))},A=g=>{const{projection:_}=o.get(g)||{};if(!_)return;const[v,w]=_([p.longitude,p.latitude]);l.value={x:v,y:w}},P=()=>{const g=m();d.value.forEach(_=>{const{projection:v,data:w}=o.get(_.id)||{};!v||!w||(v.scale(Math.min(g.width,g.height)*_.scaleFactor).translate([g.width/2,g.height/2]),At(`#${_.id}-map`).attr("viewBox",`0 0 ${g.width} ${g.height}`).selectAll(".boundary").attr("d",ai().projection(v)),_.id===d.value[r.value].id&&A(_.id))})},S=g=>{if(i.value)return;i.value=!0;const _=d.value.length,v=(r.value+g+_)%_,w=d.value[r.value],T=d.value[v];At(`#${w.id}-container`).transition().duration(1500).style("opacity",0).on("end",()=>{At(`#${T.id}-container`).style("opacity",0).classed("map-visible",!0).transition().duration(1500).style("opacity",1).on("end",()=>{r.value=v,i.value=!1,A(T.id)})})},E=()=>{const{width:_,height:v}=m();for(let w=0;w<100;w++){const T=document.createElement("div");T.className="particle";const N=Math.random()*_,D=Math.random()*v,b=Math.random()*10,F=Math.random()*2+1;T.style.left=`${N}px`,T.style.top=`${D}px`,T.style.animationDelay=`${b}s`,T.style.width=`${F}px`,T.style.height=`${F}px`,n.value.appendChild(T)}};let M;const V=()=>{M=setInterval(()=>{S(1)},d.value[0].duration+1500)};at(()=>{k(),f=setInterval(k,50),d.value.forEach(C),E(),V(),document.addEventListener("fullscreenchange",x)}),$o(()=>{clearInterval(f),clearInterval(M),o.clear(),document.removeEventListener("fullscreenchange",x)});const x=()=>{Xt(()=>{P(),n.value&&(n.value.innerHTML=""),E()})};return Qn([()=>window.innerWidth,()=>window.innerHeight,()=>{var g;return(g=t.value)==null?void 0:g.clientWidth},()=>{var g;return(g=t.value)==null?void 0:g.clientHeight}],()=>{Xt(P)}),(g,_)=>(re(),ce("div",{class:"holographic-container",ref_key:"container",ref:t},[_[3]||(_[3]=L("div",{class:"slogan-container"},[L("div",null,"对党忠诚  服务人民"),L("div",null,"执法公正  纪律严明")],-1)),_[4]||(_[4]=L("div",{class:"title-container"},[L("div",{class:"holographic-title"},"岳池县公安局情报指挥中心")],-1)),L("div",wf,[(re(),ce("svg",xf,[_[0]||(_[0]=L("circle",{cx:"50",cy:"50",r:"45",fill:"rgba(0,0,0,0)",stroke:"#00e5ff","stroke-width":"0.5"},null,-1)),_[1]||(_[1]=L("circle",{cx:"50",cy:"50",r:"42",fill:"rgba(5,15,44,0.5)"},null,-1)),L("g",bf,[(re(),ce(He,null,et(12,v=>L("line",{key:v,x1:50+38*Math.cos((v*30-90)*Math.PI/180),y1:50+38*Math.sin((v*30-90)*Math.PI/180),x2:50+42*Math.cos((v*30-90)*Math.PI/180),y2:50+42*Math.sin((v*30-90)*Math.PI/180),stroke:"#00e5ff","stroke-width":"1.5"},null,8,Ef)),64))]),L("g",Sf,[L("line",{class:"hour-hand",x1:50,y1:50,x2:50+20*Math.cos((s.value*30+a.value*.5-90)*Math.PI/180),y2:50+20*Math.sin((s.value*30+a.value*.5-90)*Math.PI/180),stroke:"#9e4edd","stroke-width":"3","stroke-linecap":"round"},null,8,kf),L("line",{class:"minute-hand",x1:50,y1:50,x2:50+30*Math.cos((a.value*6+u.value*.1-90)*Math.PI/180),y2:50+30*Math.sin((a.value*6+u.value*.1-90)*Math.PI/180),stroke:"#ff4d4d","stroke-width":"2","stroke-linecap":"round"},null,8,Rf),L("line",{class:"second-hand",x1:50,y1:50,x2:50+38*Math.cos((u.value*6+c.value*.006-90)*Math.PI/180),y2:50+38*Math.sin((u.value*6+c.value*.006-90)*Math.PI/180),stroke:"#00e5ff","stroke-width":"1","stroke-linecap":"round"},null,8,Cf)]),_[2]||(_[2]=L("circle",{cx:"50",cy:"50",r:"2",fill:"#fff"},null,-1))]))]),L("div",{ref_key:"particlesContainer",ref:n,class:"particles-container"},null,512),_[5]||(_[5]=L("div",{class:"hologram-grid"},null,-1)),_[6]||(_[6]=L("div",{class:"scan-line-vertical"},null,-1)),_[7]||(_[7]=L("div",{class:"scan-line-horizontal"},null,-1)),_[8]||(_[8]=L("div",{class:"hologram-frame"},[L("div")],-1)),(re(!0),ce(He,null,et(d.value,(v,w)=>(re(),ce("div",{key:v.id,id:`${v.id}-container`,class:Po(["map-container",{"map-visible":r.value===w}])},[(re(),ce("svg",{id:`${v.id}-map`,class:"map-svg"},null,8,Pf)),r.value===w?(re(),ce("div",{key:0,class:"location-marker",style:Zn({left:l.value.x+"px",top:l.value.y+"px"})},null,4)):Gt("",!0)],10,$f))),128))],512))}},Mf={class:"app-management-container"},Af={class:"clearfix"},Nf={class:"text-center"},If={class:"mt-4"},Tf={__name:"AppManagement",setup(e){const t=z([]),n=z([]),r=z(0),i=z(!1),o=z(!1),l=z(""),s=z(null),a=ze({id:null,name:"",url:"",isPublic:"",roles:[]}),u=ze({name:[{required:!0,message:"请输入应用名称",trigger:"blur"}],url:[{required:!0,message:"请输入应用URL",trigger:"blur"}],isPublic:[{required:!0,message:"请选择可见范围",trigger:"change"}],roles:[{type:"array",required:!0,validator:(S,E,M)=>{E&&E.length>0?M():M(new Error("至少选择一个角色组"))},trigger:"change"}]}),c=S=>{try{return JSON.parse(S).map(M=>{const V=n.value.find(x=>String(x.value)===String(M));return V?V.label:M})}catch(E){return console.error("解析 roleList 失败:",E),[]}},f=async()=>{try{const S=new FormData;S.append("controlCode","query");const E=await fe.post("/api/application_manage.php",S);E.status===1&&(t.value=E.data.application,n.value=E.data.rolelist.map(M=>({value:M.id,label:M.roleName})),r.value=E.data.application.length)}catch(S){console.error("获取应用列表失败:",S),Q.error("获取应用列表失败")}};at(()=>{f()});const d=S=>{switch(S){case"0":return"公开";case"1":return"非第三方人员";case"2":return"民警";case"3":return"授权用户";default:return"未知"}},p=()=>{k(),i.value=!0},m=S=>{if(k(),n.value.length===0){console.error("角色选项未加载完成"),Q.error("角色数据加载中，请稍后再试");return}a.id=S.id,a.name=S.application_name,a.url=S.url,a.isPublic=S.public;try{const E=JSON.parse(S.roleList);a.roles=E.map(M=>String(M)),Xt(()=>{a.roles=[...a.roles]})}catch(E){console.error("解析角色列表失败:",E),a.roles=[]}i.value=!0},k=()=>{s.value&&s.value.resetFields(),a.id=null,a.name="",a.url="",a.isPublic="",a.roles=[]},C=S=>{l.value=S.application_name,a.id=S.id,o.value=!0},A=async()=>{try{const S=new FormData;S.append("controlCode","del"),S.append("id",a.id);const E=await fe.post("/api/application_manage.php",S);E.status===1?(Q.success("删除成功"),o.value=!1,f()):Q.error("删除失败: "+(E.message||"未知错误"))}catch(S){console.error("删除应用失败:",S),Q.error("删除应用失败: "+S.message)}},P=async()=>{try{await s.value.validate()}catch(S){console.error("表单验证失败:",S);return}try{const S=new FormData;S.append("controlCode",a.id?"modify":"add"),S.append("id",a.id),S.append("application_name",a.name),S.append("url",a.url),S.append("public",a.isPublic),S.append("roleList",JSON.stringify(a.roles));const E=await fe.post("/api/application_manage.php",S);E.status===1?(Q.success("更新成功"),i.value=!1,f()):Q.error(E.message||"更新失败")}catch(S){console.error("更新应用失败:",S),Q.error("更新应用失败: "+S.message)}};return(S,E)=>{const M=O("el-button"),V=O("el-table-column"),x=O("el-tag"),g=O("el-icon"),_=O("el-button-group"),v=O("el-table"),w=O("el-card"),T=O("el-input"),N=O("el-form-item"),D=O("el-option"),b=O("el-select"),F=O("el-checkbox"),G=O("el-checkbox-group"),Z=O("el-form"),j=O("el-dialog");return re(),ce("div",Mf,[h(w,{class:"box-card"},{header:y(()=>[L("div",Af,[h(M,{style:{float:"right"},round:"",type:"primary",onClick:p},{default:y(()=>E[8]||(E[8]=[W(" 添加应用 ")])),_:1,__:[8]})])]),default:y(()=>[h(v,{data:t.value,stripe:"",border:"",fit:"","highlight-current-row":"",onRowDblclick:m,style:{width:"100%"}},{default:y(()=>[h(V,{prop:"application_name",label:"应用名称","min-width":"120"}),h(V,{prop:"url",label:"URL","min-width":"150"}),h(V,{prop:"public",label:"是否公开",width:"150",align:"center"},{default:y($=>[W(Pe(d($.row.public)),1)]),_:1}),h(V,{prop:"roles",label:"授权角色组","min-width":"180"},{default:y($=>[(re(!0),ce(He,null,et(c($.row.roleList),B=>(re(),Fe(x,{key:B,size:"small",type:"info"},{default:y(()=>[W(Pe(B),1)]),_:2},1024))),128))]),_:1}),h(V,{label:"操作",width:"160",align:"center"},{default:y($=>[h(_,null,{default:y(()=>[h(M,{size:"mini",type:"warning",onClick:B=>m($.row)},{default:y(()=>[h(g,null,{default:y(()=>[h(ne(Jn))]),_:1})]),_:2},1032,["onClick"]),h(M,{size:"mini",type:"danger",onClick:B=>C($.row)},{default:y(()=>[h(g,null,{default:y(()=>[h(ne(jn))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])]),_:1}),h(j,{modelValue:i.value,"onUpdate:modelValue":E[5]||(E[5]=$=>i.value=$),title:a.id?"编辑应用":"添加应用",width:"50%"},{footer:y(()=>[h(M,{onClick:E[4]||(E[4]=$=>i.value=!1)},{default:y(()=>E[9]||(E[9]=[W("取消")])),_:1,__:[9]}),h(M,{type:"primary",onClick:P},{default:y(()=>E[10]||(E[10]=[W("保存")])),_:1,__:[10]})]),default:y(()=>[h(Z,{model:a,rules:u,ref_key:"formRef",ref:s,"label-width":"100px"},{default:y(()=>[h(N,{label:"应用名称",prop:"name"},{default:y(()=>[h(T,{modelValue:a.name,"onUpdate:modelValue":E[0]||(E[0]=$=>a.name=$),placeholder:"请输入应用名称"},null,8,["modelValue"])]),_:1}),h(N,{label:"URL",prop:"url"},{default:y(()=>[h(T,{modelValue:a.url,"onUpdate:modelValue":E[1]||(E[1]=$=>a.url=$),placeholder:"请输入应用URL"},null,8,["modelValue"])]),_:1}),h(N,{label:"是否公开",prop:"isPublic"},{default:y(()=>[h(b,{modelValue:a.isPublic,"onUpdate:modelValue":E[2]||(E[2]=$=>a.isPublic=$),placeholder:"请选择是否公开"},{default:y(()=>[h(D,{value:"0",label:"公开"}),h(D,{value:"1",label:"非第三方人员"}),h(D,{value:"2",label:"民警"}),h(D,{value:"3",label:"授权用户"})]),_:1},8,["modelValue"])]),_:1}),h(N,{label:"授权角色组",prop:"roles"},{default:y(()=>[h(G,{modelValue:a.roles,"onUpdate:modelValue":E[3]||(E[3]=$=>a.roles=$)},{default:y(()=>[(re(!0),ce(He,null,et(n.value,$=>(re(),Fe(F,{key:$.value,label:$.value},{default:y(()=>[W(Pe($.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),h(j,{modelValue:o.value,"onUpdate:modelValue":E[7]||(E[7]=$=>o.value=$),title:"提示",width:"25%"},{footer:y(()=>[h(M,{onClick:E[6]||(E[6]=$=>o.value=!1)},{default:y(()=>E[11]||(E[11]=[W("取消")])),_:1,__:[11]}),h(M,{type:"danger",onClick:A},{default:y(()=>E[12]||(E[12]=[W("确认删除")])),_:1,__:[12]})]),default:y(()=>[L("div",Nf,[h(g,{size:"40",color:"red"},{default:y(()=>[h(ne(Vo))]),_:1}),L("p",If,'确定要删除应用 "'+Pe(l.value)+'" 吗？',1)])]),_:1},8,["modelValue"])])}}},Uf=sn(Tf,[["__scopeId","data-v-952fcaf2"]]),Df={class:"role-management-container"},Of={class:"toolbar flex justify-between items-center mb-4"},Lf={__name:"RoleManagement",setup(e){const t=z([]),n=async()=>{try{const p=new FormData;p.append("controlCode","query");const m=await fe.post("/api/role_List_manage.php",p);m.status===1?t.value=m.data.map(k=>({id:k.id,name:k.roleName,desc:k.roleDesc})):Q.error(m.message||"获取角色列表失败")}catch(p){console.error("获取角色列表失败:",p),Q.error("获取角色列表失败")}};at(()=>{n()});const r=z(!1),i=z(!1),o=ze({id:null,name:""}),l=z(null),s=()=>{o.id=null,o.name="",r.value=!0},a=p=>{o.id=p.id,o.name=p.name,r.value=!0},u=p=>{a(p)},c=p=>{l.value=p.id,i.value=!0},f=async()=>{if(!o.name){Q.error("角色名称不能为空");return}try{const p=new FormData;p.append("controlCode",o.id?"modify":"add"),p.append("id",o.id||""),p.append("roleName",o.name);const m=await fe.post("/api/role_List_manage.php",p);m.status===1?(Q.success(o.id?"更新成功":"添加成功"),r.value=!1,n()):Q.error(m.message||(o.id?"更新失败":"添加失败"))}catch(p){console.error("操作失败:",p),Q.error("操作失败: "+p.message)}},d=async()=>{if(l.value)try{const p=new FormData;p.append("controlCode","del"),p.append("id",l.value);const m=await fe.post("/api/role_List_manage.php",p);m.status===1?(Q.success("删除成功"),i.value=!1,n()):Q.error(m.message||"删除失败")}catch(p){console.error("删除失败:",p),Q.error("删除失败: "+p.message)}};return(p,m)=>{const k=O("el-icon"),C=O("el-button"),A=O("el-table-column"),P=O("el-table"),S=O("el-input"),E=O("el-form-item"),M=O("el-form"),V=O("el-dialog");return re(),ce("div",Df,[L("div",Of,[m[4]||(m[4]=L("h3",{class:"text-xl font-semibold"},"角色管理",-1)),h(C,{type:"primary",onClick:s},{default:y(()=>[h(k,null,{default:y(()=>[h(ne(hi))]),_:1}),m[3]||(m[3]=W(" 添加角色 "))]),_:1,__:[3]})]),h(P,{data:t.value,style:{width:"100%"}},{default:y(()=>[h(A,{prop:"name",label:"角色名称"}),h(A,{label:"操作"},{default:y(x=>[h(C,{type:"primary",size:"small",onClick:g=>a(x.row)},{default:y(()=>[h(k,null,{default:y(()=>[h(ne(Jn))]),_:1}),m[5]||(m[5]=W(" 编辑 "))]),_:2,__:[5]},1032,["onClick"]),h(C,{type:"danger",size:"small",onClick:g=>c(x.row)},{default:y(()=>[h(k,null,{default:y(()=>[h(ne(jn))]),_:1}),m[6]||(m[6]=W(" 删除 "))]),_:2,__:[6]},1032,["onClick"]),h(C,{type:"warning",size:"small",onClick:g=>u(x.row)},{default:y(()=>[h(k,null,{default:y(()=>[h(ne(Mo))]),_:1}),m[7]||(m[7]=W(" 修改 "))]),_:2,__:[7]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),h(V,{visible:r.value,title:"角色管理"},{content:y(()=>[h(M,{model:o,"label-width":"100px"},{default:y(()=>[h(E,{label:"角色名称",prop:"name"},{default:y(()=>[h(S,{modelValue:o.name,"onUpdate:modelValue":m[0]||(m[0]=x=>o.name=x)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),footer:y(()=>[h(C,{onClick:m[1]||(m[1]=x=>r.value=!1)},{default:y(()=>m[8]||(m[8]=[W("取消")])),_:1,__:[8]}),h(C,{type:"primary",onClick:f},{default:y(()=>m[9]||(m[9]=[W("保存")])),_:1,__:[9]})]),_:1},8,["visible"]),h(V,{visible:i.value,title:"确认删除"},{content:y(()=>m[10]||(m[10]=[L("p",null,"确定要删除该角色吗？",-1)])),footer:y(()=>[h(C,{onClick:m[2]||(m[2]=x=>i.value=!1)},{default:y(()=>m[11]||(m[11]=[W("取消")])),_:1,__:[11]}),h(C,{type:"danger",onClick:d},{default:y(()=>m[12]||(m[12]=[W("确认删除")])),_:1,__:[12]})]),_:1},8,["visible"])])}}},Ff=sn(Lf,[["__scopeId","data-v-4be1d71d"]]),qf=[{path:"/unit-management",name:"UnitManagement",component:Jl},{path:"/user-management",name:"UserManagement",component:ha},{path:"/visual-screen",name:"VisualScreen",component:Vf},{path:"/app-management",name:"AppManagement",component:Uf},{path:"/role-management",name:"RoleManagement",component:Ff}],zf=Ul({history:fl(),routes:qf}),hn=Ao(Wl);hn.use(No);for(const[e,t]of Object.entries(Io))hn.component(e,t);hn.use(zf);hn.mount("#app");
