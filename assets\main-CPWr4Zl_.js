import{s as vo,d as li,u as at,a as mo,c as ht,p as hn,r as B,w as Kn,h as ui,n as En,i as Wt,b as Wn,_ as ci,o as on,e as bt,f as yt,g as F,j as P,k as N,l as Y,m as _o,F as ve,q as ot,t as yo,v as Qt,x as ut,y as ze,z as de,A as wo,B as fi,C as sr,D as lr,E as Ct,G as xo,H as Ue,I as Qn,J as Eo,K as So,L as Ro,M as bo,N as ko,O as Po,P as Co,Q as $o,R as Mo,S as Ao,T as No}from"./index-DqwVT1Jm.js";/*!
  * vue-router v4.5.1
  * (c) 2025 <PERSON>
  * @license MIT
  */const Xt=typeof document<"u";function hi(t){return typeof t=="object"||"displayName"in t||"props"in t||"__vccOpts"in t}function Io(t){return t.__esModule||t[Symbol.toStringTag]==="Module"||t.default&&hi(t.default)}const G=Object.assign;function dn(t,e){const n={};for(const r in e){const i=e[r];n[r]=St(i)?i.map(t):t(i)}return n}const pe=()=>{},St=Array.isArray,di=/#/g,To=/&/g,Oo=/\//g,Vo=/=/g,Do=/\?/g,pi=/\+/g,Lo=/%5B/g,Fo=/%5D/g,gi=/%5E/g,qo=/%60/g,vi=/%7B/g,Ho=/%7C/g,mi=/%7D/g,zo=/%20/g;function Zn(t){return encodeURI(""+t).replace(Ho,"|").replace(Lo,"[").replace(Fo,"]")}function Uo(t){return Zn(t).replace(vi,"{").replace(mi,"}").replace(gi,"^")}function Sn(t){return Zn(t).replace(pi,"%2B").replace(zo,"+").replace(di,"%23").replace(To,"%26").replace(qo,"`").replace(vi,"{").replace(mi,"}").replace(gi,"^")}function Bo(t){return Sn(t).replace(Vo,"%3D")}function Xo(t){return Zn(t).replace(di,"%23").replace(Do,"%3F")}function Go(t){return t==null?"":Xo(t).replace(Oo,"%2F")}function me(t){try{return decodeURIComponent(""+t)}catch{}return""+t}const Yo=/\/$/,Ko=t=>t.replace(Yo,"");function pn(t,e,n="/"){let r,i={},o="",a="";const s=e.indexOf("#");let u=e.indexOf("?");return s<u&&s>=0&&(u=-1),u>-1&&(r=e.slice(0,u),o=e.slice(u+1,s>-1?s:e.length),i=t(o)),s>-1&&(r=r||e.slice(0,s),a=e.slice(s,e.length)),r=Jo(r??e,n),{fullPath:r+(o&&"?")+o+a,path:r,query:i,hash:me(a)}}function Wo(t,e){const n=e.query?t(e.query):"";return e.path+(n&&"?")+n+(e.hash||"")}function ur(t,e){return!e||!t.toLowerCase().startsWith(e.toLowerCase())?t:t.slice(e.length)||"/"}function Qo(t,e,n){const r=e.matched.length-1,i=n.matched.length-1;return r>-1&&r===i&&Zt(e.matched[r],n.matched[i])&&_i(e.params,n.params)&&t(e.query)===t(n.query)&&e.hash===n.hash}function Zt(t,e){return(t.aliasOf||t)===(e.aliasOf||e)}function _i(t,e){if(Object.keys(t).length!==Object.keys(e).length)return!1;for(const n in t)if(!Zo(t[n],e[n]))return!1;return!0}function Zo(t,e){return St(t)?cr(t,e):St(e)?cr(e,t):t===e}function cr(t,e){return St(e)?t.length===e.length&&t.every((n,r)=>n===e[r]):t.length===1&&t[0]===e}function Jo(t,e){if(t.startsWith("/"))return t;if(!t)return e;const n=e.split("/"),r=t.split("/"),i=r[r.length-1];(i===".."||i===".")&&r.push("");let o=n.length-1,a,s;for(a=0;a<r.length;a++)if(s=r[a],s!==".")if(s==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+r.slice(a).join("/")}const Ot={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var _e;(function(t){t.pop="pop",t.push="push"})(_e||(_e={}));var ge;(function(t){t.back="back",t.forward="forward",t.unknown=""})(ge||(ge={}));function jo(t){if(!t)if(Xt){const e=document.querySelector("base");t=e&&e.getAttribute("href")||"/",t=t.replace(/^\w+:\/\/[^\/]+/,"")}else t="/";return t[0]!=="/"&&t[0]!=="#"&&(t="/"+t),Ko(t)}const ta=/^[^#]+#/;function ea(t,e){return t.replace(ta,"#")+e}function na(t,e){const n=document.documentElement.getBoundingClientRect(),r=t.getBoundingClientRect();return{behavior:e.behavior,left:r.left-n.left-(e.left||0),top:r.top-n.top-(e.top||0)}}const an=()=>({left:window.scrollX,top:window.scrollY});function ra(t){let e;if("el"in t){const n=t.el,r=typeof n=="string"&&n.startsWith("#"),i=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!i)return;e=na(i,t)}else e=t;"scrollBehavior"in document.documentElement.style?window.scrollTo(e):window.scrollTo(e.left!=null?e.left:window.scrollX,e.top!=null?e.top:window.scrollY)}function fr(t,e){return(history.state?history.state.position-e:-1)+t}const Rn=new Map;function ia(t,e){Rn.set(t,e)}function oa(t){const e=Rn.get(t);return Rn.delete(t),e}let aa=()=>location.protocol+"//"+location.host;function yi(t,e){const{pathname:n,search:r,hash:i}=e,o=t.indexOf("#");if(o>-1){let s=i.includes(t.slice(o))?t.slice(o).length:1,u=i.slice(s);return u[0]!=="/"&&(u="/"+u),ur(u,"")}return ur(n,t)+r+i}function sa(t,e,n,r){let i=[],o=[],a=null;const s=({state:h})=>{const d=yi(t,location),g=n.value,x=e.value;let S=0;if(h){if(n.value=d,e.value=h,a&&a===g){a=null;return}S=x?h.position-x.position:0}else r(d);i.forEach(M=>{M(n.value,g,{delta:S,type:_e.pop,direction:S?S>0?ge.forward:ge.back:ge.unknown})})};function u(){a=n.value}function f(h){i.push(h);const d=()=>{const g=i.indexOf(h);g>-1&&i.splice(g,1)};return o.push(d),d}function l(){const{history:h}=window;h.state&&h.replaceState(G({},h.state,{scroll:an()}),"")}function c(){for(const h of o)h();o=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",l)}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:u,listen:f,destroy:c}}function hr(t,e,n,r=!1,i=!1){return{back:t,current:e,forward:n,replaced:r,position:window.history.length,scroll:i?an():null}}function la(t){const{history:e,location:n}=window,r={value:yi(t,n)},i={value:e.state};i.value||o(r.value,{back:null,current:r.value,forward:null,position:e.length-1,replaced:!0,scroll:null},!0);function o(u,f,l){const c=t.indexOf("#"),h=c>-1?(n.host&&document.querySelector("base")?t:t.slice(c))+u:aa()+t+u;try{e[l?"replaceState":"pushState"](f,"",h),i.value=f}catch(d){console.error(d),n[l?"replace":"assign"](h)}}function a(u,f){const l=G({},e.state,hr(i.value.back,u,i.value.forward,!0),f,{position:i.value.position});o(u,l,!0),r.value=u}function s(u,f){const l=G({},i.value,e.state,{forward:u,scroll:an()});o(l.current,l,!0);const c=G({},hr(r.value,u,null),{position:l.position+1},f);o(u,c,!1),r.value=u}return{location:r,state:i,push:s,replace:a}}function ua(t){t=jo(t);const e=la(t),n=sa(t,e.state,e.location,e.replace);function r(o,a=!0){a||n.pauseListeners(),history.go(o)}const i=G({location:"",base:t,go:r,createHref:ea.bind(null,t)},e,n);return Object.defineProperty(i,"location",{enumerable:!0,get:()=>e.location.value}),Object.defineProperty(i,"state",{enumerable:!0,get:()=>e.state.value}),i}function ca(t){return typeof t=="string"||t&&typeof t=="object"}function wi(t){return typeof t=="string"||typeof t=="symbol"}const xi=Symbol("");var dr;(function(t){t[t.aborted=4]="aborted",t[t.cancelled=8]="cancelled",t[t.duplicated=16]="duplicated"})(dr||(dr={}));function Jt(t,e){return G(new Error,{type:t,[xi]:!0},e)}function At(t,e){return t instanceof Error&&xi in t&&(e==null||!!(t.type&e))}const pr="[^/]+?",fa={sensitive:!1,strict:!1,start:!0,end:!0},ha=/[.+*?^${}()[\]/\\]/g;function da(t,e){const n=G({},fa,e),r=[];let i=n.start?"^":"";const o=[];for(const f of t){const l=f.length?[]:[90];n.strict&&!f.length&&(i+="/");for(let c=0;c<f.length;c++){const h=f[c];let d=40+(n.sensitive?.25:0);if(h.type===0)c||(i+="/"),i+=h.value.replace(ha,"\\$&"),d+=40;else if(h.type===1){const{value:g,repeatable:x,optional:S,regexp:M}=h;o.push({name:g,repeatable:x,optional:S});const v=M||pr;if(v!==pr){d+=10;try{new RegExp(`(${v})`)}catch(C){throw new Error(`Invalid custom RegExp for param "${g}" (${v}): `+C.message)}}let E=x?`((?:${v})(?:/(?:${v}))*)`:`(${v})`;c||(E=S&&f.length<2?`(?:/${E})`:"/"+E),S&&(E+="?"),i+=E,d+=20,S&&(d+=-8),x&&(d+=-20),v===".*"&&(d+=-50)}l.push(d)}r.push(l)}if(n.strict&&n.end){const f=r.length-1;r[f][r[f].length-1]+=.7000000000000001}n.strict||(i+="/?"),n.end?i+="$":n.strict&&!i.endsWith("/")&&(i+="(?:/|$)");const a=new RegExp(i,n.sensitive?"":"i");function s(f){const l=f.match(a),c={};if(!l)return null;for(let h=1;h<l.length;h++){const d=l[h]||"",g=o[h-1];c[g.name]=d&&g.repeatable?d.split("/"):d}return c}function u(f){let l="",c=!1;for(const h of t){(!c||!l.endsWith("/"))&&(l+="/"),c=!1;for(const d of h)if(d.type===0)l+=d.value;else if(d.type===1){const{value:g,repeatable:x,optional:S}=d,M=g in f?f[g]:"";if(St(M)&&!x)throw new Error(`Provided param "${g}" is an array but it is not repeatable (* or + modifiers)`);const v=St(M)?M.join("/"):M;if(!v)if(S)h.length<2&&(l.endsWith("/")?l=l.slice(0,-1):c=!0);else throw new Error(`Missing required param "${g}"`);l+=v}}return l||"/"}return{re:a,score:r,keys:o,parse:s,stringify:u}}function pa(t,e){let n=0;for(;n<t.length&&n<e.length;){const r=e[n]-t[n];if(r)return r;n++}return t.length<e.length?t.length===1&&t[0]===80?-1:1:t.length>e.length?e.length===1&&e[0]===80?1:-1:0}function Ei(t,e){let n=0;const r=t.score,i=e.score;for(;n<r.length&&n<i.length;){const o=pa(r[n],i[n]);if(o)return o;n++}if(Math.abs(i.length-r.length)===1){if(gr(r))return 1;if(gr(i))return-1}return i.length-r.length}function gr(t){const e=t[t.length-1];return t.length>0&&e[e.length-1]<0}const ga={type:0,value:""},va=/[a-zA-Z0-9_]/;function ma(t){if(!t)return[[]];if(t==="/")return[[ga]];if(!t.startsWith("/"))throw new Error(`Invalid path "${t}"`);function e(d){throw new Error(`ERR (${n})/"${f}": ${d}`)}let n=0,r=n;const i=[];let o;function a(){o&&i.push(o),o=[]}let s=0,u,f="",l="";function c(){f&&(n===0?o.push({type:0,value:f}):n===1||n===2||n===3?(o.length>1&&(u==="*"||u==="+")&&e(`A repeatable param (${f}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:f,regexp:l,repeatable:u==="*"||u==="+",optional:u==="*"||u==="?"})):e("Invalid state to consume buffer"),f="")}function h(){f+=u}for(;s<t.length;){if(u=t[s++],u==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:u==="/"?(f&&c(),a()):u===":"?(c(),n=1):h();break;case 4:h(),n=r;break;case 1:u==="("?n=2:va.test(u)?h():(c(),n=0,u!=="*"&&u!=="?"&&u!=="+"&&s--);break;case 2:u===")"?l[l.length-1]=="\\"?l=l.slice(0,-1)+u:n=3:l+=u;break;case 3:c(),n=0,u!=="*"&&u!=="?"&&u!=="+"&&s--,l="";break;default:e("Unknown state");break}}return n===2&&e(`Unfinished custom RegExp for param "${f}"`),c(),a(),i}function _a(t,e,n){const r=da(ma(t.path),n),i=G(r,{record:t,parent:e,children:[],alias:[]});return e&&!i.record.aliasOf==!e.record.aliasOf&&e.children.push(i),i}function ya(t,e){const n=[],r=new Map;e=yr({strict:!1,end:!0,sensitive:!1},e);function i(c){return r.get(c)}function o(c,h,d){const g=!d,x=mr(c);x.aliasOf=d&&d.record;const S=yr(e,c),M=[x];if("alias"in c){const C=typeof c.alias=="string"?[c.alias]:c.alias;for(const $ of C)M.push(mr(G({},x,{components:d?d.record.components:x.components,path:$,aliasOf:d?d.record:x})))}let v,E;for(const C of M){const{path:$}=C;if(h&&$[0]!=="/"){const b=h.record.path,k=b[b.length-1]==="/"?"":"/";C.path=h.record.path+($&&k+$)}if(v=_a(C,h,S),d?d.alias.push(v):(E=E||v,E!==v&&E.alias.push(v),g&&c.name&&!_r(v)&&a(c.name)),Si(v)&&u(v),x.children){const b=x.children;for(let k=0;k<b.length;k++)o(b[k],v,d&&d.children[k])}d=d||v}return E?()=>{a(E)}:pe}function a(c){if(wi(c)){const h=r.get(c);h&&(r.delete(c),n.splice(n.indexOf(h),1),h.children.forEach(a),h.alias.forEach(a))}else{const h=n.indexOf(c);h>-1&&(n.splice(h,1),c.record.name&&r.delete(c.record.name),c.children.forEach(a),c.alias.forEach(a))}}function s(){return n}function u(c){const h=Ea(c,n);n.splice(h,0,c),c.record.name&&!_r(c)&&r.set(c.record.name,c)}function f(c,h){let d,g={},x,S;if("name"in c&&c.name){if(d=r.get(c.name),!d)throw Jt(1,{location:c});S=d.record.name,g=G(vr(h.params,d.keys.filter(E=>!E.optional).concat(d.parent?d.parent.keys.filter(E=>E.optional):[]).map(E=>E.name)),c.params&&vr(c.params,d.keys.map(E=>E.name))),x=d.stringify(g)}else if(c.path!=null)x=c.path,d=n.find(E=>E.re.test(x)),d&&(g=d.parse(x),S=d.record.name);else{if(d=h.name?r.get(h.name):n.find(E=>E.re.test(h.path)),!d)throw Jt(1,{location:c,currentLocation:h});S=d.record.name,g=G({},h.params,c.params),x=d.stringify(g)}const M=[];let v=d;for(;v;)M.unshift(v.record),v=v.parent;return{name:S,path:x,params:g,matched:M,meta:xa(M)}}t.forEach(c=>o(c));function l(){n.length=0,r.clear()}return{addRoute:o,resolve:f,removeRoute:a,clearRoutes:l,getRoutes:s,getRecordMatcher:i}}function vr(t,e){const n={};for(const r of e)r in t&&(n[r]=t[r]);return n}function mr(t){const e={path:t.path,redirect:t.redirect,name:t.name,meta:t.meta||{},aliasOf:t.aliasOf,beforeEnter:t.beforeEnter,props:wa(t),children:t.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in t?t.components||null:t.component&&{default:t.component}};return Object.defineProperty(e,"mods",{value:{}}),e}function wa(t){const e={},n=t.props||!1;if("component"in t)e.default=n;else for(const r in t.components)e[r]=typeof n=="object"?n[r]:n;return e}function _r(t){for(;t;){if(t.record.aliasOf)return!0;t=t.parent}return!1}function xa(t){return t.reduce((e,n)=>G(e,n.meta),{})}function yr(t,e){const n={};for(const r in t)n[r]=r in e?e[r]:t[r];return n}function Ea(t,e){let n=0,r=e.length;for(;n!==r;){const o=n+r>>1;Ei(t,e[o])<0?r=o:n=o+1}const i=Sa(t);return i&&(r=e.lastIndexOf(i,r-1)),r}function Sa(t){let e=t;for(;e=e.parent;)if(Si(e)&&Ei(t,e)===0)return e}function Si({record:t}){return!!(t.name||t.components&&Object.keys(t.components).length||t.redirect)}function Ra(t){const e={};if(t===""||t==="?")return e;const r=(t[0]==="?"?t.slice(1):t).split("&");for(let i=0;i<r.length;++i){const o=r[i].replace(pi," "),a=o.indexOf("="),s=me(a<0?o:o.slice(0,a)),u=a<0?null:me(o.slice(a+1));if(s in e){let f=e[s];St(f)||(f=e[s]=[f]),f.push(u)}else e[s]=u}return e}function wr(t){let e="";for(let n in t){const r=t[n];if(n=Bo(n),r==null){r!==void 0&&(e+=(e.length?"&":"")+n);continue}(St(r)?r.map(o=>o&&Sn(o)):[r&&Sn(r)]).forEach(o=>{o!==void 0&&(e+=(e.length?"&":"")+n,o!=null&&(e+="="+o))})}return e}function ba(t){const e={};for(const n in t){const r=t[n];r!==void 0&&(e[n]=St(r)?r.map(i=>i==null?null:""+i):r==null?r:""+r)}return e}const ka=Symbol(""),xr=Symbol(""),sn=Symbol(""),Ri=Symbol(""),bn=Symbol("");function ie(){let t=[];function e(r){return t.push(r),()=>{const i=t.indexOf(r);i>-1&&t.splice(i,1)}}function n(){t=[]}return{add:e,list:()=>t.slice(),reset:n}}function Vt(t,e,n,r,i,o=a=>a()){const a=r&&(r.enterCallbacks[i]=r.enterCallbacks[i]||[]);return()=>new Promise((s,u)=>{const f=h=>{h===!1?u(Jt(4,{from:n,to:e})):h instanceof Error?u(h):ca(h)?u(Jt(2,{from:e,to:h})):(a&&r.enterCallbacks[i]===a&&typeof h=="function"&&a.push(h),s())},l=o(()=>t.call(r&&r.instances[i],e,n,f));let c=Promise.resolve(l);t.length<3&&(c=c.then(f)),c.catch(h=>u(h))})}function gn(t,e,n,r,i=o=>o()){const o=[];for(const a of t)for(const s in a.components){let u=a.components[s];if(!(e!=="beforeRouteEnter"&&!a.instances[s]))if(hi(u)){const l=(u.__vccOpts||u)[e];l&&o.push(Vt(l,n,r,a,s,i))}else{let f=u();o.push(()=>f.then(l=>{if(!l)throw new Error(`Couldn't resolve component "${s}" at "${a.path}"`);const c=Io(l)?l.default:l;a.mods[s]=l,a.components[s]=c;const d=(c.__vccOpts||c)[e];return d&&Vt(d,n,r,a,s,i)()}))}}return o}function Er(t){const e=Wt(sn),n=Wt(Ri),r=ht(()=>{const u=at(t.to);return e.resolve(u)}),i=ht(()=>{const{matched:u}=r.value,{length:f}=u,l=u[f-1],c=n.matched;if(!l||!c.length)return-1;const h=c.findIndex(Zt.bind(null,l));if(h>-1)return h;const d=Sr(u[f-2]);return f>1&&Sr(l)===d&&c[c.length-1].path!==d?c.findIndex(Zt.bind(null,u[f-2])):h}),o=ht(()=>i.value>-1&&Aa(n.params,r.value.params)),a=ht(()=>i.value>-1&&i.value===n.matched.length-1&&_i(n.params,r.value.params));function s(u={}){if(Ma(u)){const f=e[at(t.replace)?"replace":"push"](at(t.to)).catch(pe);return t.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>f),f}return Promise.resolve()}return{route:r,href:ht(()=>r.value.href),isActive:o,isExactActive:a,navigate:s}}function Pa(t){return t.length===1?t[0]:t}const Ca=li({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Er,setup(t,{slots:e}){const n=Wn(Er(t)),{options:r}=Wt(sn),i=ht(()=>({[Rr(t.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[Rr(t.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=e.default&&Pa(e.default(n));return t.custom?o:ui("a",{"aria-current":n.isExactActive?t.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:i.value},o)}}}),$a=Ca;function Ma(t){if(!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)&&!t.defaultPrevented&&!(t.button!==void 0&&t.button!==0)){if(t.currentTarget&&t.currentTarget.getAttribute){const e=t.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(e))return}return t.preventDefault&&t.preventDefault(),!0}}function Aa(t,e){for(const n in e){const r=e[n],i=t[n];if(typeof r=="string"){if(r!==i)return!1}else if(!St(i)||i.length!==r.length||r.some((o,a)=>o!==i[a]))return!1}return!0}function Sr(t){return t?t.aliasOf?t.aliasOf.path:t.path:""}const Rr=(t,e,n)=>t??e??n,Na=li({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(t,{attrs:e,slots:n}){const r=Wt(bn),i=ht(()=>t.route||r.value),o=Wt(xr,0),a=ht(()=>{let f=at(o);const{matched:l}=i.value;let c;for(;(c=l[f])&&!c.components;)f++;return f}),s=ht(()=>i.value.matched[a.value]);hn(xr,ht(()=>a.value+1)),hn(ka,s),hn(bn,i);const u=B();return Kn(()=>[u.value,s.value,t.name],([f,l,c],[h,d,g])=>{l&&(l.instances[c]=f,d&&d!==l&&f&&f===h&&(l.leaveGuards.size||(l.leaveGuards=d.leaveGuards),l.updateGuards.size||(l.updateGuards=d.updateGuards))),f&&l&&(!d||!Zt(l,d)||!h)&&(l.enterCallbacks[c]||[]).forEach(x=>x(f))},{flush:"post"}),()=>{const f=i.value,l=t.name,c=s.value,h=c&&c.components[l];if(!h)return br(n.default,{Component:h,route:f});const d=c.props[l],g=d?d===!0?f.params:typeof d=="function"?d(f):d:null,S=ui(h,G({},g,e,{onVnodeUnmounted:M=>{M.component.isUnmounted&&(c.instances[l]=null)},ref:u}));return br(n.default,{Component:S,route:f})||S}}});function br(t,e){if(!t)return null;const n=t(e);return n.length===1?n[0]:n}const Ia=Na;function Ta(t){const e=ya(t.routes,t),n=t.parseQuery||Ra,r=t.stringifyQuery||wr,i=t.history,o=ie(),a=ie(),s=ie(),u=vo(Ot);let f=Ot;Xt&&t.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const l=dn.bind(null,w=>""+w),c=dn.bind(null,Go),h=dn.bind(null,me);function d(w,I){let A,O;return wi(w)?(A=e.getRecordMatcher(w),O=I):O=w,e.addRoute(O,A)}function g(w){const I=e.getRecordMatcher(w);I&&e.removeRoute(I)}function x(){return e.getRoutes().map(w=>w.record)}function S(w){return!!e.getRecordMatcher(w)}function M(w,I){if(I=G({},I||u.value),typeof w=="string"){const H=pn(n,w,I.path),it=e.resolve({path:H.path},I),re=i.createHref(H.fullPath);return G(H,it,{params:h(it.params),hash:me(H.hash),redirectedFrom:void 0,href:re})}let A;if(w.path!=null)A=G({},w,{path:pn(n,w.path,I.path).path});else{const H=G({},w.params);for(const it in H)H[it]==null&&delete H[it];A=G({},w,{params:c(H)}),I.params=c(I.params)}const O=e.resolve(A,I),Q=w.hash||"";O.params=l(h(O.params));const nt=Wo(r,G({},w,{hash:Uo(Q),path:O.path})),z=i.createHref(nt);return G({fullPath:nt,hash:Q,query:r===wr?ba(w.query):w.query||{}},O,{redirectedFrom:void 0,href:z})}function v(w){return typeof w=="string"?pn(n,w,u.value.path):G({},w)}function E(w,I){if(f!==w)return Jt(8,{from:I,to:w})}function C(w){return k(w)}function $(w){return C(G(v(w),{replace:!0}))}function b(w){const I=w.matched[w.matched.length-1];if(I&&I.redirect){const{redirect:A}=I;let O=typeof A=="function"?A(w):A;return typeof O=="string"&&(O=O.includes("?")||O.includes("#")?O=v(O):{path:O},O.params={}),G({query:w.query,hash:w.hash,params:O.path!=null?{}:w.params},O)}}function k(w,I){const A=f=M(w),O=u.value,Q=w.state,nt=w.force,z=w.replace===!0,H=b(A);if(H)return k(G(v(H),{state:typeof H=="object"?G({},Q,H.state):Q,force:nt,replace:z}),I||A);const it=A;it.redirectedFrom=I;let re;return!nt&&Qo(r,O,A)&&(re=Jt(16,{to:it,from:O}),X(O,O,!0,!1)),(re?Promise.resolve(re):m(it,O)).catch(ft=>At(ft)?At(ft,2)?ft:gt(ft):W(ft,it,O)).then(ft=>{if(ft){if(At(ft,2))return k(G({replace:z},v(ft.to),{state:typeof ft.to=="object"?G({},Q,ft.to.state):Q,force:nt}),I||it)}else ft=T(it,O,!0,z,Q);return _(it,O,ft),ft})}function R(w,I){const A=E(w,I);return A?Promise.reject(A):Promise.resolve()}function p(w){const I=q.values().next().value;return I&&typeof I.runWithContext=="function"?I.runWithContext(w):w()}function m(w,I){let A;const[O,Q,nt]=Oa(w,I);A=gn(O.reverse(),"beforeRouteLeave",w,I);for(const H of O)H.leaveGuards.forEach(it=>{A.push(Vt(it,w,I))});const z=R.bind(null,w,I);return A.push(z),Bt(A).then(()=>{A=[];for(const H of o.list())A.push(Vt(H,w,I));return A.push(z),Bt(A)}).then(()=>{A=gn(Q,"beforeRouteUpdate",w,I);for(const H of Q)H.updateGuards.forEach(it=>{A.push(Vt(it,w,I))});return A.push(z),Bt(A)}).then(()=>{A=[];for(const H of nt)if(H.beforeEnter)if(St(H.beforeEnter))for(const it of H.beforeEnter)A.push(Vt(it,w,I));else A.push(Vt(H.beforeEnter,w,I));return A.push(z),Bt(A)}).then(()=>(w.matched.forEach(H=>H.enterCallbacks={}),A=gn(nt,"beforeRouteEnter",w,I,p),A.push(z),Bt(A))).then(()=>{A=[];for(const H of a.list())A.push(Vt(H,w,I));return A.push(z),Bt(A)}).catch(H=>At(H,8)?H:Promise.reject(H))}function _(w,I,A){s.list().forEach(O=>p(()=>O(w,I,A)))}function T(w,I,A,O,Q){const nt=E(w,I);if(nt)return nt;const z=I===Ot,H=Xt?history.state:{};A&&(O||z?i.replace(w.fullPath,G({scroll:z&&H&&H.scroll},Q)):i.push(w.fullPath,Q)),u.value=w,X(w,I,A,z),gt()}let L;function V(){L||(L=i.listen((w,I,A)=>{if(!lt.listening)return;const O=M(w),Q=b(O);if(Q){k(G(Q,{replace:!0,force:!0}),O).catch(pe);return}f=O;const nt=u.value;Xt&&ia(fr(nt.fullPath,A.delta),an()),m(O,nt).catch(z=>At(z,12)?z:At(z,2)?(k(G(v(z.to),{force:!0}),O).then(H=>{At(H,20)&&!A.delta&&A.type===_e.pop&&i.go(-1,!1)}).catch(pe),Promise.reject()):(A.delta&&i.go(-A.delta,!1),W(z,O,nt))).then(z=>{z=z||T(O,nt,!1),z&&(A.delta&&!At(z,8)?i.go(-A.delta,!1):A.type===_e.pop&&At(z,20)&&i.go(-1,!1)),_(O,nt,z)}).catch(pe)}))}let y=ie(),D=ie(),K;function W(w,I,A){gt(w);const O=D.list();return O.length?O.forEach(Q=>Q(w,I,A)):console.error(w),Promise.reject(w)}function j(){return K&&u.value!==Ot?Promise.resolve():new Promise((w,I)=>{y.add([w,I])})}function gt(w){return K||(K=!w,V(),y.list().forEach(([I,A])=>w?A(w):I()),y.reset()),w}function X(w,I,A,O){const{scrollBehavior:Q}=t;if(!Xt||!Q)return Promise.resolve();const nt=!A&&oa(fr(w.fullPath,0))||(O||!A)&&history.state&&history.state.scroll||null;return En().then(()=>Q(w,I,nt)).then(z=>z&&ra(z)).catch(z=>W(z,w,I))}const ct=w=>i.go(w);let st;const q=new Set,lt={currentRoute:u,listening:!0,addRoute:d,removeRoute:g,clearRoutes:e.clearRoutes,hasRoute:S,getRoutes:x,resolve:M,options:t,push:C,replace:$,go:ct,back:()=>ct(-1),forward:()=>ct(1),beforeEach:o.add,beforeResolve:a.add,afterEach:s.add,onError:D.add,isReady:j,install(w){const I=this;w.component("RouterLink",$a),w.component("RouterView",Ia),w.config.globalProperties.$router=I,Object.defineProperty(w.config.globalProperties,"$route",{enumerable:!0,get:()=>at(u)}),Xt&&!st&&u.value===Ot&&(st=!0,C(i.location).catch(Q=>{}));const A={};for(const Q in Ot)Object.defineProperty(A,Q,{get:()=>u.value[Q],enumerable:!0});w.provide(sn,I),w.provide(Ri,mo(A)),w.provide(bn,u);const O=w.unmount;q.add(w),w.unmount=function(){q.delete(w),q.size<1&&(f=Ot,L&&L(),L=null,u.value=Ot,st=!1,K=!1),O()}}};function Bt(w){return w.reduce((I,A)=>I.then(()=>p(A)),Promise.resolve())}return lt}function Oa(t,e){const n=[],r=[],i=[],o=Math.max(e.matched.length,t.matched.length);for(let a=0;a<o;a++){const s=e.matched[a];s&&(t.matched.find(f=>Zt(f,s))?r.push(s):n.push(s));const u=t.matched[a];u&&(e.matched.find(f=>Zt(f,u))||i.push(u))}return[n,r,i]}function Va(){return Wt(sn)}const Da={class:"app-container"},La={class:"grid-container"},Fa={class:"header"},qa={class:"header-left"},Ha={class:"user-info"},za={class:"el-dropdown-link"},Ua={class:"user-name"},Ba={class:"sidebar"},Xa={class:"content",ref:"contentRef"},Ga={__name:"App",setup(t){const e=Va(),n=B(""),r=B([]),i=B(!1),o=B(""),a=B(null);on(async()=>{await s(),await u(),e.push("/unit-management")});const s=async()=>{const b=await bt.post("/api/get_user_info.php");n.value=b.user.name},u=async()=>{const b=await bt.post("/api/get_user_app.php");r.value=b.data},f=b=>{console.log("点击的菜单对应的路由是:",b),e.push(b)},l=()=>{i.value=!i.value},c=B(!1),h=B({oldPassword:"",newPassword:"",confirmPassword:""}),d=B(null),g=B(!1),x=B(!1),S=()=>{g.value=!g.value},M=()=>{x.value=!x.value},v=async b=>{b==="logout"?(await bt.get("/api/logout.php"),window.location.href="login.html"):b==="changePassword"&&(c.value=!0)},E=async()=>{d.value&&await d.value.validate(b=>{if(b){if(h.value.newPassword!==h.value.confirmPassword){Ct.error("两次输入的密码不一致");return}const k=new FormData;k.append("old_password",h.value.oldPassword),k.append("new_password",h.value.newPassword),bt.post("/api/change_password.php",k).then(()=>{Ct.success("密码修改成功，请重新登录"),c.value=!1,h.value={oldPassword:"",newPassword:"",confirmPassword:""},setTimeout(()=>{window.location.href="login.html"},3e3)}).catch(()=>{Ct.error("密码修改失败")})}})},C=Wn({oldPassword:[{required:!0,message:"请输入旧密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"}],confirmPassword:[{required:!0,message:"请输入确认新密码",trigger:"blur"},{validator:(b,k,R)=>{k!==h.value.newPassword?R(new Error("两次输入的密码不一致")):R()},trigger:"blur"}]}),$=()=>{const b=a.value;b&&(document.fullscreenElement?document.exitFullscreen():b.requestFullscreen().catch(k=>{console.error("全屏失败:",k),Ct.error("全屏功能不支持")}))};return(b,k)=>{const R=Y("el-button"),p=Y("el-icon"),m=Y("el-avatar"),_=Y("el-dropdown-item"),T=Y("el-dropdown-menu"),L=Y("el-dropdown"),V=Y("el-menu-item"),y=Y("el-menu"),D=Y("router-view"),K=Y("el-input"),W=Y("el-form-item"),j=Y("el-form"),gt=Y("el-dialog");return ot(),yt(ve,null,[F("div",Da,[F("div",La,[F("div",Fa,[F("div",qa,[P(R,{onClick:l,type:"text",class:"menu-btn"},{default:N(()=>k[5]||(k[5]=[F("i",{class:"el-icon-menu"},null,-1)])),_:1,__:[5]}),k[6]||(k[6]=F("img",{src:_o,alt:"Logo",class:"header-logo"},null,-1)),k[7]||(k[7]=F("span",{class:"logo"},"CloudPivot",-1))])]),F("div",Ha,[P(L,{onCommand:v},{dropdown:N(()=>[P(T,null,{default:N(()=>[P(_,{command:"profile"},{default:N(()=>k[8]||(k[8]=[ut("个人信息")])),_:1,__:[8]}),P(_,{command:"changePassword"},{default:N(()=>k[9]||(k[9]=[ut("修改密码")])),_:1,__:[9]}),P(_,{command:"logout"},{default:N(()=>k[10]||(k[10]=[ut("退出登录")])),_:1,__:[10]})]),_:1})]),default:N(()=>[F("span",za,[P(m,{size:"small"},{default:N(()=>[P(p,null,{default:N(()=>[P(at(yo),{style:{color:"#409EFF"}})]),_:1})]),_:1}),F("span",Ua,Qt(n.value),1)])]),_:1})]),F("div",Ba,[P(y,{"default-active":o.value,class:"el-menu-vertical",mode:"vertical",collapse:i.value,onOpen:b.handleOpen,onClose:b.handleClose},{default:N(()=>[(ot(!0),yt(ve,null,ze(r.value,X=>(ot(),de(V,{key:X.id,index:X.id.toString(),router:X.url,onClick:ct=>f(X.url)},{title:N(()=>[F("span",null,Qt(X.application_name),1)]),_:2},1032,["index","router","onClick"]))),128))]),_:1},8,["default-active","collapse","onOpen","onClose"])]),F("div",Xa,[P(R,{onClick:$,type:"text",class:"fullscreen-btn"},{default:N(()=>[P(p,null,{default:N(()=>[P(at(wo))]),_:1})]),_:1}),F("div",{class:"fullscreen-target",ref_key:"fullscreenTargetRef",ref:a},[P(D,{ref:"routerViewRef"},null,512)],512)],512)])]),P(gt,{modelValue:c.value,"onUpdate:modelValue":k[4]||(k[4]=X=>c.value=X),width:"400px"},{default:N(()=>[P(j,{model:h.value,ref_key:"passwordFormRef",ref:d,rules:C,"label-width":"120px",onSubmit:fi(E,["prevent"])},{default:N(()=>[P(W,{label:"旧密码",prop:"oldPassword",required:""},{default:N(()=>[P(K,{modelValue:h.value.oldPassword,"onUpdate:modelValue":k[0]||(k[0]=X=>h.value.oldPassword=X),type:"password",placeholder:"请输入旧密码"},null,8,["modelValue"])]),_:1}),P(W,{label:"新密码",prop:"newPassword",required:""},{default:N(()=>[P(K,{modelValue:h.value.newPassword,"onUpdate:modelValue":k[1]||(k[1]=X=>h.value.newPassword=X),type:g.value?"text":"password",placeholder:"请输入新密码"},{suffix:N(()=>[P(R,{icon:g.value?at(sr):at(lr),onClick:S,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),P(W,{label:"确认新密码",prop:"confirmPassword",required:""},{default:N(()=>[P(K,{modelValue:h.value.confirmPassword,"onUpdate:modelValue":k[2]||(k[2]=X=>h.value.confirmPassword=X),type:x.value?"text":"password",placeholder:"请输入确认新密码"},{suffix:N(()=>[P(R,{icon:x.value?at(sr):at(lr),onClick:M,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),P(W,null,{default:N(()=>[P(R,{type:"primary","native-type":"submit"},{default:N(()=>k[11]||(k[11]=[ut("确定")])),_:1,__:[11]}),P(R,{onClick:k[3]||(k[3]=X=>c.value=!1)},{default:N(()=>k[12]||(k[12]=[ut("取消")])),_:1,__:[12]})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])],64)}}},Ya=ci(Ga,[["__scopeId","data-v-727c4254"]]),Ka={class:"unit-management"},Wa={style:{"text-align":"right",margin:"10px"}},Qa={__name:"UnitManagement",setup(t){const e=B(!1),n={expandTrigger:"hover",checkStrictly:!0,value:"id",label:"unit_name",children:"children"},r=p=>{if(p&&p.length>0){const m=p[p.length-1],_=o(m);_&&(i.parentId=m,c.value=_.children||[])}else i.parentId=null,c.value=[]},i=Wn({id:null,name:"",parentId:null,parentIdPath:[],code:"",sort:"top"});Kn(()=>i.parentId,(p,m)=>{if(console.log("parentId 发生变化，旧值: ",m,"新值: ",p),p){const _=o(p);_&&(c.value=_.children||[])}else c.value=[]},{immediate:!1});const o=(p,m=a.value)=>{for(let _=0;_<m.length;_++){if(m[_].id===p)return m[_];if(m[_].children){const T=o(p,m[_].children);if(T)return T}}return null},a=B([]),s=B(new Set),u=ht(()=>{const p=[],m=(_,T=0,L=null)=>{_.forEach(V=>{p.push({...V,level:T,parentId:L,expanded:s.value.has(V.id)}),V.children&&m(V.children,T+1,V.id)})};return m(a.value),p}),f=ht(()=>{const p=[],m=_=>{if(_.level===0)return!0;let T=u.value.find(L=>L.id===_.parentId);for(;T;){if(!T.expanded)return!1;T=u.value.find(L=>L.id===T.parentId)}return!0};return u.value.forEach(_=>{m(_)&&p.push(_)}),p}),l=B(!1),c=B([]),h=B(null);on(async()=>{await v()});const d=B(!1),g=p=>{const m=[];function _(T,L){for(const V of T){const y=[...L,V.id];if(V.id===p)return m.push(...y),!0;if(V.children&&V.children.length>0&&_(V.children,y))return!0}return!1}return _(a.value,[]),m},x=p=>{if(h.value&&h.value.resetFields(),e.value=!1,i.id=null,i.name="",i.code="",i.parentId=null,i.parentIdPath=[],i.sort="top",d.value=!!p,p){const m=o(p);m&&(i.parentId=p,console.log("parentId位置一:",p),i.parentIdPath=g(p),c.value=m.children||[])}else i.parentId=null,i.parentIdPath=[],c.value=[];l.value=!0,console.log("dialogVisible 已设为 true")},S=p=>{h.value&&h.value.resetFields(),e.value=!0,i.id=p.id,i.name=p.unit_name,i.code=p.code,i.parentId=p.parent_id,i.parentIdPath=g(p.parent_id);const m=o(p.parent_id);m?c.value=m.children||[]:c.value=a.value,c.value.some(_=>_.id===p.sort_after_id)?i.sort=p.sort_after_id:i.sort="top",console.log("formData.sort:",i.sort),l.value=!0},M=()=>{h.value.validate(async p=>{if(p)try{let m=0;i.sort!=="top"&&(m=i.sort);const _=new FormData;i.id?(_.append("action","edit"),_.append("id",i.id)):_.append("action","add"),_.append("unit_name",i.name),_.append("code",i.code),console.log("formData.parentId:",i.parentId),_.append("parent_id",i.parentId||null),_.append("after_unit_id",m);const T=await bt.post("api/unit_manage.php",_,{headers:{"Content-Type":"multipart/form-data"}});T.status==="success"?(await v(),l.value=!1,Ct.success(i.id?"编辑成功":"新增成功")):Ct.error(T.message)}catch(m){console.error("保存单位失败:",m),Ct.error("保存单位失败，请稍后重试")}})},v=async()=>{const p=await bt.post("api/get_unit_info.php");a.value=[p.data],console.log("获取单位数据成功:",a.value);const m=_=>{_.forEach(T=>{T.children&&T.children.length>0&&(s.value.add(T.id),m(T.children))})};m(a.value)},E=p=>{Po.confirm(`确定要删除 ${p.unit_name} 吗？`,"Warning",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const m=new FormData;m.append("action","del"),m.append("id",p.id),await bt.post("api/unit_manage.php",m),Ct.success("删除成功"),await v()})},C=p=>{const m=new Set(s.value);m.has(p.id)?m.delete(p.id):m.add(p.id),s.value=m},$=p=>((p.parentId?o(p.parentId):{children:a.value}).children||[]).findIndex(L=>L.id===p.id)===0,b=p=>{const _=(p.parentId?o(p.parentId):{children:a.value}).children||[];return _.findIndex(L=>L.id===p.id)===_.length-1},k=async p=>{const m=new FormData;m.append("action","edit"),m.append("id",p.id),m.append("sort_order",p.sort_order-1),m.append("unit_name",p.unit_name),m.append("code",p.code),m.append("parent_id",p.parentId),await bt.post("api/unit_manage.php",m),await v()},R=async p=>{const m=new FormData;m.append("action","edit"),m.append("id",p.id),m.append("sort_order",p.sort_order+1),m.append("unit_name",p.unit_name),m.append("code",p.code),m.append("parent_id",p.parentId),await bt.post("api/unit_manage.php",m),await v()};return(p,m)=>{const _=Y("el-button"),T=Y("el-col"),L=Y("el-row"),V=Y("el-table-column"),y=Y("el-icon"),D=Y("el-button-group"),K=Y("el-table"),W=Y("el-input"),j=Y("el-form-item"),gt=Y("el-cascader"),X=Y("el-option"),ct=Y("el-select"),st=Y("el-form");return ot(),yt("div",Ka,[P(L,null,{default:N(()=>[P(T,{span:24},{default:N(()=>[F("div",Wa,[P(_,{type:"primary",round:"",onClick:m[0]||(m[0]=q=>x(null))},{default:N(()=>m[7]||(m[7]=[ut("添加单位")])),_:1,__:[7]})])]),_:1})]),_:1}),P(K,{data:f.value,style:{width:"100%"}},{default:N(()=>[P(V,{label:"操作",width:"100"},{default:N(q=>[q.row.children&&q.row.children.length?(ot(),de(_,{key:0,size:"mini",type:"text",onClick:lt=>C(q.row)},{default:N(()=>[ut(Qt(q.row.expanded?"−":"+"),1)]),_:2},1032,["onClick"])):Ue("",!0)]),_:1}),P(V,{prop:"unit_name",label:"单位名"},{default:N(q=>[F("span",{style:Qn({paddingLeft:`${q.row.level*20}px`})},Qt(q.row.unit_name),5)]),_:1}),P(V,{prop:"code",label:"单位编号",width:"250"}),P(V,{label:"操作",width:"350"},{default:N(q=>[P(D,null,{default:N(()=>[P(_,{size:"mini",type:"primary",onClick:lt=>x(q.row.id)},{default:N(()=>[P(y,null,{default:N(()=>[P(at(Eo))]),_:1})]),_:2},1032,["onClick"]),P(_,{size:"mini",type:"warning",onClick:lt=>S(q.row)},{default:N(()=>[P(y,null,{default:N(()=>[P(at(So))]),_:1})]),_:2},1032,["onClick"]),P(_,{size:"mini",type:"danger",onClick:lt=>E(q.row)},{default:N(()=>[P(y,null,{default:N(()=>[P(at(Ro))]),_:1})]),_:2},1032,["onClick"]),P(_,{size:"mini",type:"info",onClick:lt=>k(q.row),disabled:$(q.row)},{default:N(()=>[P(y,null,{default:N(()=>[P(at(bo))]),_:1})]),_:2},1032,["onClick","disabled"]),P(_,{size:"mini",type:"info",onClick:lt=>R(q.row),disabled:b(q.row)},{default:N(()=>[P(y,null,{default:N(()=>[P(at(ko))]),_:1})]),_:2},1032,["onClick","disabled"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),P(at(xo),{modelValue:l.value,"onUpdate:modelValue":m[6]||(m[6]=q=>l.value=q),title:"",width:"450px","close-on-click-modal":!1},{footer:N(()=>[P(_,{onClick:m[5]||(m[5]=q=>l.value=!1)},{default:N(()=>m[8]||(m[8]=[ut("取消")])),_:1,__:[8]}),P(_,{type:"primary",onClick:M},{default:N(()=>m[9]||(m[9]=[ut("确定")])),_:1,__:[9]})]),default:N(()=>[P(st,{model:i,ref_key:"formRef",ref:h,"label-width":"130px"},{default:N(()=>[P(j,{label:"单位名称",prop:"name",required:""},{default:N(()=>[P(W,{modelValue:i.name,"onUpdate:modelValue":m[1]||(m[1]=q=>i.name=q),required:""},null,8,["modelValue"])]),_:1}),P(j,{label:"单位编码",prop:"code",required:""},{default:N(()=>[P(W,{modelValue:i.code,"onUpdate:modelValue":m[2]||(m[2]=q=>i.code=q),required:""},null,8,["modelValue"])]),_:1}),P(j,{label:"上级单位",prop:"parentId"},{default:N(()=>[P(gt,{modelValue:i.parentIdPath,"onUpdate:modelValue":m[3]||(m[3]=q=>i.parentIdPath=q),options:a.value,props:n,onChange:r,style:{width:"100%"},"show-all-levels":!1,placeholder:"请选择上级单位"},null,8,["modelValue","options"])]),_:1}),e.value?Ue("",!0):(ot(),de(j,{key:0,label:"排序",prop:"sort",required:""},{default:N(()=>[P(ct,{modelValue:i.sort,"onUpdate:modelValue":m[4]||(m[4]=q=>i.sort=q),placeholder:"请选择排序位置"},{default:N(()=>[P(X,{label:"置于 最前",value:"top"}),(ot(!0),yt(ve,null,ze(c.value,q=>(ot(),de(X,{key:q.id,label:`置于 ${q.unit_name} 之后`,value:q.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}))]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Za={class:"user-management-container"},Ja={class:"left-panel"},ja=["onClick"],ts={class:"right-panel"},es={class:"action-buttons"},ns={__name:"UserManagement",setup(t){const e=B([]),n=B([]),r=B(""),i=B([]),o=B([]),a=ht(()=>i.value),s=ht(()=>r.value?n.value.filter(v=>v.show&&v.unit_name.toLowerCase().includes(r.value.toLowerCase())):n.value.filter(v=>v.show));on(async()=>{try{const v=await bt.post("api/get_unit_info.php");v.status==="success"?(e.value=[v.data],u(v.data),console.log("获取部门数据成功:",e.value)):Ct.error(v.message)}catch(v){console.error("获取部门数据失败:",v),Ct.error("获取部门数据失败，请稍后重试")}});const u=(v,E=0,C=null)=>{const $={...v,level:E,expanded:v.children&&v.children.length>0,parent:C,indent:E*20,show:!0};n.value.push($),v.children&&v.children.length>0&&v.children.forEach(b=>{u(b,E+1,$)})},f=v=>{v.expanded=!v.expanded;const E=n.value.indexOf(v)+1;let C=v.level+1;for(let $=E;$<n.value.length;$++){const b=n.value[$];if(b.level<=v.level)break;b.level===C?b.show=v.expanded:b.level>C&&(b.show=v.expanded&&n.value[$-1].show)}},l=async v=>{console.log("点击的部门:",v)},c=()=>{},h=()=>{},d=v=>{},g=v=>{},x=v=>{},S=v=>{o.value=v},M=v=>{console.log("点击的部门名称:",v)};return(v,E)=>{const C=Y("el-input"),$=Y("el-button"),b=Y("el-table-column"),k=Y("el-table");return ot(),yt("div",Za,[F("div",Ja,[P(C,{modelValue:r.value,"onUpdate:modelValue":E[0]||(E[0]=R=>r.value=R),placeholder:"搜索部门",class:"department-search"},null,8,["modelValue"]),P(k,{data:s.value,border:"",class:"department-table",onRowClick:l},{default:N(()=>[P(b,{label:"操作",width:"80"},{default:N(({row:R})=>[R.children&&R.children.length>0?(ot(),de($,{key:0,type:"text",size:"small",onClick:fi(p=>f(R),["stop"])},{default:N(()=>[ut(Qt(R.expanded?"-":"+"),1)]),_:2},1032,["onClick"])):Ue("",!0)]),_:1}),P(b,{prop:"unit_name",label:"部门名称"},{default:N(({row:R})=>[F("span",{class:"indent",style:Qn({width:`${R.indent}px`})},null,4),F("span",{onClick:p=>M(R),style:{cursor:"pointer"}},Qt(R.unit_name),9,ja)]),_:1})]),_:1},8,["data"])]),F("div",ts,[F("div",es,[P($,{type:"primary",onClick:c},{default:N(()=>E[1]||(E[1]=[ut("新增")])),_:1,__:[1]}),P($,{type:"danger",onClick:h},{default:N(()=>E[2]||(E[2]=[ut("删除")])),_:1,__:[2]})]),P(k,{data:a.value,border:"",class:"user-table",onSelectionChange:S},{default:N(()=>[P(b,{type:"selection",width:"55"}),P(b,{prop:"index",label:"序号",width:"80"}),P(b,{prop:"username",label:"用户名"}),P(b,{prop:"account",label:"账号"}),P(b,{prop:"role",label:"角色"}),P(b,{prop:"email",label:"邮箱"}),P(b,{prop:"createTime",label:"创建时间"}),P(b,{label:"操作",width:"200"},{default:N(({row:R})=>[P($,{type:"primary",onClick:p=>d(R)},{default:N(()=>E[3]||(E[3]=[ut("编辑")])),_:2,__:[3]},1032,["onClick"]),P($,{type:"success",onClick:p=>g(R)},{default:N(()=>E[4]||(E[4]=[ut("详情")])),_:2,__:[4]},1032,["onClick"]),P($,{type:"danger",onClick:p=>x(R)},{default:N(()=>E[5]||(E[5]=[ut("删除")])),_:2,__:[5]},1032,["onClick"])]),_:1})]),_:1},8,["data"])])])}}},rs=ci(ns,[["__scopeId","data-v-800648a1"]]);class qt{constructor(){this._partials=new Float64Array(32),this._n=0}add(e){const n=this._partials;let r=0;for(let i=0;i<this._n&&i<32;i++){const o=n[i],a=e+o,s=Math.abs(e)<Math.abs(o)?e-(a-o):o-(a-e);s&&(n[r++]=s),e=a}return n[r]=e,this._n=r+1,this}valueOf(){const e=this._partials;let n=this._n,r,i,o,a=0;if(n>0){for(a=e[--n];n>0&&(r=a,i=e[--n],a=r+i,o=i-(a-r),!o););n>0&&(o<0&&e[n-1]<0||o>0&&e[n-1]>0)&&(i=o*2,r=a+i,i==r-a&&(a=r))}return a}}function*is(t){for(const e of t)yield*e}function bi(t){return Array.from(is(t))}var os={value:()=>{}};function ki(){for(var t=0,e=arguments.length,n={},r;t<e;++t){if(!(r=arguments[t]+"")||r in n||/[\s.]/.test(r))throw new Error("illegal type: "+r);n[r]=[]}return new Le(n)}function Le(t){this._=t}function as(t,e){return t.trim().split(/^|\s+/).map(function(n){var r="",i=n.indexOf(".");if(i>=0&&(r=n.slice(i+1),n=n.slice(0,i)),n&&!e.hasOwnProperty(n))throw new Error("unknown type: "+n);return{type:n,name:r}})}Le.prototype=ki.prototype={constructor:Le,on:function(t,e){var n=this._,r=as(t+"",n),i,o=-1,a=r.length;if(arguments.length<2){for(;++o<a;)if((i=(t=r[o]).type)&&(i=ss(n[i],t.name)))return i;return}if(e!=null&&typeof e!="function")throw new Error("invalid callback: "+e);for(;++o<a;)if(i=(t=r[o]).type)n[i]=kr(n[i],t.name,e);else if(e==null)for(i in n)n[i]=kr(n[i],t.name,null);return this},copy:function(){var t={},e=this._;for(var n in e)t[n]=e[n].slice();return new Le(t)},call:function(t,e){if((i=arguments.length-2)>0)for(var n=new Array(i),r=0,i,o;r<i;++r)n[r]=arguments[r+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(o=this._[t],r=0,i=o.length;r<i;++r)o[r].value.apply(e,n)},apply:function(t,e,n){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(e,n)}};function ss(t,e){for(var n=0,r=t.length,i;n<r;++n)if((i=t[n]).name===e)return i.value}function kr(t,e,n){for(var r=0,i=t.length;r<i;++r)if(t[r].name===e){t[r]=os,t=t.slice(0,r).concat(t.slice(r+1));break}return n!=null&&t.push({name:e,value:n}),t}var kn="http://www.w3.org/1999/xhtml";const Pr={svg:"http://www.w3.org/2000/svg",xhtml:kn,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function ln(t){var e=t+="",n=e.indexOf(":");return n>=0&&(e=t.slice(0,n))!=="xmlns"&&(t=t.slice(n+1)),Pr.hasOwnProperty(e)?{space:Pr[e],local:t}:t}function ls(t){return function(){var e=this.ownerDocument,n=this.namespaceURI;return n===kn&&e.documentElement.namespaceURI===kn?e.createElement(t):e.createElementNS(n,t)}}function us(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}function Pi(t){var e=ln(t);return(e.local?us:ls)(e)}function cs(){}function Jn(t){return t==null?cs:function(){return this.querySelector(t)}}function fs(t){typeof t!="function"&&(t=Jn(t));for(var e=this._groups,n=e.length,r=new Array(n),i=0;i<n;++i)for(var o=e[i],a=o.length,s=r[i]=new Array(a),u,f,l=0;l<a;++l)(u=o[l])&&(f=t.call(u,u.__data__,l,o))&&("__data__"in u&&(f.__data__=u.__data__),s[l]=f);return new mt(r,this._parents)}function hs(t){return t==null?[]:Array.isArray(t)?t:Array.from(t)}function ds(){return[]}function Ci(t){return t==null?ds:function(){return this.querySelectorAll(t)}}function ps(t){return function(){return hs(t.apply(this,arguments))}}function gs(t){typeof t=="function"?t=ps(t):t=Ci(t);for(var e=this._groups,n=e.length,r=[],i=[],o=0;o<n;++o)for(var a=e[o],s=a.length,u,f=0;f<s;++f)(u=a[f])&&(r.push(t.call(u,u.__data__,f,a)),i.push(u));return new mt(r,i)}function $i(t){return function(){return this.matches(t)}}function Mi(t){return function(e){return e.matches(t)}}var vs=Array.prototype.find;function ms(t){return function(){return vs.call(this.children,t)}}function _s(){return this.firstElementChild}function ys(t){return this.select(t==null?_s:ms(typeof t=="function"?t:Mi(t)))}var ws=Array.prototype.filter;function xs(){return Array.from(this.children)}function Es(t){return function(){return ws.call(this.children,t)}}function Ss(t){return this.selectAll(t==null?xs:Es(typeof t=="function"?t:Mi(t)))}function Rs(t){typeof t!="function"&&(t=$i(t));for(var e=this._groups,n=e.length,r=new Array(n),i=0;i<n;++i)for(var o=e[i],a=o.length,s=r[i]=[],u,f=0;f<a;++f)(u=o[f])&&t.call(u,u.__data__,f,o)&&s.push(u);return new mt(r,this._parents)}function Ai(t){return new Array(t.length)}function bs(){return new mt(this._enter||this._groups.map(Ai),this._parents)}function Be(t,e){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=e}Be.prototype={constructor:Be,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,e){return this._parent.insertBefore(t,e)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};function ks(t){return function(){return t}}function Ps(t,e,n,r,i,o){for(var a=0,s,u=e.length,f=o.length;a<f;++a)(s=e[a])?(s.__data__=o[a],r[a]=s):n[a]=new Be(t,o[a]);for(;a<u;++a)(s=e[a])&&(i[a]=s)}function Cs(t,e,n,r,i,o,a){var s,u,f=new Map,l=e.length,c=o.length,h=new Array(l),d;for(s=0;s<l;++s)(u=e[s])&&(h[s]=d=a.call(u,u.__data__,s,e)+"",f.has(d)?i[s]=u:f.set(d,u));for(s=0;s<c;++s)d=a.call(t,o[s],s,o)+"",(u=f.get(d))?(r[s]=u,u.__data__=o[s],f.delete(d)):n[s]=new Be(t,o[s]);for(s=0;s<l;++s)(u=e[s])&&f.get(h[s])===u&&(i[s]=u)}function $s(t){return t.__data__}function Ms(t,e){if(!arguments.length)return Array.from(this,$s);var n=e?Cs:Ps,r=this._parents,i=this._groups;typeof t!="function"&&(t=ks(t));for(var o=i.length,a=new Array(o),s=new Array(o),u=new Array(o),f=0;f<o;++f){var l=r[f],c=i[f],h=c.length,d=As(t.call(l,l&&l.__data__,f,r)),g=d.length,x=s[f]=new Array(g),S=a[f]=new Array(g),M=u[f]=new Array(h);n(l,c,x,S,M,d,e);for(var v=0,E=0,C,$;v<g;++v)if(C=x[v]){for(v>=E&&(E=v+1);!($=S[E])&&++E<g;);C._next=$||null}}return a=new mt(a,r),a._enter=s,a._exit=u,a}function As(t){return typeof t=="object"&&"length"in t?t:Array.from(t)}function Ns(){return new mt(this._exit||this._groups.map(Ai),this._parents)}function Is(t,e,n){var r=this.enter(),i=this,o=this.exit();return typeof t=="function"?(r=t(r),r&&(r=r.selection())):r=r.append(t+""),e!=null&&(i=e(i),i&&(i=i.selection())),n==null?o.remove():n(o),r&&i?r.merge(i).order():i}function Ts(t){for(var e=t.selection?t.selection():t,n=this._groups,r=e._groups,i=n.length,o=r.length,a=Math.min(i,o),s=new Array(i),u=0;u<a;++u)for(var f=n[u],l=r[u],c=f.length,h=s[u]=new Array(c),d,g=0;g<c;++g)(d=f[g]||l[g])&&(h[g]=d);for(;u<i;++u)s[u]=n[u];return new mt(s,this._parents)}function Os(){for(var t=this._groups,e=-1,n=t.length;++e<n;)for(var r=t[e],i=r.length-1,o=r[i],a;--i>=0;)(a=r[i])&&(o&&a.compareDocumentPosition(o)^4&&o.parentNode.insertBefore(a,o),o=a);return this}function Vs(t){t||(t=Ds);function e(c,h){return c&&h?t(c.__data__,h.__data__):!c-!h}for(var n=this._groups,r=n.length,i=new Array(r),o=0;o<r;++o){for(var a=n[o],s=a.length,u=i[o]=new Array(s),f,l=0;l<s;++l)(f=a[l])&&(u[l]=f);u.sort(e)}return new mt(i,this._parents).order()}function Ds(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}function Ls(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this}function Fs(){return Array.from(this)}function qs(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var r=t[e],i=0,o=r.length;i<o;++i){var a=r[i];if(a)return a}return null}function Hs(){let t=0;for(const e of this)++t;return t}function zs(){return!this.node()}function Us(t){for(var e=this._groups,n=0,r=e.length;n<r;++n)for(var i=e[n],o=0,a=i.length,s;o<a;++o)(s=i[o])&&t.call(s,s.__data__,o,i);return this}function Bs(t){return function(){this.removeAttribute(t)}}function Xs(t){return function(){this.removeAttributeNS(t.space,t.local)}}function Gs(t,e){return function(){this.setAttribute(t,e)}}function Ys(t,e){return function(){this.setAttributeNS(t.space,t.local,e)}}function Ks(t,e){return function(){var n=e.apply(this,arguments);n==null?this.removeAttribute(t):this.setAttribute(t,n)}}function Ws(t,e){return function(){var n=e.apply(this,arguments);n==null?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,n)}}function Qs(t,e){var n=ln(t);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((e==null?n.local?Xs:Bs:typeof e=="function"?n.local?Ws:Ks:n.local?Ys:Gs)(n,e))}function Ni(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function Zs(t){return function(){this.style.removeProperty(t)}}function Js(t,e,n){return function(){this.style.setProperty(t,e,n)}}function js(t,e,n){return function(){var r=e.apply(this,arguments);r==null?this.style.removeProperty(t):this.style.setProperty(t,r,n)}}function tl(t,e,n){return arguments.length>1?this.each((e==null?Zs:typeof e=="function"?js:Js)(t,e,n??"")):jt(this.node(),t)}function jt(t,e){return t.style.getPropertyValue(e)||Ni(t).getComputedStyle(t,null).getPropertyValue(e)}function el(t){return function(){delete this[t]}}function nl(t,e){return function(){this[t]=e}}function rl(t,e){return function(){var n=e.apply(this,arguments);n==null?delete this[t]:this[t]=n}}function il(t,e){return arguments.length>1?this.each((e==null?el:typeof e=="function"?rl:nl)(t,e)):this.node()[t]}function Ii(t){return t.trim().split(/^|\s+/)}function jn(t){return t.classList||new Ti(t)}function Ti(t){this._node=t,this._names=Ii(t.getAttribute("class")||"")}Ti.prototype={add:function(t){var e=this._names.indexOf(t);e<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var e=this._names.indexOf(t);e>=0&&(this._names.splice(e,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};function Oi(t,e){for(var n=jn(t),r=-1,i=e.length;++r<i;)n.add(e[r])}function Vi(t,e){for(var n=jn(t),r=-1,i=e.length;++r<i;)n.remove(e[r])}function ol(t){return function(){Oi(this,t)}}function al(t){return function(){Vi(this,t)}}function sl(t,e){return function(){(e.apply(this,arguments)?Oi:Vi)(this,t)}}function ll(t,e){var n=Ii(t+"");if(arguments.length<2){for(var r=jn(this.node()),i=-1,o=n.length;++i<o;)if(!r.contains(n[i]))return!1;return!0}return this.each((typeof e=="function"?sl:e?ol:al)(n,e))}function ul(){this.textContent=""}function cl(t){return function(){this.textContent=t}}function fl(t){return function(){var e=t.apply(this,arguments);this.textContent=e??""}}function hl(t){return arguments.length?this.each(t==null?ul:(typeof t=="function"?fl:cl)(t)):this.node().textContent}function dl(){this.innerHTML=""}function pl(t){return function(){this.innerHTML=t}}function gl(t){return function(){var e=t.apply(this,arguments);this.innerHTML=e??""}}function vl(t){return arguments.length?this.each(t==null?dl:(typeof t=="function"?gl:pl)(t)):this.node().innerHTML}function ml(){this.nextSibling&&this.parentNode.appendChild(this)}function _l(){return this.each(ml)}function yl(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function wl(){return this.each(yl)}function xl(t){var e=typeof t=="function"?t:Pi(t);return this.select(function(){return this.appendChild(e.apply(this,arguments))})}function El(){return null}function Sl(t,e){var n=typeof t=="function"?t:Pi(t),r=e==null?El:typeof e=="function"?e:Jn(e);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})}function Rl(){var t=this.parentNode;t&&t.removeChild(this)}function bl(){return this.each(Rl)}function kl(){var t=this.cloneNode(!1),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function Pl(){var t=this.cloneNode(!0),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function Cl(t){return this.select(t?Pl:kl)}function $l(t){return arguments.length?this.property("__data__",t):this.node().__data__}function Ml(t){return function(e){t.call(this,e,this.__data__)}}function Al(t){return t.trim().split(/^|\s+/).map(function(e){var n="",r=e.indexOf(".");return r>=0&&(n=e.slice(r+1),e=e.slice(0,r)),{type:e,name:n}})}function Nl(t){return function(){var e=this.__on;if(e){for(var n=0,r=-1,i=e.length,o;n<i;++n)o=e[n],(!t.type||o.type===t.type)&&o.name===t.name?this.removeEventListener(o.type,o.listener,o.options):e[++r]=o;++r?e.length=r:delete this.__on}}}function Il(t,e,n){return function(){var r=this.__on,i,o=Ml(e);if(r){for(var a=0,s=r.length;a<s;++a)if((i=r[a]).type===t.type&&i.name===t.name){this.removeEventListener(i.type,i.listener,i.options),this.addEventListener(i.type,i.listener=o,i.options=n),i.value=e;return}}this.addEventListener(t.type,o,n),i={type:t.type,name:t.name,value:e,listener:o,options:n},r?r.push(i):this.__on=[i]}}function Tl(t,e,n){var r=Al(t+""),i,o=r.length,a;if(arguments.length<2){var s=this.node().__on;if(s){for(var u=0,f=s.length,l;u<f;++u)for(i=0,l=s[u];i<o;++i)if((a=r[i]).type===l.type&&a.name===l.name)return l.value}return}for(s=e?Il:Nl,i=0;i<o;++i)this.each(s(r[i],e,n));return this}function Di(t,e,n){var r=Ni(t),i=r.CustomEvent;typeof i=="function"?i=new i(e,n):(i=r.document.createEvent("Event"),n?(i.initEvent(e,n.bubbles,n.cancelable),i.detail=n.detail):i.initEvent(e,!1,!1)),t.dispatchEvent(i)}function Ol(t,e){return function(){return Di(this,t,e)}}function Vl(t,e){return function(){return Di(this,t,e.apply(this,arguments))}}function Dl(t,e){return this.each((typeof e=="function"?Vl:Ol)(t,e))}function*Ll(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var r=t[e],i=0,o=r.length,a;i<o;++i)(a=r[i])&&(yield a)}var Li=[null];function mt(t,e){this._groups=t,this._parents=e}function Pe(){return new mt([[document.documentElement]],Li)}function Fl(){return this}mt.prototype=Pe.prototype={constructor:mt,select:fs,selectAll:gs,selectChild:ys,selectChildren:Ss,filter:Rs,data:Ms,enter:bs,exit:Ns,join:Is,merge:Ts,selection:Fl,order:Os,sort:Vs,call:Ls,nodes:Fs,node:qs,size:Hs,empty:zs,each:Us,attr:Qs,style:tl,property:il,classed:ll,text:hl,html:vl,raise:_l,lower:wl,append:xl,insert:Sl,remove:bl,clone:Cl,datum:$l,on:Tl,dispatch:Dl,[Symbol.iterator]:Ll};function $e(t){return typeof t=="string"?new mt([[document.querySelector(t)]],[document.documentElement]):new mt([[t]],Li)}function tr(t,e,n){t.prototype=e.prototype=n,n.constructor=t}function Fi(t,e){var n=Object.create(t.prototype);for(var r in e)n[r]=e[r];return n}function Ce(){}var ye=.7,Xe=1/ye,Kt="\\s*([+-]?\\d+)\\s*",we="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",$t="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",ql=/^#([0-9a-f]{3,8})$/,Hl=new RegExp(`^rgb\\(${Kt},${Kt},${Kt}\\)$`),zl=new RegExp(`^rgb\\(${$t},${$t},${$t}\\)$`),Ul=new RegExp(`^rgba\\(${Kt},${Kt},${Kt},${we}\\)$`),Bl=new RegExp(`^rgba\\(${$t},${$t},${$t},${we}\\)$`),Xl=new RegExp(`^hsl\\(${we},${$t},${$t}\\)$`),Gl=new RegExp(`^hsla\\(${we},${$t},${$t},${we}\\)$`),Cr={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};tr(Ce,xe,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:$r,formatHex:$r,formatHex8:Yl,formatHsl:Kl,formatRgb:Mr,toString:Mr});function $r(){return this.rgb().formatHex()}function Yl(){return this.rgb().formatHex8()}function Kl(){return qi(this).formatHsl()}function Mr(){return this.rgb().formatRgb()}function xe(t){var e,n;return t=(t+"").trim().toLowerCase(),(e=ql.exec(t))?(n=e[1].length,e=parseInt(e[1],16),n===6?Ar(e):n===3?new dt(e>>8&15|e>>4&240,e>>4&15|e&240,(e&15)<<4|e&15,1):n===8?Me(e>>24&255,e>>16&255,e>>8&255,(e&255)/255):n===4?Me(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|e&240,((e&15)<<4|e&15)/255):null):(e=Hl.exec(t))?new dt(e[1],e[2],e[3],1):(e=zl.exec(t))?new dt(e[1]*255/100,e[2]*255/100,e[3]*255/100,1):(e=Ul.exec(t))?Me(e[1],e[2],e[3],e[4]):(e=Bl.exec(t))?Me(e[1]*255/100,e[2]*255/100,e[3]*255/100,e[4]):(e=Xl.exec(t))?Tr(e[1],e[2]/100,e[3]/100,1):(e=Gl.exec(t))?Tr(e[1],e[2]/100,e[3]/100,e[4]):Cr.hasOwnProperty(t)?Ar(Cr[t]):t==="transparent"?new dt(NaN,NaN,NaN,0):null}function Ar(t){return new dt(t>>16&255,t>>8&255,t&255,1)}function Me(t,e,n,r){return r<=0&&(t=e=n=NaN),new dt(t,e,n,r)}function Wl(t){return t instanceof Ce||(t=xe(t)),t?(t=t.rgb(),new dt(t.r,t.g,t.b,t.opacity)):new dt}function Pn(t,e,n,r){return arguments.length===1?Wl(t):new dt(t,e,n,r??1)}function dt(t,e,n,r){this.r=+t,this.g=+e,this.b=+n,this.opacity=+r}tr(dt,Pn,Fi(Ce,{brighter(t){return t=t==null?Xe:Math.pow(Xe,t),new dt(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=t==null?ye:Math.pow(ye,t),new dt(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new dt(Ft(this.r),Ft(this.g),Ft(this.b),Ge(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Nr,formatHex:Nr,formatHex8:Ql,formatRgb:Ir,toString:Ir}));function Nr(){return`#${Lt(this.r)}${Lt(this.g)}${Lt(this.b)}`}function Ql(){return`#${Lt(this.r)}${Lt(this.g)}${Lt(this.b)}${Lt((isNaN(this.opacity)?1:this.opacity)*255)}`}function Ir(){const t=Ge(this.opacity);return`${t===1?"rgb(":"rgba("}${Ft(this.r)}, ${Ft(this.g)}, ${Ft(this.b)}${t===1?")":`, ${t})`}`}function Ge(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function Ft(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function Lt(t){return t=Ft(t),(t<16?"0":"")+t.toString(16)}function Tr(t,e,n,r){return r<=0?t=e=n=NaN:n<=0||n>=1?t=e=NaN:e<=0&&(t=NaN),new Et(t,e,n,r)}function qi(t){if(t instanceof Et)return new Et(t.h,t.s,t.l,t.opacity);if(t instanceof Ce||(t=xe(t)),!t)return new Et;if(t instanceof Et)return t;t=t.rgb();var e=t.r/255,n=t.g/255,r=t.b/255,i=Math.min(e,n,r),o=Math.max(e,n,r),a=NaN,s=o-i,u=(o+i)/2;return s?(e===o?a=(n-r)/s+(n<r)*6:n===o?a=(r-e)/s+2:a=(e-n)/s+4,s/=u<.5?o+i:2-o-i,a*=60):s=u>0&&u<1?0:a,new Et(a,s,u,t.opacity)}function Zl(t,e,n,r){return arguments.length===1?qi(t):new Et(t,e,n,r??1)}function Et(t,e,n,r){this.h=+t,this.s=+e,this.l=+n,this.opacity=+r}tr(Et,Zl,Fi(Ce,{brighter(t){return t=t==null?Xe:Math.pow(Xe,t),new Et(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=t==null?ye:Math.pow(ye,t),new Et(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*e,i=2*n-r;return new dt(vn(t>=240?t-240:t+120,i,r),vn(t,i,r),vn(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new Et(Or(this.h),Ae(this.s),Ae(this.l),Ge(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=Ge(this.opacity);return`${t===1?"hsl(":"hsla("}${Or(this.h)}, ${Ae(this.s)*100}%, ${Ae(this.l)*100}%${t===1?")":`, ${t})`}`}}));function Or(t){return t=(t||0)%360,t<0?t+360:t}function Ae(t){return Math.max(0,Math.min(1,t||0))}function vn(t,e,n){return(t<60?e+(n-e)*t/60:t<180?n:t<240?e+(n-e)*(240-t)/60:e)*255}const Hi=t=>()=>t;function Jl(t,e){return function(n){return t+n*e}}function jl(t,e,n){return t=Math.pow(t,n),e=Math.pow(e,n)-t,n=1/n,function(r){return Math.pow(t+r*e,n)}}function tu(t){return(t=+t)==1?zi:function(e,n){return n-e?jl(e,n,t):Hi(isNaN(e)?n:e)}}function zi(t,e){var n=e-t;return n?Jl(t,n):Hi(isNaN(t)?e:t)}const Vr=function t(e){var n=tu(e);function r(i,o){var a=n((i=Pn(i)).r,(o=Pn(o)).r),s=n(i.g,o.g),u=n(i.b,o.b),f=zi(i.opacity,o.opacity);return function(l){return i.r=a(l),i.g=s(l),i.b=u(l),i.opacity=f(l),i+""}}return r.gamma=t,r}(1);function Dt(t,e){return t=+t,e=+e,function(n){return t*(1-n)+e*n}}var Cn=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,mn=new RegExp(Cn.source,"g");function eu(t){return function(){return t}}function nu(t){return function(e){return t(e)+""}}function ru(t,e){var n=Cn.lastIndex=mn.lastIndex=0,r,i,o,a=-1,s=[],u=[];for(t=t+"",e=e+"";(r=Cn.exec(t))&&(i=mn.exec(e));)(o=i.index)>n&&(o=e.slice(n,o),s[a]?s[a]+=o:s[++a]=o),(r=r[0])===(i=i[0])?s[a]?s[a]+=i:s[++a]=i:(s[++a]=null,u.push({i:a,x:Dt(r,i)})),n=mn.lastIndex;return n<e.length&&(o=e.slice(n),s[a]?s[a]+=o:s[++a]=o),s.length<2?u[0]?nu(u[0].x):eu(e):(e=u.length,function(f){for(var l=0,c;l<e;++l)s[(c=u[l]).i]=c.x(f);return s.join("")})}var Dr=180/Math.PI,$n={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Ui(t,e,n,r,i,o){var a,s,u;return(a=Math.sqrt(t*t+e*e))&&(t/=a,e/=a),(u=t*n+e*r)&&(n-=t*u,r-=e*u),(s=Math.sqrt(n*n+r*r))&&(n/=s,r/=s,u/=s),t*r<e*n&&(t=-t,e=-e,u=-u,a=-a),{translateX:i,translateY:o,rotate:Math.atan2(e,t)*Dr,skewX:Math.atan(u)*Dr,scaleX:a,scaleY:s}}var Ne;function iu(t){const e=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(t+"");return e.isIdentity?$n:Ui(e.a,e.b,e.c,e.d,e.e,e.f)}function ou(t){return t==null||(Ne||(Ne=document.createElementNS("http://www.w3.org/2000/svg","g")),Ne.setAttribute("transform",t),!(t=Ne.transform.baseVal.consolidate()))?$n:(t=t.matrix,Ui(t.a,t.b,t.c,t.d,t.e,t.f))}function Bi(t,e,n,r){function i(f){return f.length?f.pop()+" ":""}function o(f,l,c,h,d,g){if(f!==c||l!==h){var x=d.push("translate(",null,e,null,n);g.push({i:x-4,x:Dt(f,c)},{i:x-2,x:Dt(l,h)})}else(c||h)&&d.push("translate("+c+e+h+n)}function a(f,l,c,h){f!==l?(f-l>180?l+=360:l-f>180&&(f+=360),h.push({i:c.push(i(c)+"rotate(",null,r)-2,x:Dt(f,l)})):l&&c.push(i(c)+"rotate("+l+r)}function s(f,l,c,h){f!==l?h.push({i:c.push(i(c)+"skewX(",null,r)-2,x:Dt(f,l)}):l&&c.push(i(c)+"skewX("+l+r)}function u(f,l,c,h,d,g){if(f!==c||l!==h){var x=d.push(i(d)+"scale(",null,",",null,")");g.push({i:x-4,x:Dt(f,c)},{i:x-2,x:Dt(l,h)})}else(c!==1||h!==1)&&d.push(i(d)+"scale("+c+","+h+")")}return function(f,l){var c=[],h=[];return f=t(f),l=t(l),o(f.translateX,f.translateY,l.translateX,l.translateY,c,h),a(f.rotate,l.rotate,c,h),s(f.skewX,l.skewX,c,h),u(f.scaleX,f.scaleY,l.scaleX,l.scaleY,c,h),f=l=null,function(d){for(var g=-1,x=h.length,S;++g<x;)c[(S=h[g]).i]=S.x(d);return c.join("")}}}var au=Bi(iu,"px, ","px)","deg)"),su=Bi(ou,", ",")",")"),te=0,ae=0,oe=0,Xi=1e3,Ye,se,Ke=0,Ht=0,un=0,Ee=typeof performance=="object"&&performance.now?performance:Date,Gi=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function er(){return Ht||(Gi(lu),Ht=Ee.now()+un)}function lu(){Ht=0}function We(){this._call=this._time=this._next=null}We.prototype=Yi.prototype={constructor:We,restart:function(t,e,n){if(typeof t!="function")throw new TypeError("callback is not a function");n=(n==null?er():+n)+(e==null?0:+e),!this._next&&se!==this&&(se?se._next=this:Ye=this,se=this),this._call=t,this._time=n,Mn()},stop:function(){this._call&&(this._call=null,this._time=1/0,Mn())}};function Yi(t,e,n){var r=new We;return r.restart(t,e,n),r}function uu(){er(),++te;for(var t=Ye,e;t;)(e=Ht-t._time)>=0&&t._call.call(void 0,e),t=t._next;--te}function Lr(){Ht=(Ke=Ee.now())+un,te=ae=0;try{uu()}finally{te=0,fu(),Ht=0}}function cu(){var t=Ee.now(),e=t-Ke;e>Xi&&(un-=e,Ke=t)}function fu(){for(var t,e=Ye,n,r=1/0;e;)e._call?(r>e._time&&(r=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:Ye=n);se=t,Mn(r)}function Mn(t){if(!te){ae&&(ae=clearTimeout(ae));var e=t-Ht;e>24?(t<1/0&&(ae=setTimeout(Lr,t-Ee.now()-un)),oe&&(oe=clearInterval(oe))):(oe||(Ke=Ee.now(),oe=setInterval(cu,Xi)),te=1,Gi(Lr))}}function Fr(t,e,n){var r=new We;return e=e==null?0:+e,r.restart(i=>{r.stop(),t(i+e)},e,n),r}var hu=ki("start","end","cancel","interrupt"),du=[],Ki=0,qr=1,An=2,Fe=3,Hr=4,Nn=5,qe=6;function cn(t,e,n,r,i,o){var a=t.__transition;if(!a)t.__transition={};else if(n in a)return;pu(t,n,{name:e,index:r,group:i,on:hu,tween:du,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:Ki})}function nr(t,e){var n=Rt(t,e);if(n.state>Ki)throw new Error("too late; already scheduled");return n}function Mt(t,e){var n=Rt(t,e);if(n.state>Fe)throw new Error("too late; already running");return n}function Rt(t,e){var n=t.__transition;if(!n||!(n=n[e]))throw new Error("transition not found");return n}function pu(t,e,n){var r=t.__transition,i;r[e]=n,n.timer=Yi(o,0,n.time);function o(f){n.state=qr,n.timer.restart(a,n.delay,n.time),n.delay<=f&&a(f-n.delay)}function a(f){var l,c,h,d;if(n.state!==qr)return u();for(l in r)if(d=r[l],d.name===n.name){if(d.state===Fe)return Fr(a);d.state===Hr?(d.state=qe,d.timer.stop(),d.on.call("interrupt",t,t.__data__,d.index,d.group),delete r[l]):+l<e&&(d.state=qe,d.timer.stop(),d.on.call("cancel",t,t.__data__,d.index,d.group),delete r[l])}if(Fr(function(){n.state===Fe&&(n.state=Hr,n.timer.restart(s,n.delay,n.time),s(f))}),n.state=An,n.on.call("start",t,t.__data__,n.index,n.group),n.state===An){for(n.state=Fe,i=new Array(h=n.tween.length),l=0,c=-1;l<h;++l)(d=n.tween[l].value.call(t,t.__data__,n.index,n.group))&&(i[++c]=d);i.length=c+1}}function s(f){for(var l=f<n.duration?n.ease.call(null,f/n.duration):(n.timer.restart(u),n.state=Nn,1),c=-1,h=i.length;++c<h;)i[c].call(t,l);n.state===Nn&&(n.on.call("end",t,t.__data__,n.index,n.group),u())}function u(){n.state=qe,n.timer.stop(),delete r[e];for(var f in r)return;delete t.__transition}}function gu(t,e){var n=t.__transition,r,i,o=!0,a;if(n){e=e==null?null:e+"";for(a in n){if((r=n[a]).name!==e){o=!1;continue}i=r.state>An&&r.state<Nn,r.state=qe,r.timer.stop(),r.on.call(i?"interrupt":"cancel",t,t.__data__,r.index,r.group),delete n[a]}o&&delete t.__transition}}function vu(t){return this.each(function(){gu(this,t)})}function mu(t,e){var n,r;return function(){var i=Mt(this,t),o=i.tween;if(o!==n){r=n=o;for(var a=0,s=r.length;a<s;++a)if(r[a].name===e){r=r.slice(),r.splice(a,1);break}}i.tween=r}}function _u(t,e,n){var r,i;if(typeof n!="function")throw new Error;return function(){var o=Mt(this,t),a=o.tween;if(a!==r){i=(r=a).slice();for(var s={name:e,value:n},u=0,f=i.length;u<f;++u)if(i[u].name===e){i[u]=s;break}u===f&&i.push(s)}o.tween=i}}function yu(t,e){var n=this._id;if(t+="",arguments.length<2){for(var r=Rt(this.node(),n).tween,i=0,o=r.length,a;i<o;++i)if((a=r[i]).name===t)return a.value;return null}return this.each((e==null?mu:_u)(n,t,e))}function rr(t,e,n){var r=t._id;return t.each(function(){var i=Mt(this,r);(i.value||(i.value={}))[e]=n.apply(this,arguments)}),function(i){return Rt(i,r).value[e]}}function Wi(t,e){var n;return(typeof e=="number"?Dt:e instanceof xe?Vr:(n=xe(e))?(e=n,Vr):ru)(t,e)}function wu(t){return function(){this.removeAttribute(t)}}function xu(t){return function(){this.removeAttributeNS(t.space,t.local)}}function Eu(t,e,n){var r,i=n+"",o;return function(){var a=this.getAttribute(t);return a===i?null:a===r?o:o=e(r=a,n)}}function Su(t,e,n){var r,i=n+"",o;return function(){var a=this.getAttributeNS(t.space,t.local);return a===i?null:a===r?o:o=e(r=a,n)}}function Ru(t,e,n){var r,i,o;return function(){var a,s=n(this),u;return s==null?void this.removeAttribute(t):(a=this.getAttribute(t),u=s+"",a===u?null:a===r&&u===i?o:(i=u,o=e(r=a,s)))}}function bu(t,e,n){var r,i,o;return function(){var a,s=n(this),u;return s==null?void this.removeAttributeNS(t.space,t.local):(a=this.getAttributeNS(t.space,t.local),u=s+"",a===u?null:a===r&&u===i?o:(i=u,o=e(r=a,s)))}}function ku(t,e){var n=ln(t),r=n==="transform"?su:Wi;return this.attrTween(t,typeof e=="function"?(n.local?bu:Ru)(n,r,rr(this,"attr."+t,e)):e==null?(n.local?xu:wu)(n):(n.local?Su:Eu)(n,r,e))}function Pu(t,e){return function(n){this.setAttribute(t,e.call(this,n))}}function Cu(t,e){return function(n){this.setAttributeNS(t.space,t.local,e.call(this,n))}}function $u(t,e){var n,r;function i(){var o=e.apply(this,arguments);return o!==r&&(n=(r=o)&&Cu(t,o)),n}return i._value=e,i}function Mu(t,e){var n,r;function i(){var o=e.apply(this,arguments);return o!==r&&(n=(r=o)&&Pu(t,o)),n}return i._value=e,i}function Au(t,e){var n="attr."+t;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(e==null)return this.tween(n,null);if(typeof e!="function")throw new Error;var r=ln(t);return this.tween(n,(r.local?$u:Mu)(r,e))}function Nu(t,e){return function(){nr(this,t).delay=+e.apply(this,arguments)}}function Iu(t,e){return e=+e,function(){nr(this,t).delay=e}}function Tu(t){var e=this._id;return arguments.length?this.each((typeof t=="function"?Nu:Iu)(e,t)):Rt(this.node(),e).delay}function Ou(t,e){return function(){Mt(this,t).duration=+e.apply(this,arguments)}}function Vu(t,e){return e=+e,function(){Mt(this,t).duration=e}}function Du(t){var e=this._id;return arguments.length?this.each((typeof t=="function"?Ou:Vu)(e,t)):Rt(this.node(),e).duration}function Lu(t,e){if(typeof e!="function")throw new Error;return function(){Mt(this,t).ease=e}}function Fu(t){var e=this._id;return arguments.length?this.each(Lu(e,t)):Rt(this.node(),e).ease}function qu(t,e){return function(){var n=e.apply(this,arguments);if(typeof n!="function")throw new Error;Mt(this,t).ease=n}}function Hu(t){if(typeof t!="function")throw new Error;return this.each(qu(this._id,t))}function zu(t){typeof t!="function"&&(t=$i(t));for(var e=this._groups,n=e.length,r=new Array(n),i=0;i<n;++i)for(var o=e[i],a=o.length,s=r[i]=[],u,f=0;f<a;++f)(u=o[f])&&t.call(u,u.__data__,f,o)&&s.push(u);return new Tt(r,this._parents,this._name,this._id)}function Uu(t){if(t._id!==this._id)throw new Error;for(var e=this._groups,n=t._groups,r=e.length,i=n.length,o=Math.min(r,i),a=new Array(r),s=0;s<o;++s)for(var u=e[s],f=n[s],l=u.length,c=a[s]=new Array(l),h,d=0;d<l;++d)(h=u[d]||f[d])&&(c[d]=h);for(;s<r;++s)a[s]=e[s];return new Tt(a,this._parents,this._name,this._id)}function Bu(t){return(t+"").trim().split(/^|\s+/).every(function(e){var n=e.indexOf(".");return n>=0&&(e=e.slice(0,n)),!e||e==="start"})}function Xu(t,e,n){var r,i,o=Bu(e)?nr:Mt;return function(){var a=o(this,t),s=a.on;s!==r&&(i=(r=s).copy()).on(e,n),a.on=i}}function Gu(t,e){var n=this._id;return arguments.length<2?Rt(this.node(),n).on.on(t):this.each(Xu(n,t,e))}function Yu(t){return function(){var e=this.parentNode;for(var n in this.__transition)if(+n!==t)return;e&&e.removeChild(this)}}function Ku(){return this.on("end.remove",Yu(this._id))}function Wu(t){var e=this._name,n=this._id;typeof t!="function"&&(t=Jn(t));for(var r=this._groups,i=r.length,o=new Array(i),a=0;a<i;++a)for(var s=r[a],u=s.length,f=o[a]=new Array(u),l,c,h=0;h<u;++h)(l=s[h])&&(c=t.call(l,l.__data__,h,s))&&("__data__"in l&&(c.__data__=l.__data__),f[h]=c,cn(f[h],e,n,h,f,Rt(l,n)));return new Tt(o,this._parents,e,n)}function Qu(t){var e=this._name,n=this._id;typeof t!="function"&&(t=Ci(t));for(var r=this._groups,i=r.length,o=[],a=[],s=0;s<i;++s)for(var u=r[s],f=u.length,l,c=0;c<f;++c)if(l=u[c]){for(var h=t.call(l,l.__data__,c,u),d,g=Rt(l,n),x=0,S=h.length;x<S;++x)(d=h[x])&&cn(d,e,n,x,h,g);o.push(h),a.push(l)}return new Tt(o,a,e,n)}var Zu=Pe.prototype.constructor;function Ju(){return new Zu(this._groups,this._parents)}function ju(t,e){var n,r,i;return function(){var o=jt(this,t),a=(this.style.removeProperty(t),jt(this,t));return o===a?null:o===n&&a===r?i:i=e(n=o,r=a)}}function Qi(t){return function(){this.style.removeProperty(t)}}function tc(t,e,n){var r,i=n+"",o;return function(){var a=jt(this,t);return a===i?null:a===r?o:o=e(r=a,n)}}function ec(t,e,n){var r,i,o;return function(){var a=jt(this,t),s=n(this),u=s+"";return s==null&&(u=s=(this.style.removeProperty(t),jt(this,t))),a===u?null:a===r&&u===i?o:(i=u,o=e(r=a,s))}}function nc(t,e){var n,r,i,o="style."+e,a="end."+o,s;return function(){var u=Mt(this,t),f=u.on,l=u.value[o]==null?s||(s=Qi(e)):void 0;(f!==n||i!==l)&&(r=(n=f).copy()).on(a,i=l),u.on=r}}function rc(t,e,n){var r=(t+="")=="transform"?au:Wi;return e==null?this.styleTween(t,ju(t,r)).on("end.style."+t,Qi(t)):typeof e=="function"?this.styleTween(t,ec(t,r,rr(this,"style."+t,e))).each(nc(this._id,t)):this.styleTween(t,tc(t,r,e),n).on("end.style."+t,null)}function ic(t,e,n){return function(r){this.style.setProperty(t,e.call(this,r),n)}}function oc(t,e,n){var r,i;function o(){var a=e.apply(this,arguments);return a!==i&&(r=(i=a)&&ic(t,a,n)),r}return o._value=e,o}function ac(t,e,n){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(e==null)return this.tween(r,null);if(typeof e!="function")throw new Error;return this.tween(r,oc(t,e,n??""))}function sc(t){return function(){this.textContent=t}}function lc(t){return function(){var e=t(this);this.textContent=e??""}}function uc(t){return this.tween("text",typeof t=="function"?lc(rr(this,"text",t)):sc(t==null?"":t+""))}function cc(t){return function(e){this.textContent=t.call(this,e)}}function fc(t){var e,n;function r(){var i=t.apply(this,arguments);return i!==n&&(e=(n=i)&&cc(i)),e}return r._value=t,r}function hc(t){var e="text";if(arguments.length<1)return(e=this.tween(e))&&e._value;if(t==null)return this.tween(e,null);if(typeof t!="function")throw new Error;return this.tween(e,fc(t))}function dc(){for(var t=this._name,e=this._id,n=Zi(),r=this._groups,i=r.length,o=0;o<i;++o)for(var a=r[o],s=a.length,u,f=0;f<s;++f)if(u=a[f]){var l=Rt(u,e);cn(u,t,n,f,a,{time:l.time+l.delay+l.duration,delay:0,duration:l.duration,ease:l.ease})}return new Tt(r,this._parents,t,n)}function pc(){var t,e,n=this,r=n._id,i=n.size();return new Promise(function(o,a){var s={value:a},u={value:function(){--i===0&&o()}};n.each(function(){var f=Mt(this,r),l=f.on;l!==t&&(e=(t=l).copy(),e._.cancel.push(s),e._.interrupt.push(s),e._.end.push(u)),f.on=e}),i===0&&o()})}var gc=0;function Tt(t,e,n,r){this._groups=t,this._parents=e,this._name=n,this._id=r}function Zi(){return++gc}var Nt=Pe.prototype;Tt.prototype={constructor:Tt,select:Wu,selectAll:Qu,selectChild:Nt.selectChild,selectChildren:Nt.selectChildren,filter:zu,merge:Uu,selection:Ju,transition:dc,call:Nt.call,nodes:Nt.nodes,node:Nt.node,size:Nt.size,empty:Nt.empty,each:Nt.each,on:Gu,attr:ku,attrTween:Au,style:rc,styleTween:ac,text:uc,textTween:hc,remove:Ku,tween:yu,delay:Tu,duration:Du,ease:Fu,easeVarying:Hu,end:pc,[Symbol.iterator]:Nt[Symbol.iterator]};function vc(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}var mc={time:null,delay:0,duration:250,ease:vc};function _c(t,e){for(var n;!(n=t.__transition)||!(n=n[e]);)if(!(t=t.parentNode))throw new Error(`transition ${e} not found`);return n}function yc(t){var e,n;t instanceof Tt?(e=t._id,t=t._name):(e=Zi(),(n=mc).time=er(),t=t==null?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var a=r[o],s=a.length,u,f=0;f<s;++f)(u=a[f])&&cn(u,t,e,f,a,n||_c(u,e));return new Tt(r,this._parents,t,e)}Pe.prototype.interrupt=vu;Pe.prototype.transition=yc;function wc(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);if(!(t.status===204||t.status===205))return t.json()}function xc(t,e){return fetch(t,e).then(wc)}var Z=1e-6,U=Math.PI,pt=U/2,zr=U/4,_t=U*2,vt=180/U,rt=U/180,J=Math.abs,Ji=Math.atan,Se=Math.atan2,tt=Math.cos,Ec=Math.exp,Sc=Math.log,et=Math.sin,Rc=Math.sign||function(t){return t>0?1:t<0?-1:0},Ut=Math.sqrt,bc=Math.tan;function kc(t){return t>1?0:t<-1?U:Math.acos(t)}function Re(t){return t>1?pt:t<-1?-pt:Math.asin(t)}function xt(){}function Qe(t,e){t&&Br.hasOwnProperty(t.type)&&Br[t.type](t,e)}var Ur={Feature:function(t,e){Qe(t.geometry,e)},FeatureCollection:function(t,e){for(var n=t.features,r=-1,i=n.length;++r<i;)Qe(n[r].geometry,e)}},Br={Sphere:function(t,e){e.sphere()},Point:function(t,e){t=t.coordinates,e.point(t[0],t[1],t[2])},MultiPoint:function(t,e){for(var n=t.coordinates,r=-1,i=n.length;++r<i;)t=n[r],e.point(t[0],t[1],t[2])},LineString:function(t,e){In(t.coordinates,e,0)},MultiLineString:function(t,e){for(var n=t.coordinates,r=-1,i=n.length;++r<i;)In(n[r],e,0)},Polygon:function(t,e){Xr(t.coordinates,e)},MultiPolygon:function(t,e){for(var n=t.coordinates,r=-1,i=n.length;++r<i;)Xr(n[r],e)},GeometryCollection:function(t,e){for(var n=t.geometries,r=-1,i=n.length;++r<i;)Qe(n[r],e)}};function In(t,e,n){var r=-1,i=t.length-n,o;for(e.lineStart();++r<i;)o=t[r],e.point(o[0],o[1],o[2]);e.lineEnd()}function Xr(t,e){var n=-1,r=t.length;for(e.polygonStart();++n<r;)In(t[n],e,1);e.polygonEnd()}function Gt(t,e){t&&Ur.hasOwnProperty(t.type)?Ur[t.type](t,e):Qe(t,e)}function Tn(t){return[Se(t[1],t[0]),Re(t[2])]}function ee(t){var e=t[0],n=t[1],r=tt(n);return[r*tt(e),r*et(e),et(n)]}function Ie(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]}function Ze(t,e){return[t[1]*e[2]-t[2]*e[1],t[2]*e[0]-t[0]*e[2],t[0]*e[1]-t[1]*e[0]]}function _n(t,e){t[0]+=e[0],t[1]+=e[1],t[2]+=e[2]}function Te(t,e){return[t[0]*e,t[1]*e,t[2]*e]}function On(t){var e=Ut(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=e,t[1]/=e,t[2]/=e}function Vn(t,e){function n(r,i){return r=t(r,i),e(r[0],r[1])}return t.invert&&e.invert&&(n.invert=function(r,i){return r=e.invert(r,i),r&&t.invert(r[0],r[1])}),n}function Dn(t,e){return J(t)>U&&(t-=Math.round(t/_t)*_t),[t,e]}Dn.invert=Dn;function ji(t,e,n){return(t%=_t)?e||n?Vn(Yr(t),Kr(e,n)):Yr(t):e||n?Kr(e,n):Dn}function Gr(t){return function(e,n){return e+=t,J(e)>U&&(e-=Math.round(e/_t)*_t),[e,n]}}function Yr(t){var e=Gr(t);return e.invert=Gr(-t),e}function Kr(t,e){var n=tt(t),r=et(t),i=tt(e),o=et(e);function a(s,u){var f=tt(u),l=tt(s)*f,c=et(s)*f,h=et(u),d=h*n+l*r;return[Se(c*i-d*o,l*n-h*r),Re(d*i+c*o)]}return a.invert=function(s,u){var f=tt(u),l=tt(s)*f,c=et(s)*f,h=et(u),d=h*i-c*o;return[Se(c*i+h*o,l*n+d*r),Re(d*n-l*r)]},a}function Pc(t){t=ji(t[0]*rt,t[1]*rt,t.length>2?t[2]*rt:0);function e(n){return n=t(n[0]*rt,n[1]*rt),n[0]*=vt,n[1]*=vt,n}return e.invert=function(n){return n=t.invert(n[0]*rt,n[1]*rt),n[0]*=vt,n[1]*=vt,n},e}function Cc(t,e,n,r,i,o){if(n){var a=tt(e),s=et(e),u=r*n;i==null?(i=e+r*_t,o=e-u/2):(i=Wr(a,i),o=Wr(a,o),(r>0?i<o:i>o)&&(i+=r*_t));for(var f,l=i;r>0?l>o:l<o;l-=u)f=Tn([a,-s*tt(l),-s*et(l)]),t.point(f[0],f[1])}}function Wr(t,e){e=ee(e),e[0]-=t,On(e);var n=kc(-e[1]);return((-e[2]<0?-n:n)+_t-Z)%_t}function to(){var t=[],e;return{point:function(n,r,i){e.push([n,r,i])},lineStart:function(){t.push(e=[])},lineEnd:xt,rejoin:function(){t.length>1&&t.push(t.pop().concat(t.shift()))},result:function(){var n=t;return t=[],e=null,n}}}function He(t,e){return J(t[0]-e[0])<Z&&J(t[1]-e[1])<Z}function Oe(t,e,n,r){this.x=t,this.z=e,this.o=n,this.e=r,this.v=!1,this.n=this.p=null}function eo(t,e,n,r,i){var o=[],a=[],s,u;if(t.forEach(function(g){if(!((x=g.length-1)<=0)){var x,S=g[0],M=g[x],v;if(He(S,M)){if(!S[2]&&!M[2]){for(i.lineStart(),s=0;s<x;++s)i.point((S=g[s])[0],S[1]);i.lineEnd();return}M[0]+=2*Z}o.push(v=new Oe(S,g,null,!0)),a.push(v.o=new Oe(S,null,v,!1)),o.push(v=new Oe(M,g,null,!1)),a.push(v.o=new Oe(M,null,v,!0))}}),!!o.length){for(a.sort(e),Qr(o),Qr(a),s=0,u=a.length;s<u;++s)a[s].e=n=!n;for(var f=o[0],l,c;;){for(var h=f,d=!0;h.v;)if((h=h.n)===f)return;l=h.z,i.lineStart();do{if(h.v=h.o.v=!0,h.e){if(d)for(s=0,u=l.length;s<u;++s)i.point((c=l[s])[0],c[1]);else r(h.x,h.n.x,1,i);h=h.n}else{if(d)for(l=h.p.z,s=l.length-1;s>=0;--s)i.point((c=l[s])[0],c[1]);else r(h.x,h.p.x,-1,i);h=h.p}h=h.o,l=h.z,d=!d}while(!h.v);i.lineEnd()}}}function Qr(t){if(e=t.length){for(var e,n=0,r=t[0],i;++n<e;)r.n=i=t[n],i.p=r,r=i;r.n=i=t[0],i.p=r}}function yn(t){return J(t[0])<=U?t[0]:Rc(t[0])*((J(t[0])+U)%_t-U)}function $c(t,e){var n=yn(e),r=e[1],i=et(r),o=[et(n),-tt(n),0],a=0,s=0,u=new qt;i===1?r=pt+Z:i===-1&&(r=-pt-Z);for(var f=0,l=t.length;f<l;++f)if(h=(c=t[f]).length)for(var c,h,d=c[h-1],g=yn(d),x=d[1]/2+zr,S=et(x),M=tt(x),v=0;v<h;++v,g=C,S=b,M=k,d=E){var E=c[v],C=yn(E),$=E[1]/2+zr,b=et($),k=tt($),R=C-g,p=R>=0?1:-1,m=p*R,_=m>U,T=S*b;if(u.add(Se(T*p*et(m),M*k+T*tt(m))),a+=_?R+p*_t:R,_^g>=n^C>=n){var L=Ze(ee(d),ee(E));On(L);var V=Ze(o,L);On(V);var y=(_^R>=0?-1:1)*Re(V[2]);(r>y||r===y&&(L[0]||L[1]))&&(s+=_^R>=0?1:-1)}}return(a<-1e-6||a<Z&&u<-1e-12)^s&1}function no(t,e,n,r){return function(i){var o=e(i),a=to(),s=e(a),u=!1,f,l,c,h={point:d,lineStart:x,lineEnd:S,polygonStart:function(){h.point=M,h.lineStart=v,h.lineEnd=E,l=[],f=[]},polygonEnd:function(){h.point=d,h.lineStart=x,h.lineEnd=S,l=bi(l);var C=$c(f,r);l.length?(u||(i.polygonStart(),u=!0),eo(l,Ac,C,n,i)):C&&(u||(i.polygonStart(),u=!0),i.lineStart(),n(null,null,1,i),i.lineEnd()),u&&(i.polygonEnd(),u=!1),l=f=null},sphere:function(){i.polygonStart(),i.lineStart(),n(null,null,1,i),i.lineEnd(),i.polygonEnd()}};function d(C,$){t(C,$)&&i.point(C,$)}function g(C,$){o.point(C,$)}function x(){h.point=g,o.lineStart()}function S(){h.point=d,o.lineEnd()}function M(C,$){c.push([C,$]),s.point(C,$)}function v(){s.lineStart(),c=[]}function E(){M(c[0][0],c[0][1]),s.lineEnd();var C=s.clean(),$=a.result(),b,k=$.length,R,p,m;if(c.pop(),f.push(c),c=null,!!k){if(C&1){if(p=$[0],(R=p.length-1)>0){for(u||(i.polygonStart(),u=!0),i.lineStart(),b=0;b<R;++b)i.point((m=p[b])[0],m[1]);i.lineEnd()}return}k>1&&C&2&&$.push($.pop().concat($.shift())),l.push($.filter(Mc))}}return h}}function Mc(t){return t.length>1}function Ac(t,e){return((t=t.x)[0]<0?t[1]-pt-Z:pt-t[1])-((e=e.x)[0]<0?e[1]-pt-Z:pt-e[1])}const Zr=no(function(){return!0},Nc,Tc,[-U,-pt]);function Nc(t){var e=NaN,n=NaN,r=NaN,i;return{lineStart:function(){t.lineStart(),i=1},point:function(o,a){var s=o>0?U:-U,u=J(o-e);J(u-U)<Z?(t.point(e,n=(n+a)/2>0?pt:-pt),t.point(r,n),t.lineEnd(),t.lineStart(),t.point(s,n),t.point(o,n),i=0):r!==s&&u>=U&&(J(e-r)<Z&&(e-=r*Z),J(o-s)<Z&&(o-=s*Z),n=Ic(e,n,o,a),t.point(r,n),t.lineEnd(),t.lineStart(),t.point(s,n),i=0),t.point(e=o,n=a),r=s},lineEnd:function(){t.lineEnd(),e=n=NaN},clean:function(){return 2-i}}}function Ic(t,e,n,r){var i,o,a=et(t-n);return J(a)>Z?Ji((et(e)*(o=tt(r))*et(n)-et(r)*(i=tt(e))*et(t))/(i*o*a)):(e+r)/2}function Tc(t,e,n,r){var i;if(t==null)i=n*pt,r.point(-U,i),r.point(0,i),r.point(U,i),r.point(U,0),r.point(U,-i),r.point(0,-i),r.point(-U,-i),r.point(-U,0),r.point(-U,i);else if(J(t[0]-e[0])>Z){var o=t[0]<e[0]?U:-U;i=n*o/2,r.point(-o,i),r.point(0,i),r.point(o,i)}else r.point(e[0],e[1])}function Oc(t){var e=tt(t),n=2*rt,r=e>0,i=J(e)>Z;function o(l,c,h,d){Cc(d,t,n,h,l,c)}function a(l,c){return tt(l)*tt(c)>e}function s(l){var c,h,d,g,x;return{lineStart:function(){g=d=!1,x=1},point:function(S,M){var v=[S,M],E,C=a(S,M),$=r?C?0:f(S,M):C?f(S+(S<0?U:-U),M):0;if(!c&&(g=d=C)&&l.lineStart(),C!==d&&(E=u(c,v),(!E||He(c,E)||He(v,E))&&(v[2]=1)),C!==d)x=0,C?(l.lineStart(),E=u(v,c),l.point(E[0],E[1])):(E=u(c,v),l.point(E[0],E[1],2),l.lineEnd()),c=E;else if(i&&c&&r^C){var b;!($&h)&&(b=u(v,c,!0))&&(x=0,r?(l.lineStart(),l.point(b[0][0],b[0][1]),l.point(b[1][0],b[1][1]),l.lineEnd()):(l.point(b[1][0],b[1][1]),l.lineEnd(),l.lineStart(),l.point(b[0][0],b[0][1],3)))}C&&(!c||!He(c,v))&&l.point(v[0],v[1]),c=v,d=C,h=$},lineEnd:function(){d&&l.lineEnd(),c=null},clean:function(){return x|(g&&d)<<1}}}function u(l,c,h){var d=ee(l),g=ee(c),x=[1,0,0],S=Ze(d,g),M=Ie(S,S),v=S[0],E=M-v*v;if(!E)return!h&&l;var C=e*M/E,$=-e*v/E,b=Ze(x,S),k=Te(x,C),R=Te(S,$);_n(k,R);var p=b,m=Ie(k,p),_=Ie(p,p),T=m*m-_*(Ie(k,k)-1);if(!(T<0)){var L=Ut(T),V=Te(p,(-m-L)/_);if(_n(V,k),V=Tn(V),!h)return V;var y=l[0],D=c[0],K=l[1],W=c[1],j;D<y&&(j=y,y=D,D=j);var gt=D-y,X=J(gt-U)<Z,ct=X||gt<Z;if(!X&&W<K&&(j=K,K=W,W=j),ct?X?K+W>0^V[1]<(J(V[0]-y)<Z?K:W):K<=V[1]&&V[1]<=W:gt>U^(y<=V[0]&&V[0]<=D)){var st=Te(p,(-m+L)/_);return _n(st,k),[V,Tn(st)]}}}function f(l,c){var h=r?t:U-t,d=0;return l<-h?d|=1:l>h&&(d|=2),c<-h?d|=4:c>h&&(d|=8),d}return no(a,s,o,r?[0,-t]:[-U,t-U])}function Vc(t,e,n,r,i,o){var a=t[0],s=t[1],u=e[0],f=e[1],l=0,c=1,h=u-a,d=f-s,g;if(g=n-a,!(!h&&g>0)){if(g/=h,h<0){if(g<l)return;g<c&&(c=g)}else if(h>0){if(g>c)return;g>l&&(l=g)}if(g=i-a,!(!h&&g<0)){if(g/=h,h<0){if(g>c)return;g>l&&(l=g)}else if(h>0){if(g<l)return;g<c&&(c=g)}if(g=r-s,!(!d&&g>0)){if(g/=d,d<0){if(g<l)return;g<c&&(c=g)}else if(d>0){if(g>c)return;g>l&&(l=g)}if(g=o-s,!(!d&&g<0)){if(g/=d,d<0){if(g>c)return;g>l&&(l=g)}else if(d>0){if(g<l)return;g<c&&(c=g)}return l>0&&(t[0]=a+l*h,t[1]=s+l*d),c<1&&(e[0]=a+c*h,e[1]=s+c*d),!0}}}}}var Ve=1e9,De=-1e9;function Dc(t,e,n,r){function i(f,l){return t<=f&&f<=n&&e<=l&&l<=r}function o(f,l,c,h){var d=0,g=0;if(f==null||(d=a(f,c))!==(g=a(l,c))||u(f,l)<0^c>0)do h.point(d===0||d===3?t:n,d>1?r:e);while((d=(d+c+4)%4)!==g);else h.point(l[0],l[1])}function a(f,l){return J(f[0]-t)<Z?l>0?0:3:J(f[0]-n)<Z?l>0?2:1:J(f[1]-e)<Z?l>0?1:0:l>0?3:2}function s(f,l){return u(f.x,l.x)}function u(f,l){var c=a(f,1),h=a(l,1);return c!==h?c-h:c===0?l[1]-f[1]:c===1?f[0]-l[0]:c===2?f[1]-l[1]:l[0]-f[0]}return function(f){var l=f,c=to(),h,d,g,x,S,M,v,E,C,$,b,k={point:R,lineStart:T,lineEnd:L,polygonStart:m,polygonEnd:_};function R(y,D){i(y,D)&&l.point(y,D)}function p(){for(var y=0,D=0,K=d.length;D<K;++D)for(var W=d[D],j=1,gt=W.length,X=W[0],ct,st,q=X[0],lt=X[1];j<gt;++j)ct=q,st=lt,X=W[j],q=X[0],lt=X[1],st<=r?lt>r&&(q-ct)*(r-st)>(lt-st)*(t-ct)&&++y:lt<=r&&(q-ct)*(r-st)<(lt-st)*(t-ct)&&--y;return y}function m(){l=c,h=[],d=[],b=!0}function _(){var y=p(),D=b&&y,K=(h=bi(h)).length;(D||K)&&(f.polygonStart(),D&&(f.lineStart(),o(null,null,1,f),f.lineEnd()),K&&eo(h,s,y,o,f),f.polygonEnd()),l=f,h=d=g=null}function T(){k.point=V,d&&d.push(g=[]),$=!0,C=!1,v=E=NaN}function L(){h&&(V(x,S),M&&C&&c.rejoin(),h.push(c.result())),k.point=R,C&&l.lineEnd()}function V(y,D){var K=i(y,D);if(d&&g.push([y,D]),$)x=y,S=D,M=K,$=!1,K&&(l.lineStart(),l.point(y,D));else if(K&&C)l.point(y,D);else{var W=[v=Math.max(De,Math.min(Ve,v)),E=Math.max(De,Math.min(Ve,E))],j=[y=Math.max(De,Math.min(Ve,y)),D=Math.max(De,Math.min(Ve,D))];Vc(W,j,t,e,n,r)?(C||(l.lineStart(),l.point(W[0],W[1])),l.point(j[0],j[1]),K||l.lineEnd(),b=!1):K&&(l.lineStart(),l.point(y,D),b=!1)}v=y,E=D,C=K}return k}}const Ln=t=>t;var wn=new qt,Fn=new qt,ro,io,qn,Hn,It={point:xt,lineStart:xt,lineEnd:xt,polygonStart:function(){It.lineStart=Lc,It.lineEnd=qc},polygonEnd:function(){It.lineStart=It.lineEnd=It.point=xt,wn.add(J(Fn)),Fn=new qt},result:function(){var t=wn/2;return wn=new qt,t}};function Lc(){It.point=Fc}function Fc(t,e){It.point=oo,ro=qn=t,io=Hn=e}function oo(t,e){Fn.add(Hn*t-qn*e),qn=t,Hn=e}function qc(){oo(ro,io)}var ne=1/0,Je=ne,be=-ne,je=be,tn={point:Hc,lineStart:xt,lineEnd:xt,polygonStart:xt,polygonEnd:xt,result:function(){var t=[[ne,Je],[be,je]];return be=je=-(Je=ne=1/0),t}};function Hc(t,e){t<ne&&(ne=t),t>be&&(be=t),e<Je&&(Je=e),e>je&&(je=e)}var zn=0,Un=0,le=0,en=0,nn=0,Yt=0,Bn=0,Xn=0,ue=0,ao,so,kt,Pt,wt={point:zt,lineStart:Jr,lineEnd:jr,polygonStart:function(){wt.lineStart=Bc,wt.lineEnd=Xc},polygonEnd:function(){wt.point=zt,wt.lineStart=Jr,wt.lineEnd=jr},result:function(){var t=ue?[Bn/ue,Xn/ue]:Yt?[en/Yt,nn/Yt]:le?[zn/le,Un/le]:[NaN,NaN];return zn=Un=le=en=nn=Yt=Bn=Xn=ue=0,t}};function zt(t,e){zn+=t,Un+=e,++le}function Jr(){wt.point=zc}function zc(t,e){wt.point=Uc,zt(kt=t,Pt=e)}function Uc(t,e){var n=t-kt,r=e-Pt,i=Ut(n*n+r*r);en+=i*(kt+t)/2,nn+=i*(Pt+e)/2,Yt+=i,zt(kt=t,Pt=e)}function jr(){wt.point=zt}function Bc(){wt.point=Gc}function Xc(){lo(ao,so)}function Gc(t,e){wt.point=lo,zt(ao=kt=t,so=Pt=e)}function lo(t,e){var n=t-kt,r=e-Pt,i=Ut(n*n+r*r);en+=i*(kt+t)/2,nn+=i*(Pt+e)/2,Yt+=i,i=Pt*t-kt*e,Bn+=i*(kt+t),Xn+=i*(Pt+e),ue+=i*3,zt(kt=t,Pt=e)}function uo(t){this._context=t}uo.prototype={_radius:4.5,pointRadius:function(t){return this._radius=t,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){this._line===0&&this._context.closePath(),this._point=NaN},point:function(t,e){switch(this._point){case 0:{this._context.moveTo(t,e),this._point=1;break}case 1:{this._context.lineTo(t,e);break}default:{this._context.moveTo(t+this._radius,e),this._context.arc(t,e,this._radius,0,_t);break}}},result:xt};var Gn=new qt,xn,co,fo,ce,fe,ke={point:xt,lineStart:function(){ke.point=Yc},lineEnd:function(){xn&&ho(co,fo),ke.point=xt},polygonStart:function(){xn=!0},polygonEnd:function(){xn=null},result:function(){var t=+Gn;return Gn=new qt,t}};function Yc(t,e){ke.point=ho,co=ce=t,fo=fe=e}function ho(t,e){ce-=t,fe-=e,Gn.add(Ut(ce*ce+fe*fe)),ce=t,fe=e}let ti,rn,ei,ni;class ri{constructor(e){this._append=e==null?po:Kc(e),this._radius=4.5,this._=""}pointRadius(e){return this._radius=+e,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){this._line===0&&(this._+="Z"),this._point=NaN}point(e,n){switch(this._point){case 0:{this._append`M${e},${n}`,this._point=1;break}case 1:{this._append`L${e},${n}`;break}default:{if(this._append`M${e},${n}`,this._radius!==ei||this._append!==rn){const r=this._radius,i=this._;this._="",this._append`m0,${r}a${r},${r} 0 1,1 0,${-2*r}a${r},${r} 0 1,1 0,${2*r}z`,ei=r,rn=this._append,ni=this._,this._=i}this._+=ni;break}}}result(){const e=this._;return this._="",e.length?e:null}}function po(t){let e=1;this._+=t[0];for(const n=t.length;e<n;++e)this._+=arguments[e]+t[e]}function Kc(t){const e=Math.floor(t);if(!(e>=0))throw new RangeError(`invalid digits: ${t}`);if(e>15)return po;if(e!==ti){const n=10**e;ti=e,rn=function(i){let o=1;this._+=i[0];for(const a=i.length;o<a;++o)this._+=Math.round(arguments[o]*n)/n+i[o]}}return rn}function ii(t,e){let n=3,r=4.5,i,o;function a(s){return s&&(typeof r=="function"&&o.pointRadius(+r.apply(this,arguments)),Gt(s,i(o))),o.result()}return a.area=function(s){return Gt(s,i(It)),It.result()},a.measure=function(s){return Gt(s,i(ke)),ke.result()},a.bounds=function(s){return Gt(s,i(tn)),tn.result()},a.centroid=function(s){return Gt(s,i(wt)),wt.result()},a.projection=function(s){return arguments.length?(i=s==null?(t=null,Ln):(t=s).stream,a):t},a.context=function(s){return arguments.length?(o=s==null?(e=null,new ri(n)):new uo(e=s),typeof r!="function"&&o.pointRadius(r),a):e},a.pointRadius=function(s){return arguments.length?(r=typeof s=="function"?s:(o.pointRadius(+s),+s),a):r},a.digits=function(s){if(!arguments.length)return n;if(s==null)n=null;else{const u=Math.floor(s);if(!(u>=0))throw new RangeError(`invalid digits: ${s}`);n=u}return e===null&&(o=new ri(n)),a},a.projection(t).digits(n).context(e)}function ir(t){return function(e){var n=new Yn;for(var r in t)n[r]=t[r];return n.stream=e,n}}function Yn(){}Yn.prototype={constructor:Yn,point:function(t,e){this.stream.point(t,e)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};function or(t,e,n){var r=t.clipExtent&&t.clipExtent();return t.scale(150).translate([0,0]),r!=null&&t.clipExtent(null),Gt(n,t.stream(tn)),e(tn.result()),r!=null&&t.clipExtent(r),t}function go(t,e,n){return or(t,function(r){var i=e[1][0]-e[0][0],o=e[1][1]-e[0][1],a=Math.min(i/(r[1][0]-r[0][0]),o/(r[1][1]-r[0][1])),s=+e[0][0]+(i-a*(r[1][0]+r[0][0]))/2,u=+e[0][1]+(o-a*(r[1][1]+r[0][1]))/2;t.scale(150*a).translate([s,u])},n)}function Wc(t,e,n){return go(t,[[0,0],e],n)}function Qc(t,e,n){return or(t,function(r){var i=+e,o=i/(r[1][0]-r[0][0]),a=(i-o*(r[1][0]+r[0][0]))/2,s=-o*r[0][1];t.scale(150*o).translate([a,s])},n)}function Zc(t,e,n){return or(t,function(r){var i=+e,o=i/(r[1][1]-r[0][1]),a=-o*r[0][0],s=(i-o*(r[1][1]+r[0][1]))/2;t.scale(150*o).translate([a,s])},n)}var oi=16,Jc=tt(30*rt);function ai(t,e){return+e?tf(t,e):jc(t)}function jc(t){return ir({point:function(e,n){e=t(e,n),this.stream.point(e[0],e[1])}})}function tf(t,e){function n(r,i,o,a,s,u,f,l,c,h,d,g,x,S){var M=f-r,v=l-i,E=M*M+v*v;if(E>4*e&&x--){var C=a+h,$=s+d,b=u+g,k=Ut(C*C+$*$+b*b),R=Re(b/=k),p=J(J(b)-1)<Z||J(o-c)<Z?(o+c)/2:Se($,C),m=t(p,R),_=m[0],T=m[1],L=_-r,V=T-i,y=v*L-M*V;(y*y/E>e||J((M*L+v*V)/E-.5)>.3||a*h+s*d+u*g<Jc)&&(n(r,i,o,a,s,u,_,T,p,C/=k,$/=k,b,x,S),S.point(_,T),n(_,T,p,C,$,b,f,l,c,h,d,g,x,S))}}return function(r){var i,o,a,s,u,f,l,c,h,d,g,x,S={point:M,lineStart:v,lineEnd:C,polygonStart:function(){r.polygonStart(),S.lineStart=$},polygonEnd:function(){r.polygonEnd(),S.lineStart=v}};function M(R,p){R=t(R,p),r.point(R[0],R[1])}function v(){c=NaN,S.point=E,r.lineStart()}function E(R,p){var m=ee([R,p]),_=t(R,p);n(c,h,l,d,g,x,c=_[0],h=_[1],l=R,d=m[0],g=m[1],x=m[2],oi,r),r.point(c,h)}function C(){S.point=M,r.lineEnd()}function $(){v(),S.point=b,S.lineEnd=k}function b(R,p){E(i=R,p),o=c,a=h,s=d,u=g,f=x,S.point=E}function k(){n(c,h,l,d,g,x,o,a,i,s,u,f,oi,r),S.lineEnd=C,C()}return S}}var ef=ir({point:function(t,e){this.stream.point(t*rt,e*rt)}});function nf(t){return ir({point:function(e,n){var r=t(e,n);return this.stream.point(r[0],r[1])}})}function rf(t,e,n,r,i){function o(a,s){return a*=r,s*=i,[e+t*a,n-t*s]}return o.invert=function(a,s){return[(a-e)/t*r,(n-s)/t*i]},o}function si(t,e,n,r,i,o){if(!o)return rf(t,e,n,r,i);var a=tt(o),s=et(o),u=a*t,f=s*t,l=a/t,c=s/t,h=(s*n-a*e)/t,d=(s*e+a*n)/t;function g(x,S){return x*=r,S*=i,[u*x-f*S+e,n-f*x-u*S]}return g.invert=function(x,S){return[r*(l*x-c*S+h),i*(d-c*x-l*S)]},g}function of(t){return af(function(){return t})()}function af(t){var e,n=150,r=480,i=250,o=0,a=0,s=0,u=0,f=0,l,c=0,h=1,d=1,g=null,x=Zr,S=null,M,v,E,C=Ln,$=.5,b,k,R,p,m;function _(y){return R(y[0]*rt,y[1]*rt)}function T(y){return y=R.invert(y[0],y[1]),y&&[y[0]*vt,y[1]*vt]}_.stream=function(y){return p&&m===y?p:p=ef(nf(l)(x(b(C(m=y)))))},_.preclip=function(y){return arguments.length?(x=y,g=void 0,V()):x},_.postclip=function(y){return arguments.length?(C=y,S=M=v=E=null,V()):C},_.clipAngle=function(y){return arguments.length?(x=+y?Oc(g=y*rt):(g=null,Zr),V()):g*vt},_.clipExtent=function(y){return arguments.length?(C=y==null?(S=M=v=E=null,Ln):Dc(S=+y[0][0],M=+y[0][1],v=+y[1][0],E=+y[1][1]),V()):S==null?null:[[S,M],[v,E]]},_.scale=function(y){return arguments.length?(n=+y,L()):n},_.translate=function(y){return arguments.length?(r=+y[0],i=+y[1],L()):[r,i]},_.center=function(y){return arguments.length?(o=y[0]%360*rt,a=y[1]%360*rt,L()):[o*vt,a*vt]},_.rotate=function(y){return arguments.length?(s=y[0]%360*rt,u=y[1]%360*rt,f=y.length>2?y[2]%360*rt:0,L()):[s*vt,u*vt,f*vt]},_.angle=function(y){return arguments.length?(c=y%360*rt,L()):c*vt},_.reflectX=function(y){return arguments.length?(h=y?-1:1,L()):h<0},_.reflectY=function(y){return arguments.length?(d=y?-1:1,L()):d<0},_.precision=function(y){return arguments.length?(b=ai(k,$=y*y),V()):Ut($)},_.fitExtent=function(y,D){return go(_,y,D)},_.fitSize=function(y,D){return Wc(_,y,D)},_.fitWidth=function(y,D){return Qc(_,y,D)},_.fitHeight=function(y,D){return Zc(_,y,D)};function L(){var y=si(n,0,0,h,d,c).apply(null,e(o,a)),D=si(n,r-y[0],i-y[1],h,d,c);return l=ji(s,u,f),k=Vn(e,D),R=Vn(l,k),b=ai(k,$),V()}function V(){return p=m=null,_}return function(){return e=t.apply(this,arguments),_.invert=e.invert&&T,L()}}function ar(t,e){return[t,Sc(bc((pt+e)/2))]}ar.invert=function(t,e){return[t,2*Ji(Ec(e))-pt]};function sf(){return lf(ar).scale(961/_t)}function lf(t){var e=of(t),n=e.center,r=e.scale,i=e.translate,o=e.clipExtent,a=null,s,u,f;e.scale=function(c){return arguments.length?(r(c),l()):r()},e.translate=function(c){return arguments.length?(i(c),l()):i()},e.center=function(c){return arguments.length?(n(c),l()):n()},e.clipExtent=function(c){return arguments.length?(c==null?a=s=u=f=null:(a=+c[0][0],s=+c[0][1],u=+c[1][0],f=+c[1][1]),l()):a==null?null:[[a,s],[u,f]]};function l(){var c=U*r(),h=e(Pc(e.rotate()).invert([0,0]));return o(a==null?[[h[0]-c,h[1]-c],[h[0]+c,h[1]+c]]:t===ar?[[Math.max(h[0]-c,a),s],[Math.min(h[0]+c,u),f]]:[[a,Math.max(h[1]-c,s)],[u,Math.min(h[1]+c,f)]])}return l()}function he(t,e,n){this.k=t,this.x=e,this.y=n}he.prototype={constructor:he,scale:function(t){return t===1?this:new he(this.k*t,this.x,this.y)},translate:function(t,e){return t===0&e===0?this:new he(this.k,this.x+this.k*t,this.y+this.k*e)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};he.prototype;const uf={class:"clock-container"},cf={class:"holographic-clock",viewBox:"0 0 100 100"},ff={class:"clock-markers"},hf=["x1","y1","x2","y2"],df={class:"clock-hands"},pf=["x2","y2"],gf=["x2","y2"],vf=["x2","y2"],mf=["id"],_f=["id"],yf={__name:"VisualScreen",setup(t){const e=B(null),n=B(null),r=B(0),i=B(!1),o=new Map,a=B({x:0,y:0}),s=B(0),u=B(0),f=B(0),l=B(0);let c;const h=B([{id:"china",url:"https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json",center:[104,35],scaleFactor:.75,duration:5e3},{id:"sichuan",url:"https://geo.datav.aliyun.com/areas_v3/bound/510000_full.json",center:[103,30],scaleFactor:3,duration:5e3},{id:"guangan",url:"https://geo.datav.aliyun.com/areas_v3/bound/511600_full.json",center:[106.6,30.5],scaleFactor:30,duration:5e3},{id:"yuechi",url:"https://geo.datav.aliyun.com/areas_v3/bound/511621.json",center:[106.4,30.5],scaleFactor:50,duration:5e3}]),d={longitude:106.43,latitude:30.55},g=()=>e.value?{width:e.value.clientWidth,height:e.value.clientHeight}:{width:window.innerWidth,height:window.innerHeight},x=()=>{const R=new Date;s.value=R.getHours()%12,u.value=R.getMinutes(),f.value=R.getSeconds(),l.value=R.getMilliseconds()},S=R=>{if(o.has(R.id))return;const p=g(),m=$e(`#${R.id}-map`).attr("viewBox",`0 0 ${p.width} ${p.height}`).attr("preserveAspectRatio","xMidYMid meet");xc(R.url).then(_=>{const T=sf().center(R.center).scale(Math.min(p.width,p.height)*R.scaleFactor).translate([p.width/2,p.height/2]);o.set(R.id,{projection:T,data:_}),m.selectAll(".boundary").data(_.features).enter().append("path").attr("class","boundary").attr("d",ii().projection(T)),M(R.id)}).catch(_=>console.error("地图加载失败:",_))},M=R=>{const{projection:p}=o.get(R)||{};if(!p)return;const[m,_]=p([d.longitude,d.latitude]);a.value={x:m,y:_}},v=()=>{const R=g();h.value.forEach(p=>{const{projection:m,data:_}=o.get(p.id)||{};!m||!_||(m.scale(Math.min(R.width,R.height)*p.scaleFactor).translate([R.width/2,R.height/2]),$e(`#${p.id}-map`).attr("viewBox",`0 0 ${R.width} ${R.height}`).selectAll(".boundary").attr("d",ii().projection(m)),p.id===h.value[r.value].id&&M(p.id))})},E=R=>{if(i.value)return;i.value=!0;const p=h.value.length,m=(r.value+R+p)%p,_=h.value[r.value],T=h.value[m];$e(`#${_.id}-container`).transition().duration(1500).style("opacity",0).on("end",()=>{$e(`#${T.id}-container`).style("opacity",0).classed("map-visible",!0).transition().duration(1500).style("opacity",1).on("end",()=>{r.value=m,i.value=!1,M(T.id)})})},C=()=>{const{width:p,height:m}=g();for(let _=0;_<100;_++){const T=document.createElement("div");T.className="particle";const L=Math.random()*p,V=Math.random()*m,y=Math.random()*10,D=Math.random()*2+1;T.style.left=`${L}px`,T.style.top=`${V}px`,T.style.animationDelay=`${y}s`,T.style.width=`${D}px`,T.style.height=`${D}px`,n.value.appendChild(T)}};let $;const b=()=>{$=setInterval(()=>{E(1)},h.value[0].duration+1500)};on(()=>{x(),c=setInterval(x,50),h.value.forEach(S),C(),b(),document.addEventListener("fullscreenchange",k)}),Co(()=>{clearInterval(c),clearInterval($),o.clear(),document.removeEventListener("fullscreenchange",k)});const k=()=>{En(()=>{v(),n.value&&(n.value.innerHTML=""),C()})};return Kn([()=>window.innerWidth,()=>window.innerHeight,()=>{var R;return(R=e.value)==null?void 0:R.clientWidth},()=>{var R;return(R=e.value)==null?void 0:R.clientHeight}],()=>{En(v)}),(R,p)=>(ot(),yt("div",{class:"holographic-container",ref_key:"container",ref:e},[p[3]||(p[3]=F("div",{class:"slogan-container"},[F("div",null,"对党忠诚  服务人民"),F("div",null,"执法公正  纪律严明")],-1)),p[4]||(p[4]=F("div",{class:"title-container"},[F("div",{class:"holographic-title"},"岳池县公安局情报指挥中心")],-1)),F("div",uf,[(ot(),yt("svg",cf,[p[0]||(p[0]=F("circle",{cx:"50",cy:"50",r:"45",fill:"rgba(0,0,0,0)",stroke:"#00e5ff","stroke-width":"0.5"},null,-1)),p[1]||(p[1]=F("circle",{cx:"50",cy:"50",r:"42",fill:"rgba(5,15,44,0.5)"},null,-1)),F("g",ff,[(ot(),yt(ve,null,ze(12,m=>F("line",{key:m,x1:50+38*Math.cos((m*30-90)*Math.PI/180),y1:50+38*Math.sin((m*30-90)*Math.PI/180),x2:50+42*Math.cos((m*30-90)*Math.PI/180),y2:50+42*Math.sin((m*30-90)*Math.PI/180),stroke:"#00e5ff","stroke-width":"1.5"},null,8,hf)),64))]),F("g",df,[F("line",{class:"hour-hand",x1:50,y1:50,x2:50+20*Math.cos((s.value*30+u.value*.5-90)*Math.PI/180),y2:50+20*Math.sin((s.value*30+u.value*.5-90)*Math.PI/180),stroke:"#9e4edd","stroke-width":"3","stroke-linecap":"round"},null,8,pf),F("line",{class:"minute-hand",x1:50,y1:50,x2:50+30*Math.cos((u.value*6+f.value*.1-90)*Math.PI/180),y2:50+30*Math.sin((u.value*6+f.value*.1-90)*Math.PI/180),stroke:"#ff4d4d","stroke-width":"2","stroke-linecap":"round"},null,8,gf),F("line",{class:"second-hand",x1:50,y1:50,x2:50+38*Math.cos((f.value*6+l.value*.006-90)*Math.PI/180),y2:50+38*Math.sin((f.value*6+l.value*.006-90)*Math.PI/180),stroke:"#00e5ff","stroke-width":"1","stroke-linecap":"round"},null,8,vf)]),p[2]||(p[2]=F("circle",{cx:"50",cy:"50",r:"2",fill:"#fff"},null,-1))]))]),F("div",{ref_key:"particlesContainer",ref:n,class:"particles-container"},null,512),p[5]||(p[5]=F("div",{class:"hologram-grid"},null,-1)),p[6]||(p[6]=F("div",{class:"scan-line-vertical"},null,-1)),p[7]||(p[7]=F("div",{class:"scan-line-horizontal"},null,-1)),p[8]||(p[8]=F("div",{class:"hologram-frame"},[F("div")],-1)),(ot(!0),yt(ve,null,ze(h.value,(m,_)=>(ot(),yt("div",{key:m.id,id:`${m.id}-container`,class:$o(["map-container",{"map-visible":r.value===_}])},[(ot(),yt("svg",{id:`${m.id}-map`,class:"map-svg"},null,8,_f)),r.value===_?(ot(),yt("div",{key:0,class:"location-marker",style:Qn({left:a.value.x+"px",top:a.value.y+"px"})},null,4)):Ue("",!0)],10,mf))),128))],512))}},wf=[{path:"/unit-management",name:"UnitManagement",component:Qa},{path:"/user-management",name:"UserManagement",component:rs},{path:"/visual-screen",name:"VisualScreen",component:yf}],xf=Ta({history:ua(),routes:wf}),fn=Mo(Ya);fn.use(Ao);for(const[t,e]of Object.entries(No))fn.component(t,e);fn.use(xf);fn.mount("#app");
