import{s as go,d as si,u as se,a as vo,c as he,p as pn,r as X,w as Wn,h as ui,n as En,i as We,b as on,_ as ci,o as ln,e as Re,f as we,g as L,j as m,k,l as H,m as _o,F as gt,q as ae,t as yo,v as Qe,x as le,y as zt,z as pt,A as wo,B as fi,C as ar,D as sr,E as pe,G as xo,H as Ht,I as Qn,J as bo,K as Eo,L as So,M as ko,N as Ro,O as Po,P as Co,Q as $o,R as Mo,S as Ao,T as Vo}from"./index-CsUMa5m2.js";/*!
  * vue-router v4.5.1
  * (c) 2025 <PERSON>
  * @license MIT
  */const Xe=typeof document<"u";function di(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function No(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&di(e.default)}const W=Object.assign;function hn(e,t){const n={};for(const r in t){const i=t[r];n[r]=Se(i)?i.map(e):e(i)}return n}const ht=()=>{},Se=Array.isArray,pi=/#/g,Io=/&/g,To=/\//g,Oo=/=/g,Uo=/\?/g,hi=/\+/g,Do=/%5B/g,Lo=/%5D/g,mi=/%5E/g,Fo=/%60/g,gi=/%7B/g,qo=/%7C/g,vi=/%7D/g,zo=/%20/g;function Zn(e){return encodeURI(""+e).replace(qo,"|").replace(Do,"[").replace(Lo,"]")}function Ho(e){return Zn(e).replace(gi,"{").replace(vi,"}").replace(mi,"^")}function Sn(e){return Zn(e).replace(hi,"%2B").replace(zo,"+").replace(pi,"%23").replace(Io,"%26").replace(Fo,"`").replace(gi,"{").replace(vi,"}").replace(mi,"^")}function Bo(e){return Sn(e).replace(Oo,"%3D")}function Xo(e){return Zn(e).replace(pi,"%23").replace(Uo,"%3F")}function Go(e){return e==null?"":Xo(e).replace(To,"%2F")}function vt(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Yo=/\/$/,Ko=e=>e.replace(Yo,"");function mn(e,t,n="/"){let r,i={},o="",l="";const a=t.indexOf("#");let c=t.indexOf("?");return a<c&&a>=0&&(c=-1),c>-1&&(r=t.slice(0,c),o=t.slice(c+1,a>-1?a:t.length),i=e(o)),a>-1&&(r=r||t.slice(0,a),l=t.slice(a,t.length)),r=Jo(r??t,n),{fullPath:r+(o&&"?")+o+l,path:r,query:i,hash:vt(l)}}function Wo(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function ur(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Qo(e,t,n){const r=t.matched.length-1,i=n.matched.length-1;return r>-1&&r===i&&Ze(t.matched[r],n.matched[i])&&_i(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Ze(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function _i(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Zo(e[n],t[n]))return!1;return!0}function Zo(e,t){return Se(e)?cr(e,t):Se(t)?cr(t,e):e===t}function cr(e,t){return Se(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function Jo(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),i=r[r.length-1];(i===".."||i===".")&&r.push("");let o=n.length-1,l,a;for(l=0;l<r.length;l++)if(a=r[l],a!==".")if(a==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+r.slice(l).join("/")}const Te={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var _t;(function(e){e.pop="pop",e.push="push"})(_t||(_t={}));var mt;(function(e){e.back="back",e.forward="forward",e.unknown=""})(mt||(mt={}));function jo(e){if(!e)if(Xe){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Ko(e)}const el=/^[^#]+#/;function tl(e,t){return e.replace(el,"#")+t}function nl(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const an=()=>({left:window.scrollX,top:window.scrollY});function rl(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),i=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!i)return;t=nl(i,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function fr(e,t){return(history.state?history.state.position-t:-1)+e}const kn=new Map;function il(e,t){kn.set(e,t)}function ol(e){const t=kn.get(e);return kn.delete(e),t}let ll=()=>location.protocol+"//"+location.host;function yi(e,t){const{pathname:n,search:r,hash:i}=t,o=e.indexOf("#");if(o>-1){let a=i.includes(e.slice(o))?e.slice(o).length:1,c=i.slice(a);return c[0]!=="/"&&(c="/"+c),ur(c,"")}return ur(n,e)+r+i}function al(e,t,n,r){let i=[],o=[],l=null;const a=({state:d})=>{const p=yi(e,location),_=n.value,E=t.value;let R=0;if(d){if(n.value=p,t.value=d,l&&l===_){l=null;return}R=E?d.position-E.position:0}else r(p);i.forEach(A=>{A(n.value,_,{delta:R,type:_t.pop,direction:R?R>0?mt.forward:mt.back:mt.unknown})})};function c(){l=n.value}function s(d){i.push(d);const p=()=>{const _=i.indexOf(d);_>-1&&i.splice(_,1)};return o.push(p),p}function u(){const{history:d}=window;d.state&&d.replaceState(W({},d.state,{scroll:an()}),"")}function f(){for(const d of o)d();o=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:c,listen:s,destroy:f}}function dr(e,t,n,r=!1,i=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:i?an():null}}function sl(e){const{history:t,location:n}=window,r={value:yi(e,n)},i={value:t.state};i.value||o(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(c,s,u){const f=e.indexOf("#"),d=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+c:ll()+e+c;try{t[u?"replaceState":"pushState"](s,"",d),i.value=s}catch(p){console.error(p),n[u?"replace":"assign"](d)}}function l(c,s){const u=W({},t.state,dr(i.value.back,c,i.value.forward,!0),s,{position:i.value.position});o(c,u,!0),r.value=c}function a(c,s){const u=W({},i.value,t.state,{forward:c,scroll:an()});o(u.current,u,!0);const f=W({},dr(r.value,c,null),{position:u.position+1},s);o(c,f,!1),r.value=c}return{location:r,state:i,push:a,replace:l}}function ul(e){e=jo(e);const t=sl(e),n=al(e,t.state,t.location,t.replace);function r(o,l=!0){l||n.pauseListeners(),history.go(o)}const i=W({location:"",base:e,go:r,createHref:tl.bind(null,e)},t,n);return Object.defineProperty(i,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(i,"state",{enumerable:!0,get:()=>t.state.value}),i}function cl(e){return typeof e=="string"||e&&typeof e=="object"}function wi(e){return typeof e=="string"||typeof e=="symbol"}const xi=Symbol("");var pr;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(pr||(pr={}));function Je(e,t){return W(new Error,{type:e,[xi]:!0},t)}function Ae(e,t){return e instanceof Error&&xi in e&&(t==null||!!(e.type&t))}const hr="[^/]+?",fl={sensitive:!1,strict:!1,start:!0,end:!0},dl=/[.+*?^${}()[\]/\\]/g;function pl(e,t){const n=W({},fl,t),r=[];let i=n.start?"^":"";const o=[];for(const s of e){const u=s.length?[]:[90];n.strict&&!s.length&&(i+="/");for(let f=0;f<s.length;f++){const d=s[f];let p=40+(n.sensitive?.25:0);if(d.type===0)f||(i+="/"),i+=d.value.replace(dl,"\\$&"),p+=40;else if(d.type===1){const{value:_,repeatable:E,optional:R,regexp:A}=d;o.push({name:_,repeatable:E,optional:R});const S=A||hr;if(S!==hr){p+=10;try{new RegExp(`(${S})`)}catch($){throw new Error(`Invalid custom RegExp for param "${_}" (${S}): `+$.message)}}let P=E?`((?:${S})(?:/(?:${S}))*)`:`(${S})`;f||(P=R&&s.length<2?`(?:/${P})`:"/"+P),R&&(P+="?"),i+=P,p+=20,R&&(p+=-8),E&&(p+=-20),S===".*"&&(p+=-50)}u.push(p)}r.push(u)}if(n.strict&&n.end){const s=r.length-1;r[s][r[s].length-1]+=.7000000000000001}n.strict||(i+="/?"),n.end?i+="$":n.strict&&!i.endsWith("/")&&(i+="(?:/|$)");const l=new RegExp(i,n.sensitive?"":"i");function a(s){const u=s.match(l),f={};if(!u)return null;for(let d=1;d<u.length;d++){const p=u[d]||"",_=o[d-1];f[_.name]=p&&_.repeatable?p.split("/"):p}return f}function c(s){let u="",f=!1;for(const d of e){(!f||!u.endsWith("/"))&&(u+="/"),f=!1;for(const p of d)if(p.type===0)u+=p.value;else if(p.type===1){const{value:_,repeatable:E,optional:R}=p,A=_ in s?s[_]:"";if(Se(A)&&!E)throw new Error(`Provided param "${_}" is an array but it is not repeatable (* or + modifiers)`);const S=Se(A)?A.join("/"):A;if(!S)if(R)d.length<2&&(u.endsWith("/")?u=u.slice(0,-1):f=!0);else throw new Error(`Missing required param "${_}"`);u+=S}}return u||"/"}return{re:l,score:r,keys:o,parse:a,stringify:c}}function hl(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function bi(e,t){let n=0;const r=e.score,i=t.score;for(;n<r.length&&n<i.length;){const o=hl(r[n],i[n]);if(o)return o;n++}if(Math.abs(i.length-r.length)===1){if(mr(r))return 1;if(mr(i))return-1}return i.length-r.length}function mr(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const ml={type:0,value:""},gl=/[a-zA-Z0-9_]/;function vl(e){if(!e)return[[]];if(e==="/")return[[ml]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(p){throw new Error(`ERR (${n})/"${s}": ${p}`)}let n=0,r=n;const i=[];let o;function l(){o&&i.push(o),o=[]}let a=0,c,s="",u="";function f(){s&&(n===0?o.push({type:0,value:s}):n===1||n===2||n===3?(o.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${s}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:s,regexp:u,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),s="")}function d(){s+=c}for(;a<e.length;){if(c=e[a++],c==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:c==="/"?(s&&f(),l()):c===":"?(f(),n=1):d();break;case 4:d(),n=r;break;case 1:c==="("?n=2:gl.test(c)?d():(f(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&a--);break;case 2:c===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+c:n=3:u+=c;break;case 3:f(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&a--,u="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${s}"`),f(),l(),i}function _l(e,t,n){const r=pl(vl(e.path),n),i=W(r,{record:e,parent:t,children:[],alias:[]});return t&&!i.record.aliasOf==!t.record.aliasOf&&t.children.push(i),i}function yl(e,t){const n=[],r=new Map;t=yr({strict:!1,end:!0,sensitive:!1},t);function i(f){return r.get(f)}function o(f,d,p){const _=!p,E=vr(f);E.aliasOf=p&&p.record;const R=yr(t,f),A=[E];if("alias"in f){const $=typeof f.alias=="string"?[f.alias]:f.alias;for(const O of $)A.push(vr(W({},E,{components:p?p.record.components:E.components,path:O,aliasOf:p?p.record:E})))}let S,P;for(const $ of A){const{path:O}=$;if(d&&O[0]!=="/"){const M=d.record.path,x=M[M.length-1]==="/"?"":"/";$.path=d.record.path+(O&&x+O)}if(S=_l($,d,R),p?p.alias.push(S):(P=P||S,P!==S&&P.alias.push(S),_&&f.name&&!_r(S)&&l(f.name)),Ei(S)&&c(S),E.children){const M=E.children;for(let x=0;x<M.length;x++)o(M[x],S,p&&p.children[x])}p=p||S}return P?()=>{l(P)}:ht}function l(f){if(wi(f)){const d=r.get(f);d&&(r.delete(f),n.splice(n.indexOf(d),1),d.children.forEach(l),d.alias.forEach(l))}else{const d=n.indexOf(f);d>-1&&(n.splice(d,1),f.record.name&&r.delete(f.record.name),f.children.forEach(l),f.alias.forEach(l))}}function a(){return n}function c(f){const d=bl(f,n);n.splice(d,0,f),f.record.name&&!_r(f)&&r.set(f.record.name,f)}function s(f,d){let p,_={},E,R;if("name"in f&&f.name){if(p=r.get(f.name),!p)throw Je(1,{location:f});R=p.record.name,_=W(gr(d.params,p.keys.filter(P=>!P.optional).concat(p.parent?p.parent.keys.filter(P=>P.optional):[]).map(P=>P.name)),f.params&&gr(f.params,p.keys.map(P=>P.name))),E=p.stringify(_)}else if(f.path!=null)E=f.path,p=n.find(P=>P.re.test(E)),p&&(_=p.parse(E),R=p.record.name);else{if(p=d.name?r.get(d.name):n.find(P=>P.re.test(d.path)),!p)throw Je(1,{location:f,currentLocation:d});R=p.record.name,_=W({},d.params,f.params),E=p.stringify(_)}const A=[];let S=p;for(;S;)A.unshift(S.record),S=S.parent;return{name:R,path:E,params:_,matched:A,meta:xl(A)}}e.forEach(f=>o(f));function u(){n.length=0,r.clear()}return{addRoute:o,resolve:s,removeRoute:l,clearRoutes:u,getRoutes:a,getRecordMatcher:i}}function gr(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function vr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:wl(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function wl(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function _r(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function xl(e){return e.reduce((t,n)=>W(t,n.meta),{})}function yr(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function bl(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;bi(e,t[o])<0?r=o:n=o+1}const i=El(e);return i&&(r=t.lastIndexOf(i,r-1)),r}function El(e){let t=e;for(;t=t.parent;)if(Ei(t)&&bi(e,t)===0)return t}function Ei({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Sl(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let i=0;i<r.length;++i){const o=r[i].replace(hi," "),l=o.indexOf("="),a=vt(l<0?o:o.slice(0,l)),c=l<0?null:vt(o.slice(l+1));if(a in t){let s=t[a];Se(s)||(s=t[a]=[s]),s.push(c)}else t[a]=c}return t}function wr(e){let t="";for(let n in e){const r=e[n];if(n=Bo(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(Se(r)?r.map(o=>o&&Sn(o)):[r&&Sn(r)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function kl(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=Se(r)?r.map(i=>i==null?null:""+i):r==null?r:""+r)}return t}const Rl=Symbol(""),xr=Symbol(""),sn=Symbol(""),Si=Symbol(""),Rn=Symbol("");function it(){let e=[];function t(r){return e.push(r),()=>{const i=e.indexOf(r);i>-1&&e.splice(i,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Oe(e,t,n,r,i,o=l=>l()){const l=r&&(r.enterCallbacks[i]=r.enterCallbacks[i]||[]);return()=>new Promise((a,c)=>{const s=d=>{d===!1?c(Je(4,{from:n,to:t})):d instanceof Error?c(d):cl(d)?c(Je(2,{from:t,to:d})):(l&&r.enterCallbacks[i]===l&&typeof d=="function"&&l.push(d),a())},u=o(()=>e.call(r&&r.instances[i],t,n,s));let f=Promise.resolve(u);e.length<3&&(f=f.then(s)),f.catch(d=>c(d))})}function gn(e,t,n,r,i=o=>o()){const o=[];for(const l of e)for(const a in l.components){let c=l.components[a];if(!(t!=="beforeRouteEnter"&&!l.instances[a]))if(di(c)){const u=(c.__vccOpts||c)[t];u&&o.push(Oe(u,n,r,l,a,i))}else{let s=c();o.push(()=>s.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${a}" at "${l.path}"`);const f=No(u)?u.default:u;l.mods[a]=u,l.components[a]=f;const p=(f.__vccOpts||f)[t];return p&&Oe(p,n,r,l,a,i)()}))}}return o}function br(e){const t=We(sn),n=We(Si),r=he(()=>{const c=se(e.to);return t.resolve(c)}),i=he(()=>{const{matched:c}=r.value,{length:s}=c,u=c[s-1],f=n.matched;if(!u||!f.length)return-1;const d=f.findIndex(Ze.bind(null,u));if(d>-1)return d;const p=Er(c[s-2]);return s>1&&Er(u)===p&&f[f.length-1].path!==p?f.findIndex(Ze.bind(null,c[s-2])):d}),o=he(()=>i.value>-1&&Al(n.params,r.value.params)),l=he(()=>i.value>-1&&i.value===n.matched.length-1&&_i(n.params,r.value.params));function a(c={}){if(Ml(c)){const s=t[se(e.replace)?"replace":"push"](se(e.to)).catch(ht);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>s),s}return Promise.resolve()}return{route:r,href:he(()=>r.value.href),isActive:o,isExactActive:l,navigate:a}}function Pl(e){return e.length===1?e[0]:e}const Cl=si({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:br,setup(e,{slots:t}){const n=on(br(e)),{options:r}=We(sn),i=he(()=>({[Sr(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[Sr(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&Pl(t.default(n));return e.custom?o:ui("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:i.value},o)}}}),$l=Cl;function Ml(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Al(e,t){for(const n in t){const r=t[n],i=e[n];if(typeof r=="string"){if(r!==i)return!1}else if(!Se(i)||i.length!==r.length||r.some((o,l)=>o!==i[l]))return!1}return!0}function Er(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Sr=(e,t,n)=>e??t??n,Vl=si({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=We(Rn),i=he(()=>e.route||r.value),o=We(xr,0),l=he(()=>{let s=se(o);const{matched:u}=i.value;let f;for(;(f=u[s])&&!f.components;)s++;return s}),a=he(()=>i.value.matched[l.value]);pn(xr,he(()=>l.value+1)),pn(Rl,a),pn(Rn,i);const c=X();return Wn(()=>[c.value,a.value,e.name],([s,u,f],[d,p,_])=>{u&&(u.instances[f]=s,p&&p!==u&&s&&s===d&&(u.leaveGuards.size||(u.leaveGuards=p.leaveGuards),u.updateGuards.size||(u.updateGuards=p.updateGuards))),s&&u&&(!p||!Ze(u,p)||!d)&&(u.enterCallbacks[f]||[]).forEach(E=>E(s))},{flush:"post"}),()=>{const s=i.value,u=e.name,f=a.value,d=f&&f.components[u];if(!d)return kr(n.default,{Component:d,route:s});const p=f.props[u],_=p?p===!0?s.params:typeof p=="function"?p(s):p:null,R=ui(d,W({},_,t,{onVnodeUnmounted:A=>{A.component.isUnmounted&&(f.instances[u]=null)},ref:c}));return kr(n.default,{Component:R,route:s})||R}}});function kr(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Nl=Vl;function Il(e){const t=yl(e.routes,e),n=e.parseQuery||Sl,r=e.stringifyQuery||wr,i=e.history,o=it(),l=it(),a=it(),c=go(Te);let s=Te;Xe&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=hn.bind(null,b=>""+b),f=hn.bind(null,Go),d=hn.bind(null,vt);function p(b,I){let N,F;return wi(b)?(N=t.getRecordMatcher(b),F=I):F=b,t.addRoute(F,N)}function _(b){const I=t.getRecordMatcher(b);I&&t.removeRoute(I)}function E(){return t.getRoutes().map(b=>b.record)}function R(b){return!!t.getRecordMatcher(b)}function A(b,I){if(I=W({},I||c.value),typeof b=="string"){const z=mn(n,b,I.path),oe=t.resolve({path:z.path},I),rt=i.createHref(z.fullPath);return W(z,oe,{params:d(oe.params),hash:vt(z.hash),redirectedFrom:void 0,href:rt})}let N;if(b.path!=null)N=W({},b,{path:mn(n,b.path,I.path).path});else{const z=W({},b.params);for(const oe in z)z[oe]==null&&delete z[oe];N=W({},b,{params:f(z)}),I.params=f(I.params)}const F=t.resolve(N,I),Z=b.hash||"";F.params=u(d(F.params));const re=Wo(r,W({},b,{hash:Ho(Z),path:F.path})),G=i.createHref(re);return W({fullPath:re,hash:Z,query:r===wr?kl(b.query):b.query||{}},F,{redirectedFrom:void 0,href:G})}function S(b){return typeof b=="string"?mn(n,b,c.value.path):W({},b)}function P(b,I){if(s!==b)return Je(8,{from:I,to:b})}function $(b){return x(b)}function O(b){return $(W(S(b),{replace:!0}))}function M(b){const I=b.matched[b.matched.length-1];if(I&&I.redirect){const{redirect:N}=I;let F=typeof N=="function"?N(b):N;return typeof F=="string"&&(F=F.includes("?")||F.includes("#")?F=S(F):{path:F},F.params={}),W({query:b.query,hash:b.hash,params:F.path!=null?{}:b.params},F)}}function x(b,I){const N=s=A(b),F=c.value,Z=b.state,re=b.force,G=b.replace===!0,z=M(N);if(z)return x(W(S(z),{state:typeof z=="object"?W({},Z,z.state):Z,force:re,replace:G}),I||N);const oe=N;oe.redirectedFrom=I;let rt;return!re&&Qo(r,F,N)&&(rt=Je(16,{to:oe,from:F}),B(F,F,!0,!1)),(rt?Promise.resolve(rt):v(oe,F)).catch(de=>Ae(de)?Ae(de,2)?de:C(de):Q(de,oe,F)).then(de=>{if(de){if(Ae(de,2))return x(W({replace:G},S(de.to),{state:typeof de.to=="object"?W({},Z,de.to.state):Z,force:re}),I||oe)}else de=T(oe,F,!0,G,Z);return y(oe,F,de),de})}function g(b,I){const N=P(b,I);return N?Promise.reject(N):Promise.resolve()}function h(b){const I=q.values().next().value;return I&&typeof I.runWithContext=="function"?I.runWithContext(b):b()}function v(b,I){let N;const[F,Z,re]=Tl(b,I);N=gn(F.reverse(),"beforeRouteLeave",b,I);for(const z of F)z.leaveGuards.forEach(oe=>{N.push(Oe(oe,b,I))});const G=g.bind(null,b,I);return N.push(G),Be(N).then(()=>{N=[];for(const z of o.list())N.push(Oe(z,b,I));return N.push(G),Be(N)}).then(()=>{N=gn(Z,"beforeRouteUpdate",b,I);for(const z of Z)z.updateGuards.forEach(oe=>{N.push(Oe(oe,b,I))});return N.push(G),Be(N)}).then(()=>{N=[];for(const z of re)if(z.beforeEnter)if(Se(z.beforeEnter))for(const oe of z.beforeEnter)N.push(Oe(oe,b,I));else N.push(Oe(z.beforeEnter,b,I));return N.push(G),Be(N)}).then(()=>(b.matched.forEach(z=>z.enterCallbacks={}),N=gn(re,"beforeRouteEnter",b,I,h),N.push(G),Be(N))).then(()=>{N=[];for(const z of l.list())N.push(Oe(z,b,I));return N.push(G),Be(N)}).catch(z=>Ae(z,8)?z:Promise.reject(z))}function y(b,I,N){a.list().forEach(F=>h(()=>F(b,I,N)))}function T(b,I,N,F,Z){const re=P(b,I);if(re)return re;const G=I===Te,z=Xe?history.state:{};N&&(F||G?i.replace(b.fullPath,W({scroll:G&&z&&z.scroll},Z)):i.push(b.fullPath,Z)),c.value=b,B(b,I,N,G),C()}let V;function U(){V||(V=i.listen((b,I,N)=>{if(!ce.listening)return;const F=A(b),Z=M(F);if(Z){x(W(Z,{replace:!0,force:!0}),F).catch(ht);return}s=F;const re=c.value;Xe&&il(fr(re.fullPath,N.delta),an()),v(F,re).catch(G=>Ae(G,12)?G:Ae(G,2)?(x(W(S(G.to),{force:!0}),F).then(z=>{Ae(z,20)&&!N.delta&&N.type===_t.pop&&i.go(-1,!1)}).catch(ht),Promise.reject()):(N.delta&&i.go(-N.delta,!1),Q(G,F,re))).then(G=>{G=G||T(F,re,!1),G&&(N.delta&&!Ae(G,8)?i.go(-N.delta,!1):N.type===_t.pop&&Ae(G,20)&&i.go(-1,!1)),y(F,re,G)}).catch(ht)}))}let w=it(),D=it(),K;function Q(b,I,N){C(b);const F=D.list();return F.length?F.forEach(Z=>Z(b,I,N)):console.error(b),Promise.reject(b)}function j(){return K&&c.value!==Te?Promise.resolve():new Promise((b,I)=>{w.add([b,I])})}function C(b){return K||(K=!b,U(),w.list().forEach(([I,N])=>b?N(b):I()),w.reset()),b}function B(b,I,N,F){const{scrollBehavior:Z}=e;if(!Xe||!Z)return Promise.resolve();const re=!N&&ol(fr(b.fullPath,0))||(F||!N)&&history.state&&history.state.scroll||null;return En().then(()=>Z(b,I,re)).then(G=>G&&rl(G)).catch(G=>Q(G,b,I))}const fe=b=>i.go(b);let ue;const q=new Set,ce={currentRoute:c,listening:!0,addRoute:p,removeRoute:_,clearRoutes:t.clearRoutes,hasRoute:R,getRoutes:E,resolve:A,options:e,push:$,replace:O,go:fe,back:()=>fe(-1),forward:()=>fe(1),beforeEach:o.add,beforeResolve:l.add,afterEach:a.add,onError:D.add,isReady:j,install(b){const I=this;b.component("RouterLink",$l),b.component("RouterView",Nl),b.config.globalProperties.$router=I,Object.defineProperty(b.config.globalProperties,"$route",{enumerable:!0,get:()=>se(c)}),Xe&&!ue&&c.value===Te&&(ue=!0,$(i.location).catch(Z=>{}));const N={};for(const Z in Te)Object.defineProperty(N,Z,{get:()=>c.value[Z],enumerable:!0});b.provide(sn,I),b.provide(Si,vo(N)),b.provide(Rn,c);const F=b.unmount;q.add(b),b.unmount=function(){q.delete(b),q.size<1&&(s=Te,V&&V(),V=null,c.value=Te,ue=!1,K=!1),F()}}};function Be(b){return b.reduce((I,N)=>I.then(()=>h(N)),Promise.resolve())}return ce}function Tl(e,t){const n=[],r=[],i=[],o=Math.max(t.matched.length,e.matched.length);for(let l=0;l<o;l++){const a=t.matched[l];a&&(e.matched.find(s=>Ze(s,a))?r.push(a):n.push(a));const c=e.matched[l];c&&(t.matched.find(s=>Ze(s,c))||i.push(c))}return[n,r,i]}function Ol(){return We(sn)}const Ul={class:"app-container"},Dl={class:"grid-container"},Ll={class:"header"},Fl={class:"header-left"},ql={class:"user-info"},zl={class:"el-dropdown-link"},Hl={class:"user-name"},Bl={class:"sidebar"},Xl={class:"content",ref:"contentRef"},Gl={__name:"App",setup(e){const t=Ol(),n=X(""),r=X([]),i=X(!1),o=X(""),l=X(null);ln(async()=>{await a(),await c(),t.push("/unit-management")});const a=async()=>{const M=await Re.post("/api/get_user_info.php");n.value=M.user.name},c=async()=>{const M=await Re.post("/api/get_user_app.php");r.value=M.data},s=M=>{console.log("点击的菜单对应的路由是:",M),t.push(M)},u=()=>{i.value=!i.value},f=X(!1),d=X({oldPassword:"",newPassword:"",confirmPassword:""}),p=X(null),_=X(!1),E=X(!1),R=()=>{_.value=!_.value},A=()=>{E.value=!E.value},S=async M=>{M==="logout"?(await Re.get("/api/logout.php"),window.location.href="login.html"):M==="changePassword"&&(f.value=!0)},P=async()=>{p.value&&await p.value.validate(M=>{if(M){if(d.value.newPassword!==d.value.confirmPassword){pe.error("两次输入的密码不一致");return}const x=new FormData;x.append("old_password",d.value.oldPassword),x.append("new_password",d.value.newPassword),Re.post("/api/change_password.php",x).then(()=>{pe.success("密码修改成功，请重新登录"),f.value=!1,d.value={oldPassword:"",newPassword:"",confirmPassword:""},setTimeout(()=>{window.location.href="login.html"},3e3)}).catch(()=>{pe.error("密码修改失败")})}})},$=on({oldPassword:[{required:!0,message:"请输入旧密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"}],confirmPassword:[{required:!0,message:"请输入确认新密码",trigger:"blur"},{validator:(M,x,g)=>{x!==d.value.newPassword?g(new Error("两次输入的密码不一致")):g()},trigger:"blur"}]}),O=()=>{const M=l.value;M&&(document.fullscreenElement?document.exitFullscreen():M.requestFullscreen().catch(x=>{console.error("全屏失败:",x),pe.error("全屏功能不支持")}))};return(M,x)=>{const g=H("el-button"),h=H("el-icon"),v=H("el-avatar"),y=H("el-dropdown-item"),T=H("el-dropdown-menu"),V=H("el-dropdown"),U=H("el-menu-item"),w=H("el-menu"),D=H("router-view"),K=H("el-input"),Q=H("el-form-item"),j=H("el-form"),C=H("el-dialog");return ae(),we(gt,null,[L("div",Ul,[L("div",Dl,[L("div",Ll,[L("div",Fl,[m(g,{onClick:u,type:"text",class:"menu-btn"},{default:k(()=>x[5]||(x[5]=[L("i",{class:"el-icon-menu"},null,-1)])),_:1,__:[5]}),x[6]||(x[6]=L("img",{src:_o,alt:"Logo",class:"header-logo"},null,-1)),x[7]||(x[7]=L("span",{class:"logo"},"CloudPivot",-1))])]),L("div",ql,[m(V,{onCommand:S},{dropdown:k(()=>[m(T,null,{default:k(()=>[m(y,{command:"profile"},{default:k(()=>x[8]||(x[8]=[le("个人信息")])),_:1,__:[8]}),m(y,{command:"changePassword"},{default:k(()=>x[9]||(x[9]=[le("修改密码")])),_:1,__:[9]}),m(y,{command:"logout"},{default:k(()=>x[10]||(x[10]=[le("退出登录")])),_:1,__:[10]})]),_:1})]),default:k(()=>[L("span",zl,[m(v,{size:"small"},{default:k(()=>[m(h,null,{default:k(()=>[m(se(yo),{style:{color:"#409EFF"}})]),_:1})]),_:1}),L("span",Hl,Qe(n.value),1)])]),_:1})]),L("div",Bl,[m(w,{"default-active":o.value,class:"el-menu-vertical",mode:"vertical",collapse:i.value,onOpen:M.handleOpen,onClose:M.handleClose},{default:k(()=>[(ae(!0),we(gt,null,zt(r.value,B=>(ae(),pt(U,{key:B.id,index:B.id.toString(),router:B.url,onClick:fe=>s(B.url)},{title:k(()=>[L("span",null,Qe(B.application_name),1)]),_:2},1032,["index","router","onClick"]))),128))]),_:1},8,["default-active","collapse","onOpen","onClose"])]),L("div",Xl,[m(g,{onClick:O,type:"text",class:"fullscreen-btn"},{default:k(()=>[m(h,null,{default:k(()=>[m(se(wo))]),_:1})]),_:1}),L("div",{class:"fullscreen-target",ref_key:"fullscreenTargetRef",ref:l},[m(D,{ref:"routerViewRef"},null,512)],512)],512)])]),m(C,{modelValue:f.value,"onUpdate:modelValue":x[4]||(x[4]=B=>f.value=B),width:"400px"},{default:k(()=>[m(j,{model:d.value,ref_key:"passwordFormRef",ref:p,rules:$,"label-width":"120px",onSubmit:fi(P,["prevent"])},{default:k(()=>[m(Q,{label:"旧密码",prop:"oldPassword",required:""},{default:k(()=>[m(K,{modelValue:d.value.oldPassword,"onUpdate:modelValue":x[0]||(x[0]=B=>d.value.oldPassword=B),type:"password",placeholder:"请输入旧密码"},null,8,["modelValue"])]),_:1}),m(Q,{label:"新密码",prop:"newPassword",required:""},{default:k(()=>[m(K,{modelValue:d.value.newPassword,"onUpdate:modelValue":x[1]||(x[1]=B=>d.value.newPassword=B),type:_.value?"text":"password",placeholder:"请输入新密码"},{suffix:k(()=>[m(g,{icon:_.value?se(ar):se(sr),onClick:R,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),m(Q,{label:"确认新密码",prop:"confirmPassword",required:""},{default:k(()=>[m(K,{modelValue:d.value.confirmPassword,"onUpdate:modelValue":x[2]||(x[2]=B=>d.value.confirmPassword=B),type:E.value?"text":"password",placeholder:"请输入确认新密码"},{suffix:k(()=>[m(g,{icon:E.value?se(ar):se(sr),onClick:A,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),m(Q,null,{default:k(()=>[m(g,{type:"primary","native-type":"submit"},{default:k(()=>x[11]||(x[11]=[le("确定")])),_:1,__:[11]}),m(g,{onClick:x[3]||(x[3]=B=>f.value=!1)},{default:k(()=>x[12]||(x[12]=[le("取消")])),_:1,__:[12]})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])],64)}}},Yl=ci(Gl,[["__scopeId","data-v-727c4254"]]),Kl={class:"unit-management"},Wl={style:{"text-align":"right",margin:"10px"}},Ql={__name:"UnitManagement",setup(e){const t=X(!1),n={expandTrigger:"hover",checkStrictly:!0,value:"id",label:"unit_name",children:"children"},r=h=>{if(h&&h.length>0){const v=h[h.length-1],y=o(v);y&&(i.parentId=v,f.value=y.children||[])}else i.parentId=null,f.value=[]},i=on({id:null,name:"",parentId:null,parentIdPath:[],code:"",sort:"top"});Wn(()=>i.parentId,(h,v)=>{if(console.log("parentId 发生变化，旧值: ",v,"新值: ",h),h){const y=o(h);y&&(f.value=y.children||[])}else f.value=[]},{immediate:!1});const o=(h,v=l.value)=>{for(let y=0;y<v.length;y++){if(v[y].id===h)return v[y];if(v[y].children){const T=o(h,v[y].children);if(T)return T}}return null},l=X([]),a=X(new Set),c=he(()=>{const h=[],v=(y,T=0,V=null)=>{y.forEach(U=>{h.push({...U,level:T,parentId:V,expanded:a.value.has(U.id)}),U.children&&v(U.children,T+1,U.id)})};return v(l.value),h}),s=he(()=>{const h=[],v=y=>{if(y.level===0)return!0;let T=c.value.find(V=>V.id===y.parentId);for(;T;){if(!T.expanded)return!1;T=c.value.find(V=>V.id===T.parentId)}return!0};return c.value.forEach(y=>{v(y)&&h.push(y)}),h}),u=X(!1),f=X([]),d=X(null);ln(async()=>{await S()});const p=X(!1),_=h=>{const v=[];function y(T,V){for(const U of T){const w=[...V,U.id];if(U.id===h)return v.push(...w),!0;if(U.children&&U.children.length>0&&y(U.children,w))return!0}return!1}return y(l.value,[]),v},E=h=>{if(d.value&&d.value.resetFields(),t.value=!1,i.id=null,i.name="",i.code="",i.parentId=null,i.parentIdPath=[],i.sort="top",p.value=!!h,h){const v=o(h);v&&(i.parentId=h,console.log("parentId位置一:",h),i.parentIdPath=_(h),f.value=v.children||[])}else i.parentId=null,i.parentIdPath=[],f.value=[];u.value=!0,console.log("dialogVisible 已设为 true")},R=h=>{d.value&&d.value.resetFields(),t.value=!0,i.id=h.id,i.name=h.unit_name,i.code=h.code,i.parentId=h.parent_id,i.parentIdPath=_(h.parent_id);const v=o(h.parent_id);v?f.value=v.children||[]:f.value=l.value,f.value.some(y=>y.id===h.sort_after_id)?i.sort=h.sort_after_id:i.sort="top",console.log("formData.sort:",i.sort),u.value=!0},A=()=>{d.value.validate(async h=>{if(h)try{let v=0;i.sort!=="top"&&(v=i.sort);const y=new FormData;i.id?(y.append("action","edit"),y.append("id",i.id)):y.append("action","add"),y.append("unit_name",i.name),y.append("code",i.code),console.log("formData.parentId:",i.parentId),y.append("parent_id",i.parentId||null),y.append("after_unit_id",v);const T=await Re.post("api/unit_manage.php",y,{headers:{"Content-Type":"multipart/form-data"}});T.status==="success"?(await S(),u.value=!1,pe.success(i.id?"编辑成功":"新增成功")):pe.error(T.message)}catch(v){console.error("保存单位失败:",v),pe.error("保存单位失败，请稍后重试")}})},S=async()=>{const h=await Re.post("api/get_unit_info.php");l.value=[h.data],console.log("获取单位数据成功:",l.value);const v=y=>{y.forEach(T=>{T.children&&T.children.length>0&&(a.value.add(T.id),v(T.children))})};v(l.value)},P=h=>{Po.confirm(`确定要删除 ${h.unit_name} 吗？`,"Warning",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const v=new FormData;v.append("action","del"),v.append("id",h.id),await Re.post("api/unit_manage.php",v),pe.success("删除成功"),await S()})},$=h=>{const v=new Set(a.value);v.has(h.id)?v.delete(h.id):v.add(h.id),a.value=v},O=h=>((h.parentId?o(h.parentId):{children:l.value}).children||[]).findIndex(V=>V.id===h.id)===0,M=h=>{const y=(h.parentId?o(h.parentId):{children:l.value}).children||[];return y.findIndex(V=>V.id===h.id)===y.length-1},x=async h=>{const v=new FormData;v.append("action","edit"),v.append("id",h.id),v.append("sort_order",h.sort_order-1),v.append("unit_name",h.unit_name),v.append("code",h.code),v.append("parent_id",h.parentId),await Re.post("api/unit_manage.php",v),await S()},g=async h=>{const v=new FormData;v.append("action","edit"),v.append("id",h.id),v.append("sort_order",h.sort_order+1),v.append("unit_name",h.unit_name),v.append("code",h.code),v.append("parent_id",h.parentId),await Re.post("api/unit_manage.php",v),await S()};return(h,v)=>{const y=H("el-button"),T=H("el-col"),V=H("el-row"),U=H("el-table-column"),w=H("el-icon"),D=H("el-button-group"),K=H("el-table"),Q=H("el-input"),j=H("el-form-item"),C=H("el-cascader"),B=H("el-option"),fe=H("el-select"),ue=H("el-form");return ae(),we("div",Kl,[m(V,null,{default:k(()=>[m(T,{span:24},{default:k(()=>[L("div",Wl,[m(y,{type:"primary",round:"",onClick:v[0]||(v[0]=q=>E(null))},{default:k(()=>v[7]||(v[7]=[le("添加单位")])),_:1,__:[7]})])]),_:1})]),_:1}),m(K,{data:s.value,style:{width:"100%"},border:""},{default:k(()=>[m(U,{label:"操作",width:"60",align:"center"},{default:k(q=>[q.row.children&&q.row.children.length?(ae(),pt(y,{key:0,size:"mini",type:"text",onClick:ce=>$(q.row)},{default:k(()=>[le(Qe(q.row.expanded?"-":"+"),1)]),_:2},1032,["onClick"])):Ht("",!0)]),_:1}),m(U,{prop:"unit_name",label:"单位名称"},{default:k(q=>[L("span",{style:Qn({paddingLeft:`${q.row.level*20}px`})},Qe(q.row.unit_name),5)]),_:1}),m(U,{prop:"code",label:"单位编号",width:"250"}),m(U,{label:"操作",width:"350"},{default:k(q=>[m(D,null,{default:k(()=>[m(y,{size:"mini",type:"primary",onClick:ce=>E(q.row.id)},{default:k(()=>[m(w,null,{default:k(()=>[m(se(bo))]),_:1})]),_:2},1032,["onClick"]),m(y,{size:"mini",type:"warning",onClick:ce=>R(q.row)},{default:k(()=>[m(w,null,{default:k(()=>[m(se(Eo))]),_:1})]),_:2},1032,["onClick"]),m(y,{size:"mini",type:"danger",onClick:ce=>P(q.row)},{default:k(()=>[m(w,null,{default:k(()=>[m(se(So))]),_:1})]),_:2},1032,["onClick"]),m(y,{size:"mini",type:"info",onClick:ce=>x(q.row),disabled:O(q.row)},{default:k(()=>[m(w,null,{default:k(()=>[m(se(ko))]),_:1})]),_:2},1032,["onClick","disabled"]),m(y,{size:"mini",type:"info",onClick:ce=>g(q.row),disabled:M(q.row)},{default:k(()=>[m(w,null,{default:k(()=>[m(se(Ro))]),_:1})]),_:2},1032,["onClick","disabled"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),m(se(xo),{modelValue:u.value,"onUpdate:modelValue":v[6]||(v[6]=q=>u.value=q),title:"",width:"450px","close-on-click-modal":!1},{footer:k(()=>[m(y,{onClick:v[5]||(v[5]=q=>u.value=!1)},{default:k(()=>v[8]||(v[8]=[le("取消")])),_:1,__:[8]}),m(y,{type:"primary",onClick:A},{default:k(()=>v[9]||(v[9]=[le("确定")])),_:1,__:[9]})]),default:k(()=>[m(ue,{model:i,ref_key:"formRef",ref:d,"label-width":"130px"},{default:k(()=>[m(j,{label:"单位名称",prop:"name",required:""},{default:k(()=>[m(Q,{modelValue:i.name,"onUpdate:modelValue":v[1]||(v[1]=q=>i.name=q),required:""},null,8,["modelValue"])]),_:1}),m(j,{label:"单位编码",prop:"code",required:""},{default:k(()=>[m(Q,{modelValue:i.code,"onUpdate:modelValue":v[2]||(v[2]=q=>i.code=q),required:""},null,8,["modelValue"])]),_:1}),m(j,{label:"上级单位",prop:"parentId"},{default:k(()=>[m(C,{modelValue:i.parentIdPath,"onUpdate:modelValue":v[3]||(v[3]=q=>i.parentIdPath=q),options:l.value,props:n,onChange:r,style:{width:"100%"},"show-all-levels":!1,placeholder:"请选择上级单位"},null,8,["modelValue","options"])]),_:1}),t.value?Ht("",!0):(ae(),pt(j,{key:0,label:"排序",prop:"sort",required:""},{default:k(()=>[m(fe,{modelValue:i.sort,"onUpdate:modelValue":v[4]||(v[4]=q=>i.sort=q),placeholder:"请选择排序位置"},{default:k(()=>[m(B,{label:"置于 最前",value:"top"}),(ae(!0),we(gt,null,zt(f.value,q=>(ae(),pt(B,{key:q.id,label:`置于 ${q.unit_name} 之后`,value:q.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}))]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Zl={class:"user-management-container"},Jl={class:"left-panel"},jl=["onClick"],ea={class:"right-panel"},ta={class:"action-buttons"},na={class:"form-row"},ra={class:"form-row"},ia={class:"form-row"},oa={class:"form-row"},la={class:"form-row"},aa={class:"form-row"},sa={class:"form-row"},ua={class:"form-row"},ca={class:"dialog-footer"},fa={__name:"UserManagement",setup(e){const t=X([]),n=X([]),r=X(""),i=X([]),o=X([]),l=X(!1),a=he(()=>i.value),c=he(()=>r.value?n.value.filter(x=>x.show&&x.unit_name.toLowerCase().includes(r.value.toLowerCase())):n.value.filter(x=>x.show)),s=on({name:"",id_number:"",phone:"",archive_birthdate:null,gender:null,short_code:"",alt_phone_1:"",alt_phone_2:"",landline:"",organization_unit:null,work_unit:null,employment_date:null,political_status:"",party_join_date:null,personnel_type:"",police_number:"",is_assisting_officer:null,employment_status:"",job_rank:"",current_rank_date:null,position:"",current_position_date:null,sorted_order:null,remark:""}),u={name:[{required:!0,message:"",trigger:"blur"}],id_number:[{required:!1,message:"",trigger:"blur"},{pattern:/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,message:"请输入正确的身份证号",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],archive_birthdate:[{required:!0,message:"请选择档案出生时间",trigger:"change"}]},f=X(null);ln(async()=>{try{const x=await Re.post("api/get_unit_info.php");x.status==="success"?(t.value=[x.data],d(x.data),console.log("获取部门数据成功:",t.value)):pe.error(x.message)}catch(x){console.error("获取部门数据失败:",x),pe.error("获取部门数据失败，请稍后重试")}});const d=(x,g=0,h=null)=>{const v={...x,level:g,expanded:x.children&&x.children.length>0,parent:h,indent:g*20,show:!0};n.value.push(v),x.children&&x.children.length>0&&x.children.forEach(y=>{d(y,g+1,v)})},p=x=>{x.expanded=!x.expanded;const g=n.value.indexOf(x)+1;let h=x.level+1;for(let v=g;v<n.value.length;v++){const y=n.value[v];if(y.level<=x.level)break;y.level===h?y.show=x.expanded:y.level>h&&(y.show=x.expanded&&n.value[v-1].show)}},_=async x=>{console.log("点击的部门:",x)},E=()=>{l.value=!0,f.value&&f.value.resetFields()},R=()=>{if(o.value.length===0){pe.warning("请先选择要删除的用户");return}pe.success(`已删除 ${o.value.length} 个用户`),o.value=[]},A=x=>{console.log("编辑用户:",x)},S=x=>{console.log("查看用户详情:",x)},P=x=>{console.log("删除用户:",x)},$=x=>{o.value=x},O=x=>{console.log("点击的部门名称:",x)},M=async()=>{f.value&&await f.value.validate(async x=>{if(x)try{console.log("提交的用户数据:",s),pe.success("模拟新增用户成功"),l.value=!1}catch(g){console.error("新增用户失败:",g),pe.error("新增用户失败，请稍后重试")}})};return(x,g)=>{const h=H("el-input"),v=H("el-button"),y=H("el-table-column"),T=H("el-table"),V=H("el-form-item"),U=H("el-date-picker"),w=H("el-option"),D=H("el-select"),K=H("el-cascader"),Q=H("el-form"),j=H("el-dialog");return ae(),we("div",Zl,[L("div",Jl,[m(h,{modelValue:r.value,"onUpdate:modelValue":g[0]||(g[0]=C=>r.value=C),placeholder:"搜索单位",class:"department-search"},null,8,["modelValue"]),m(T,{data:c.value,border:"",class:"department-table",onRowClick:_},{default:k(()=>[m(y,{label:"操作",width:"60"},{default:k(({row:C})=>[C.children&&C.children.length>0?(ae(),pt(v,{key:0,type:"text",size:"small",onClick:fi(B=>p(C),["stop"])},{default:k(()=>[le(Qe(C.expanded?"-":"+"),1)]),_:2},1032,["onClick"])):Ht("",!0)]),_:1}),m(y,{prop:"unit_name",label:"单位名称"},{default:k(({row:C})=>[L("span",{class:"indent",style:Qn({width:`${C.indent}px`})},null,4),L("span",{onClick:B=>O(C),style:{cursor:"pointer"}},Qe(C.unit_name),9,jl)]),_:1})]),_:1},8,["data"])]),L("div",ea,[L("div",ta,[m(v,{type:"primary",onClick:E},{default:k(()=>g[27]||(g[27]=[le("新增")])),_:1,__:[27]}),m(v,{type:"danger",onClick:R},{default:k(()=>g[28]||(g[28]=[le("删除")])),_:1,__:[28]})]),m(T,{data:a.value,border:"",class:"user-table",onSelectionChange:$},{default:k(()=>[m(y,{type:"selection",width:"55"}),m(y,{prop:"name",label:"姓名"}),m(y,{prop:"id_number",label:"身份证号",width:"150"}),m(y,{prop:"phone",label:"手机号码"}),m(y,{prop:"gender",label:"性别"}),m(y,{prop:"short_code",label:"短号"}),m(y,{prop:"organization_unit",label:"编制单位"}),m(y,{prop:"work_unit",label:"工作单位"}),m(y,{prop:"personnel_type",label:"人员身份"}),m(y,{prop:"employment_status",label:"人员状态"}),m(y,{prop:"remark",label:"备注"}),m(y,{label:"操作",width:"200"},{default:k(({row:C})=>[m(v,{type:"primary",onClick:B=>A(C)},{default:k(()=>g[29]||(g[29]=[le("编辑")])),_:2,__:[29]},1032,["onClick"]),m(v,{type:"success",onClick:B=>S(C)},{default:k(()=>g[30]||(g[30]=[le("详情")])),_:2,__:[30]},1032,["onClick"]),m(v,{type:"danger",onClick:B=>P(C)},{default:k(()=>g[31]||(g[31]=[le("删除")])),_:2,__:[31]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),m(j,{modelValue:l.value,"onUpdate:modelValue":g[26]||(g[26]=C=>l.value=C),title:"",width:"1000px"},{footer:k(()=>[L("span",ca,[m(v,{onClick:g[25]||(g[25]=C=>l.value=!1)},{default:k(()=>g[33]||(g[33]=[le("取消")])),_:1,__:[33]}),m(v,{type:"primary",onClick:M},{default:k(()=>g[34]||(g[34]=[le("确定")])),_:1,__:[34]})])]),default:k(()=>[m(Q,{model:s,rules:u,ref_key:"newUserFormRef",ref:f,"label-width":"120px",inline:!1,class:"user-form"},{default:k(()=>[L("div",na,[m(V,{label:"姓名",prop:"name",style:{flex:"1"}},{default:k(()=>[m(h,{modelValue:s.name,"onUpdate:modelValue":g[1]||(g[1]=C=>s.name=C)},null,8,["modelValue"])]),_:1}),m(V,{label:"身份证号",prop:"id_number",style:{flex:"1"}},{default:k(()=>[m(h,{modelValue:s.id_number,"onUpdate:modelValue":g[2]||(g[2]=C=>s.id_number=C),maxlength:"18"},null,8,["modelValue"])]),_:1}),m(V,{label:"手机号码",prop:"phone",style:{flex:"1"}},{default:k(()=>[m(h,{modelValue:s.phone,"onUpdate:modelValue":g[3]||(g[3]=C=>s.phone=C),maxlength:"11"},null,8,["modelValue"])]),_:1})]),L("div",ra,[m(V,{label:"档案出生日期",prop:"archive_birthdate",style:{flex:"1"}},{default:k(()=>[m(U,{modelValue:s.archive_birthdate,"onUpdate:modelValue":g[4]||(g[4]=C=>s.archive_birthdate=C),type:"date"},null,8,["modelValue"])]),_:1}),m(V,{label:"性别",prop:"gender",style:{flex:"1"}},{default:k(()=>[m(D,{modelValue:s.gender,"onUpdate:modelValue":g[5]||(g[5]=C=>s.gender=C)},{default:k(()=>[m(w,{label:"未知",value:"0"}),m(w,{label:"男",value:"1"}),m(w,{label:"女",value:"2"})]),_:1},8,["modelValue"])]),_:1}),m(V,{label:"备注",prop:"remark",style:{flex:"1"}},{default:k(()=>[m(h,{modelValue:s.remark,"onUpdate:modelValue":g[6]||(g[6]=C=>s.remark=C)},null,8,["modelValue"])]),_:1})]),L("div",ia,[m(V,{label:"短号",prop:"short_code",style:{flex:"1"}},{default:k(()=>[m(h,{modelValue:s.short_code,"onUpdate:modelValue":g[7]||(g[7]=C=>s.short_code=C)},null,8,["modelValue"]),g[32]||(g[32]=L("div",{class:""},null,-1))]),_:1,__:[32]}),m(V,{label:"手机号码2",prop:"alt_phone_1",style:{flex:"1"}},{default:k(()=>[m(h,{modelValue:s.alt_phone_1,"onUpdate:modelValue":g[8]||(g[8]=C=>s.alt_phone_1=C)},null,8,["modelValue"])]),_:1}),m(V,{label:"手机号码3",prop:"alt_phone_2",style:{flex:"1"}},{default:k(()=>[m(h,{modelValue:s.alt_phone_2,"onUpdate:modelValue":g[9]||(g[9]=C=>s.alt_phone_2=C)},null,8,["modelValue"])]),_:1})]),L("div",oa,[m(V,{label:"座机",prop:"landline",style:{flex:"1"}},{default:k(()=>[m(h,{modelValue:s.landline,"onUpdate:modelValue":g[10]||(g[10]=C=>s.landline=C)},null,8,["modelValue"])]),_:1}),m(V,{label:"编制单位",prop:"organization_unit",style:{flex:"1"},rules:[{required:!0,message:"请选择编制单位",trigger:"change"}]},{default:k(()=>[m(K,{modelValue:s.organization_unit,"onUpdate:modelValue":g[11]||(g[11]=C=>s.organization_unit=C),options:x.organizationOptions,props:{expandTrigger:"hover"},placeholder:"请选择编制单位"},null,8,["modelValue","options"])]),_:1}),m(V,{label:"工作单位",prop:"work_unit",style:{flex:"1"},rules:[{required:!0,message:"请选择工作单位",trigger:"change"}]},{default:k(()=>[m(K,{modelValue:s.work_unit,"onUpdate:modelValue":g[12]||(g[12]=C=>s.work_unit=C),options:x.workUnitOptions,props:{expandTrigger:"hover"},placeholder:"请选择工作单位"},null,8,["modelValue","options"])]),_:1})]),L("div",la,[m(V,{label:"参工时间",prop:"employment_date",style:{flex:"1"}},{default:k(()=>[m(U,{modelValue:s.employment_date,"onUpdate:modelValue":g[13]||(g[13]=C=>s.employment_date=C),type:"date"},null,8,["modelValue"])]),_:1}),m(V,{label:"政治面貌",prop:"political_status",style:{flex:"1"}},{default:k(()=>[m(D,{modelValue:s.political_status,"onUpdate:modelValue":g[14]||(g[14]=C=>s.political_status=C),placeholder:"请选择政治面貌"},{default:k(()=>[m(w,{label:"中共党员",value:"中共党员"}),m(w,{label:"中共预备党员",value:"中共预备党员"}),m(w,{label:"共青团员",value:"共青团员"}),m(w,{label:"民主党派",value:"民主党派"}),m(w,{label:"无党派人士",value:"无党派人士"}),m(w,{label:"群众",value:"群众"})]),_:1},8,["modelValue"])]),_:1}),m(V,{label:"加入组织时间",prop:"party_join_date",style:{flex:"1"}},{default:k(()=>[m(U,{modelValue:s.party_join_date,"onUpdate:modelValue":g[15]||(g[15]=C=>s.party_join_date=C),type:"date"},null,8,["modelValue"])]),_:1})]),L("div",aa,[m(V,{label:"人员身份",prop:"personnel_type",style:{flex:"1"}},{default:k(()=>[m(D,{modelValue:s.personnel_type,"onUpdate:modelValue":g[16]||(g[16]=C=>s.personnel_type=C)},{default:k(()=>[m(w,{label:"民警",value:"民警"}),m(w,{label:"职工",value:"职工"}),m(w,{label:"辅警",value:"辅警"}),m(w,{label:"机关工勤",value:"机关工勤"}),m(w,{label:"其他",value:"其他"})]),_:1},8,["modelValue"])]),_:1}),m(V,{label:"警号/辅警号",prop:"police_number",style:{flex:"1"}},{default:k(()=>[m(h,{modelValue:s.police_number,"onUpdate:modelValue":g[17]||(g[17]=C=>s.police_number=C)},null,8,["modelValue"])]),_:1}),m(V,{label:"带辅民警",prop:"is_assisting_officer",style:{flex:"1"}},{default:k(()=>[m(D,{modelValue:s.is_assisting_officer,"onUpdate:modelValue":g[18]||(g[18]=C=>s.is_assisting_officer=C)},{default:k(()=>[m(w,{label:"否",value:"0"}),m(w,{label:"是",value:"1"})]),_:1},8,["modelValue"])]),_:1})]),L("div",sa,[m(V,{label:"人员状态",prop:"employment_status",style:{flex:"1"}},{default:k(()=>[m(D,{modelValue:s.employment_status,"onUpdate:modelValue":g[19]||(g[19]=C=>s.employment_status=C)},{default:k(()=>[m(w,{label:"在职",value:"在职"}),m(w,{label:"调离",value:"调离"}),m(w,{label:"退休",value:"退休"}),m(w,{label:"开除",value:"开除"}),m(w,{label:"借调出局",value:"借调出局"})]),_:1},8,["modelValue"])]),_:1}),m(V,{label:"职级",prop:"job_rank",style:{flex:"1"}},{default:k(()=>[m(D,{modelValue:s.job_rank,"onUpdate:modelValue":g[20]||(g[20]=C=>s.job_rank=C)},{default:k(()=>[m(w,{label:"初级",value:"初级"}),m(w,{label:"中级",value:"中级"}),m(w,{label:"高级",value:"高级"})]),_:1},8,["modelValue"])]),_:1}),m(V,{label:"任现职级时间",prop:"current_rank_date",style:{flex:"1"}},{default:k(()=>[m(U,{modelValue:s.current_rank_date,"onUpdate:modelValue":g[21]||(g[21]=C=>s.current_rank_date=C),type:"date"},null,8,["modelValue"])]),_:1})]),L("div",ua,[m(V,{label:"职务",prop:"position",style:{flex:"1"}},{default:k(()=>[m(h,{modelValue:s.position,"onUpdate:modelValue":g[22]||(g[22]=C=>s.position=C)},null,8,["modelValue"])]),_:1}),m(V,{label:"任现职务时间",prop:"current_position_date",style:{flex:"1"}},{default:k(()=>[m(U,{modelValue:s.current_position_date,"onUpdate:modelValue":g[23]||(g[23]=C=>s.current_position_date=C),type:"date"},null,8,["modelValue"])]),_:1}),m(V,{label:"人员排序",prop:"sorted_order",style:{flex:"1"}},{default:k(()=>[m(h,{modelValue:s.sorted_order,"onUpdate:modelValue":g[24]||(g[24]=C=>s.sorted_order=C),modelModifiers:{number:!0},type:"number"},null,8,["modelValue"])]),_:1})])]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},da=ci(fa,[["__scopeId","data-v-2f3d37fe"]]);class Fe{constructor(){this._partials=new Float64Array(32),this._n=0}add(t){const n=this._partials;let r=0;for(let i=0;i<this._n&&i<32;i++){const o=n[i],l=t+o,a=Math.abs(t)<Math.abs(o)?t-(l-o):o-(l-t);a&&(n[r++]=a),t=l}return n[r]=t,this._n=r+1,this}valueOf(){const t=this._partials;let n=this._n,r,i,o,l=0;if(n>0){for(l=t[--n];n>0&&(r=l,i=t[--n],l=r+i,o=i-(l-r),!o););n>0&&(o<0&&t[n-1]<0||o>0&&t[n-1]>0)&&(i=o*2,r=l+i,i==r-l&&(l=r))}return l}}function*pa(e){for(const t of e)yield*t}function ki(e){return Array.from(pa(e))}var ha={value:()=>{}};function Ri(){for(var e=0,t=arguments.length,n={},r;e<t;++e){if(!(r=arguments[e]+"")||r in n||/[\s.]/.test(r))throw new Error("illegal type: "+r);n[r]=[]}return new Dt(n)}function Dt(e){this._=e}function ma(e,t){return e.trim().split(/^|\s+/).map(function(n){var r="",i=n.indexOf(".");if(i>=0&&(r=n.slice(i+1),n=n.slice(0,i)),n&&!t.hasOwnProperty(n))throw new Error("unknown type: "+n);return{type:n,name:r}})}Dt.prototype=Ri.prototype={constructor:Dt,on:function(e,t){var n=this._,r=ma(e+"",n),i,o=-1,l=r.length;if(arguments.length<2){for(;++o<l;)if((i=(e=r[o]).type)&&(i=ga(n[i],e.name)))return i;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++o<l;)if(i=(e=r[o]).type)n[i]=Rr(n[i],e.name,t);else if(t==null)for(i in n)n[i]=Rr(n[i],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new Dt(e)},call:function(e,t){if((i=arguments.length-2)>0)for(var n=new Array(i),r=0,i,o;r<i;++r)n[r]=arguments[r+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(o=this._[e],r=0,i=o.length;r<i;++r)o[r].value.apply(t,n)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var r=this._[e],i=0,o=r.length;i<o;++i)r[i].value.apply(t,n)}};function ga(e,t){for(var n=0,r=e.length,i;n<r;++n)if((i=e[n]).name===t)return i.value}function Rr(e,t,n){for(var r=0,i=e.length;r<i;++r)if(e[r].name===t){e[r]=ha,e=e.slice(0,r).concat(e.slice(r+1));break}return n!=null&&e.push({name:t,value:n}),e}var Pn="http://www.w3.org/1999/xhtml";const Pr={svg:"http://www.w3.org/2000/svg",xhtml:Pn,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function un(e){var t=e+="",n=t.indexOf(":");return n>=0&&(t=e.slice(0,n))!=="xmlns"&&(e=e.slice(n+1)),Pr.hasOwnProperty(t)?{space:Pr[t],local:e}:e}function va(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===Pn&&t.documentElement.namespaceURI===Pn?t.createElement(e):t.createElementNS(n,e)}}function _a(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function Pi(e){var t=un(e);return(t.local?_a:va)(t)}function ya(){}function Jn(e){return e==null?ya:function(){return this.querySelector(e)}}function wa(e){typeof e!="function"&&(e=Jn(e));for(var t=this._groups,n=t.length,r=new Array(n),i=0;i<n;++i)for(var o=t[i],l=o.length,a=r[i]=new Array(l),c,s,u=0;u<l;++u)(c=o[u])&&(s=e.call(c,c.__data__,u,o))&&("__data__"in c&&(s.__data__=c.__data__),a[u]=s);return new _e(r,this._parents)}function xa(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function ba(){return[]}function Ci(e){return e==null?ba:function(){return this.querySelectorAll(e)}}function Ea(e){return function(){return xa(e.apply(this,arguments))}}function Sa(e){typeof e=="function"?e=Ea(e):e=Ci(e);for(var t=this._groups,n=t.length,r=[],i=[],o=0;o<n;++o)for(var l=t[o],a=l.length,c,s=0;s<a;++s)(c=l[s])&&(r.push(e.call(c,c.__data__,s,l)),i.push(c));return new _e(r,i)}function $i(e){return function(){return this.matches(e)}}function Mi(e){return function(t){return t.matches(e)}}var ka=Array.prototype.find;function Ra(e){return function(){return ka.call(this.children,e)}}function Pa(){return this.firstElementChild}function Ca(e){return this.select(e==null?Pa:Ra(typeof e=="function"?e:Mi(e)))}var $a=Array.prototype.filter;function Ma(){return Array.from(this.children)}function Aa(e){return function(){return $a.call(this.children,e)}}function Va(e){return this.selectAll(e==null?Ma:Aa(typeof e=="function"?e:Mi(e)))}function Na(e){typeof e!="function"&&(e=$i(e));for(var t=this._groups,n=t.length,r=new Array(n),i=0;i<n;++i)for(var o=t[i],l=o.length,a=r[i]=[],c,s=0;s<l;++s)(c=o[s])&&e.call(c,c.__data__,s,o)&&a.push(c);return new _e(r,this._parents)}function Ai(e){return new Array(e.length)}function Ia(){return new _e(this._enter||this._groups.map(Ai),this._parents)}function Bt(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}Bt.prototype={constructor:Bt,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function Ta(e){return function(){return e}}function Oa(e,t,n,r,i,o){for(var l=0,a,c=t.length,s=o.length;l<s;++l)(a=t[l])?(a.__data__=o[l],r[l]=a):n[l]=new Bt(e,o[l]);for(;l<c;++l)(a=t[l])&&(i[l]=a)}function Ua(e,t,n,r,i,o,l){var a,c,s=new Map,u=t.length,f=o.length,d=new Array(u),p;for(a=0;a<u;++a)(c=t[a])&&(d[a]=p=l.call(c,c.__data__,a,t)+"",s.has(p)?i[a]=c:s.set(p,c));for(a=0;a<f;++a)p=l.call(e,o[a],a,o)+"",(c=s.get(p))?(r[a]=c,c.__data__=o[a],s.delete(p)):n[a]=new Bt(e,o[a]);for(a=0;a<u;++a)(c=t[a])&&s.get(d[a])===c&&(i[a]=c)}function Da(e){return e.__data__}function La(e,t){if(!arguments.length)return Array.from(this,Da);var n=t?Ua:Oa,r=this._parents,i=this._groups;typeof e!="function"&&(e=Ta(e));for(var o=i.length,l=new Array(o),a=new Array(o),c=new Array(o),s=0;s<o;++s){var u=r[s],f=i[s],d=f.length,p=Fa(e.call(u,u&&u.__data__,s,r)),_=p.length,E=a[s]=new Array(_),R=l[s]=new Array(_),A=c[s]=new Array(d);n(u,f,E,R,A,p,t);for(var S=0,P=0,$,O;S<_;++S)if($=E[S]){for(S>=P&&(P=S+1);!(O=R[P])&&++P<_;);$._next=O||null}}return l=new _e(l,r),l._enter=a,l._exit=c,l}function Fa(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function qa(){return new _e(this._exit||this._groups.map(Ai),this._parents)}function za(e,t,n){var r=this.enter(),i=this,o=this.exit();return typeof e=="function"?(r=e(r),r&&(r=r.selection())):r=r.append(e+""),t!=null&&(i=t(i),i&&(i=i.selection())),n==null?o.remove():n(o),r&&i?r.merge(i).order():i}function Ha(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,i=n.length,o=r.length,l=Math.min(i,o),a=new Array(i),c=0;c<l;++c)for(var s=n[c],u=r[c],f=s.length,d=a[c]=new Array(f),p,_=0;_<f;++_)(p=s[_]||u[_])&&(d[_]=p);for(;c<i;++c)a[c]=n[c];return new _e(a,this._parents)}function Ba(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r=e[t],i=r.length-1,o=r[i],l;--i>=0;)(l=r[i])&&(o&&l.compareDocumentPosition(o)^4&&o.parentNode.insertBefore(l,o),o=l);return this}function Xa(e){e||(e=Ga);function t(f,d){return f&&d?e(f.__data__,d.__data__):!f-!d}for(var n=this._groups,r=n.length,i=new Array(r),o=0;o<r;++o){for(var l=n[o],a=l.length,c=i[o]=new Array(a),s,u=0;u<a;++u)(s=l[u])&&(c[u]=s);c.sort(t)}return new _e(i,this._parents).order()}function Ga(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function Ya(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function Ka(){return Array.from(this)}function Wa(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],i=0,o=r.length;i<o;++i){var l=r[i];if(l)return l}return null}function Qa(){let e=0;for(const t of this)++e;return e}function Za(){return!this.node()}function Ja(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var i=t[n],o=0,l=i.length,a;o<l;++o)(a=i[o])&&e.call(a,a.__data__,o,i);return this}function ja(e){return function(){this.removeAttribute(e)}}function es(e){return function(){this.removeAttributeNS(e.space,e.local)}}function ts(e,t){return function(){this.setAttribute(e,t)}}function ns(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function rs(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttribute(e):this.setAttribute(e,n)}}function is(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function os(e,t){var n=un(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((t==null?n.local?es:ja:typeof t=="function"?n.local?is:rs:n.local?ns:ts)(n,t))}function Vi(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function ls(e){return function(){this.style.removeProperty(e)}}function as(e,t,n){return function(){this.style.setProperty(e,t,n)}}function ss(e,t,n){return function(){var r=t.apply(this,arguments);r==null?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}function us(e,t,n){return arguments.length>1?this.each((t==null?ls:typeof t=="function"?ss:as)(e,t,n??"")):je(this.node(),e)}function je(e,t){return e.style.getPropertyValue(t)||Vi(e).getComputedStyle(e,null).getPropertyValue(t)}function cs(e){return function(){delete this[e]}}function fs(e,t){return function(){this[e]=t}}function ds(e,t){return function(){var n=t.apply(this,arguments);n==null?delete this[e]:this[e]=n}}function ps(e,t){return arguments.length>1?this.each((t==null?cs:typeof t=="function"?ds:fs)(e,t)):this.node()[e]}function Ni(e){return e.trim().split(/^|\s+/)}function jn(e){return e.classList||new Ii(e)}function Ii(e){this._node=e,this._names=Ni(e.getAttribute("class")||"")}Ii.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function Ti(e,t){for(var n=jn(e),r=-1,i=t.length;++r<i;)n.add(t[r])}function Oi(e,t){for(var n=jn(e),r=-1,i=t.length;++r<i;)n.remove(t[r])}function hs(e){return function(){Ti(this,e)}}function ms(e){return function(){Oi(this,e)}}function gs(e,t){return function(){(t.apply(this,arguments)?Ti:Oi)(this,e)}}function vs(e,t){var n=Ni(e+"");if(arguments.length<2){for(var r=jn(this.node()),i=-1,o=n.length;++i<o;)if(!r.contains(n[i]))return!1;return!0}return this.each((typeof t=="function"?gs:t?hs:ms)(n,t))}function _s(){this.textContent=""}function ys(e){return function(){this.textContent=e}}function ws(e){return function(){var t=e.apply(this,arguments);this.textContent=t??""}}function xs(e){return arguments.length?this.each(e==null?_s:(typeof e=="function"?ws:ys)(e)):this.node().textContent}function bs(){this.innerHTML=""}function Es(e){return function(){this.innerHTML=e}}function Ss(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t??""}}function ks(e){return arguments.length?this.each(e==null?bs:(typeof e=="function"?Ss:Es)(e)):this.node().innerHTML}function Rs(){this.nextSibling&&this.parentNode.appendChild(this)}function Ps(){return this.each(Rs)}function Cs(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function $s(){return this.each(Cs)}function Ms(e){var t=typeof e=="function"?e:Pi(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function As(){return null}function Vs(e,t){var n=typeof e=="function"?e:Pi(e),r=t==null?As:typeof t=="function"?t:Jn(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})}function Ns(){var e=this.parentNode;e&&e.removeChild(this)}function Is(){return this.each(Ns)}function Ts(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Os(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Us(e){return this.select(e?Os:Ts)}function Ds(e){return arguments.length?this.property("__data__",e):this.node().__data__}function Ls(e){return function(t){e.call(this,t,this.__data__)}}function Fs(e){return e.trim().split(/^|\s+/).map(function(t){var n="",r=t.indexOf(".");return r>=0&&(n=t.slice(r+1),t=t.slice(0,r)),{type:t,name:n}})}function qs(e){return function(){var t=this.__on;if(t){for(var n=0,r=-1,i=t.length,o;n<i;++n)o=t[n],(!e.type||o.type===e.type)&&o.name===e.name?this.removeEventListener(o.type,o.listener,o.options):t[++r]=o;++r?t.length=r:delete this.__on}}}function zs(e,t,n){return function(){var r=this.__on,i,o=Ls(t);if(r){for(var l=0,a=r.length;l<a;++l)if((i=r[l]).type===e.type&&i.name===e.name){this.removeEventListener(i.type,i.listener,i.options),this.addEventListener(i.type,i.listener=o,i.options=n),i.value=t;return}}this.addEventListener(e.type,o,n),i={type:e.type,name:e.name,value:t,listener:o,options:n},r?r.push(i):this.__on=[i]}}function Hs(e,t,n){var r=Fs(e+""),i,o=r.length,l;if(arguments.length<2){var a=this.node().__on;if(a){for(var c=0,s=a.length,u;c<s;++c)for(i=0,u=a[c];i<o;++i)if((l=r[i]).type===u.type&&l.name===u.name)return u.value}return}for(a=t?zs:qs,i=0;i<o;++i)this.each(a(r[i],t,n));return this}function Ui(e,t,n){var r=Vi(e),i=r.CustomEvent;typeof i=="function"?i=new i(t,n):(i=r.document.createEvent("Event"),n?(i.initEvent(t,n.bubbles,n.cancelable),i.detail=n.detail):i.initEvent(t,!1,!1)),e.dispatchEvent(i)}function Bs(e,t){return function(){return Ui(this,e,t)}}function Xs(e,t){return function(){return Ui(this,e,t.apply(this,arguments))}}function Gs(e,t){return this.each((typeof t=="function"?Xs:Bs)(e,t))}function*Ys(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],i=0,o=r.length,l;i<o;++i)(l=r[i])&&(yield l)}var Di=[null];function _e(e,t){this._groups=e,this._parents=t}function Pt(){return new _e([[document.documentElement]],Di)}function Ks(){return this}_e.prototype=Pt.prototype={constructor:_e,select:wa,selectAll:Sa,selectChild:Ca,selectChildren:Va,filter:Na,data:La,enter:Ia,exit:qa,join:za,merge:Ha,selection:Ks,order:Ba,sort:Xa,call:Ya,nodes:Ka,node:Wa,size:Qa,empty:Za,each:Ja,attr:os,style:us,property:ps,classed:vs,text:xs,html:ks,raise:Ps,lower:$s,append:Ms,insert:Vs,remove:Is,clone:Us,datum:Ds,on:Hs,dispatch:Gs,[Symbol.iterator]:Ys};function $t(e){return typeof e=="string"?new _e([[document.querySelector(e)]],[document.documentElement]):new _e([[e]],Di)}function er(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function Li(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function Ct(){}var yt=.7,Xt=1/yt,Ke="\\s*([+-]?\\d+)\\s*",wt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",$e="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Ws=/^#([0-9a-f]{3,8})$/,Qs=new RegExp(`^rgb\\(${Ke},${Ke},${Ke}\\)$`),Zs=new RegExp(`^rgb\\(${$e},${$e},${$e}\\)$`),Js=new RegExp(`^rgba\\(${Ke},${Ke},${Ke},${wt}\\)$`),js=new RegExp(`^rgba\\(${$e},${$e},${$e},${wt}\\)$`),eu=new RegExp(`^hsl\\(${wt},${$e},${$e}\\)$`),tu=new RegExp(`^hsla\\(${wt},${$e},${$e},${wt}\\)$`),Cr={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};er(Ct,xt,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:$r,formatHex:$r,formatHex8:nu,formatHsl:ru,formatRgb:Mr,toString:Mr});function $r(){return this.rgb().formatHex()}function nu(){return this.rgb().formatHex8()}function ru(){return Fi(this).formatHsl()}function Mr(){return this.rgb().formatRgb()}function xt(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=Ws.exec(e))?(n=t[1].length,t=parseInt(t[1],16),n===6?Ar(t):n===3?new me(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):n===8?Mt(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):n===4?Mt(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=Qs.exec(e))?new me(t[1],t[2],t[3],1):(t=Zs.exec(e))?new me(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=Js.exec(e))?Mt(t[1],t[2],t[3],t[4]):(t=js.exec(e))?Mt(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=eu.exec(e))?Ir(t[1],t[2]/100,t[3]/100,1):(t=tu.exec(e))?Ir(t[1],t[2]/100,t[3]/100,t[4]):Cr.hasOwnProperty(e)?Ar(Cr[e]):e==="transparent"?new me(NaN,NaN,NaN,0):null}function Ar(e){return new me(e>>16&255,e>>8&255,e&255,1)}function Mt(e,t,n,r){return r<=0&&(e=t=n=NaN),new me(e,t,n,r)}function iu(e){return e instanceof Ct||(e=xt(e)),e?(e=e.rgb(),new me(e.r,e.g,e.b,e.opacity)):new me}function Cn(e,t,n,r){return arguments.length===1?iu(e):new me(e,t,n,r??1)}function me(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}er(me,Cn,Li(Ct,{brighter(e){return e=e==null?Xt:Math.pow(Xt,e),new me(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?yt:Math.pow(yt,e),new me(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new me(Le(this.r),Le(this.g),Le(this.b),Gt(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Vr,formatHex:Vr,formatHex8:ou,formatRgb:Nr,toString:Nr}));function Vr(){return`#${De(this.r)}${De(this.g)}${De(this.b)}`}function ou(){return`#${De(this.r)}${De(this.g)}${De(this.b)}${De((isNaN(this.opacity)?1:this.opacity)*255)}`}function Nr(){const e=Gt(this.opacity);return`${e===1?"rgb(":"rgba("}${Le(this.r)}, ${Le(this.g)}, ${Le(this.b)}${e===1?")":`, ${e})`}`}function Gt(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Le(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function De(e){return e=Le(e),(e<16?"0":"")+e.toString(16)}function Ir(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new Ee(e,t,n,r)}function Fi(e){if(e instanceof Ee)return new Ee(e.h,e.s,e.l,e.opacity);if(e instanceof Ct||(e=xt(e)),!e)return new Ee;if(e instanceof Ee)return e;e=e.rgb();var t=e.r/255,n=e.g/255,r=e.b/255,i=Math.min(t,n,r),o=Math.max(t,n,r),l=NaN,a=o-i,c=(o+i)/2;return a?(t===o?l=(n-r)/a+(n<r)*6:n===o?l=(r-t)/a+2:l=(t-n)/a+4,a/=c<.5?o+i:2-o-i,l*=60):a=c>0&&c<1?0:l,new Ee(l,a,c,e.opacity)}function lu(e,t,n,r){return arguments.length===1?Fi(e):new Ee(e,t,n,r??1)}function Ee(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}er(Ee,lu,Li(Ct,{brighter(e){return e=e==null?Xt:Math.pow(Xt,e),new Ee(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?yt:Math.pow(yt,e),new Ee(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,i=2*n-r;return new me(vn(e>=240?e-240:e+120,i,r),vn(e,i,r),vn(e<120?e+240:e-120,i,r),this.opacity)},clamp(){return new Ee(Tr(this.h),At(this.s),At(this.l),Gt(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Gt(this.opacity);return`${e===1?"hsl(":"hsla("}${Tr(this.h)}, ${At(this.s)*100}%, ${At(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Tr(e){return e=(e||0)%360,e<0?e+360:e}function At(e){return Math.max(0,Math.min(1,e||0))}function vn(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}const qi=e=>()=>e;function au(e,t){return function(n){return e+n*t}}function su(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(r){return Math.pow(e+r*t,n)}}function uu(e){return(e=+e)==1?zi:function(t,n){return n-t?su(t,n,e):qi(isNaN(t)?n:t)}}function zi(e,t){var n=t-e;return n?au(e,n):qi(isNaN(e)?t:e)}const Or=function e(t){var n=uu(t);function r(i,o){var l=n((i=Cn(i)).r,(o=Cn(o)).r),a=n(i.g,o.g),c=n(i.b,o.b),s=zi(i.opacity,o.opacity);return function(u){return i.r=l(u),i.g=a(u),i.b=c(u),i.opacity=s(u),i+""}}return r.gamma=e,r}(1);function Ue(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var $n=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,_n=new RegExp($n.source,"g");function cu(e){return function(){return e}}function fu(e){return function(t){return e(t)+""}}function du(e,t){var n=$n.lastIndex=_n.lastIndex=0,r,i,o,l=-1,a=[],c=[];for(e=e+"",t=t+"";(r=$n.exec(e))&&(i=_n.exec(t));)(o=i.index)>n&&(o=t.slice(n,o),a[l]?a[l]+=o:a[++l]=o),(r=r[0])===(i=i[0])?a[l]?a[l]+=i:a[++l]=i:(a[++l]=null,c.push({i:l,x:Ue(r,i)})),n=_n.lastIndex;return n<t.length&&(o=t.slice(n),a[l]?a[l]+=o:a[++l]=o),a.length<2?c[0]?fu(c[0].x):cu(t):(t=c.length,function(s){for(var u=0,f;u<t;++u)a[(f=c[u]).i]=f.x(s);return a.join("")})}var Ur=180/Math.PI,Mn={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Hi(e,t,n,r,i,o){var l,a,c;return(l=Math.sqrt(e*e+t*t))&&(e/=l,t/=l),(c=e*n+t*r)&&(n-=e*c,r-=t*c),(a=Math.sqrt(n*n+r*r))&&(n/=a,r/=a,c/=a),e*r<t*n&&(e=-e,t=-t,c=-c,l=-l),{translateX:i,translateY:o,rotate:Math.atan2(t,e)*Ur,skewX:Math.atan(c)*Ur,scaleX:l,scaleY:a}}var Vt;function pu(e){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?Mn:Hi(t.a,t.b,t.c,t.d,t.e,t.f)}function hu(e){return e==null||(Vt||(Vt=document.createElementNS("http://www.w3.org/2000/svg","g")),Vt.setAttribute("transform",e),!(e=Vt.transform.baseVal.consolidate()))?Mn:(e=e.matrix,Hi(e.a,e.b,e.c,e.d,e.e,e.f))}function Bi(e,t,n,r){function i(s){return s.length?s.pop()+" ":""}function o(s,u,f,d,p,_){if(s!==f||u!==d){var E=p.push("translate(",null,t,null,n);_.push({i:E-4,x:Ue(s,f)},{i:E-2,x:Ue(u,d)})}else(f||d)&&p.push("translate("+f+t+d+n)}function l(s,u,f,d){s!==u?(s-u>180?u+=360:u-s>180&&(s+=360),d.push({i:f.push(i(f)+"rotate(",null,r)-2,x:Ue(s,u)})):u&&f.push(i(f)+"rotate("+u+r)}function a(s,u,f,d){s!==u?d.push({i:f.push(i(f)+"skewX(",null,r)-2,x:Ue(s,u)}):u&&f.push(i(f)+"skewX("+u+r)}function c(s,u,f,d,p,_){if(s!==f||u!==d){var E=p.push(i(p)+"scale(",null,",",null,")");_.push({i:E-4,x:Ue(s,f)},{i:E-2,x:Ue(u,d)})}else(f!==1||d!==1)&&p.push(i(p)+"scale("+f+","+d+")")}return function(s,u){var f=[],d=[];return s=e(s),u=e(u),o(s.translateX,s.translateY,u.translateX,u.translateY,f,d),l(s.rotate,u.rotate,f,d),a(s.skewX,u.skewX,f,d),c(s.scaleX,s.scaleY,u.scaleX,u.scaleY,f,d),s=u=null,function(p){for(var _=-1,E=d.length,R;++_<E;)f[(R=d[_]).i]=R.x(p);return f.join("")}}}var mu=Bi(pu,"px, ","px)","deg)"),gu=Bi(hu,", ",")",")"),et=0,lt=0,ot=0,Xi=1e3,Yt,at,Kt=0,qe=0,cn=0,bt=typeof performance=="object"&&performance.now?performance:Date,Gi=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function tr(){return qe||(Gi(vu),qe=bt.now()+cn)}function vu(){qe=0}function Wt(){this._call=this._time=this._next=null}Wt.prototype=Yi.prototype={constructor:Wt,restart:function(e,t,n){if(typeof e!="function")throw new TypeError("callback is not a function");n=(n==null?tr():+n)+(t==null?0:+t),!this._next&&at!==this&&(at?at._next=this:Yt=this,at=this),this._call=e,this._time=n,An()},stop:function(){this._call&&(this._call=null,this._time=1/0,An())}};function Yi(e,t,n){var r=new Wt;return r.restart(e,t,n),r}function _u(){tr(),++et;for(var e=Yt,t;e;)(t=qe-e._time)>=0&&e._call.call(void 0,t),e=e._next;--et}function Dr(){qe=(Kt=bt.now())+cn,et=lt=0;try{_u()}finally{et=0,wu(),qe=0}}function yu(){var e=bt.now(),t=e-Kt;t>Xi&&(cn-=t,Kt=e)}function wu(){for(var e,t=Yt,n,r=1/0;t;)t._call?(r>t._time&&(r=t._time),e=t,t=t._next):(n=t._next,t._next=null,t=e?e._next=n:Yt=n);at=e,An(r)}function An(e){if(!et){lt&&(lt=clearTimeout(lt));var t=e-qe;t>24?(e<1/0&&(lt=setTimeout(Dr,e-bt.now()-cn)),ot&&(ot=clearInterval(ot))):(ot||(Kt=bt.now(),ot=setInterval(yu,Xi)),et=1,Gi(Dr))}}function Lr(e,t,n){var r=new Wt;return t=t==null?0:+t,r.restart(i=>{r.stop(),e(i+t)},t,n),r}var xu=Ri("start","end","cancel","interrupt"),bu=[],Ki=0,Fr=1,Vn=2,Lt=3,qr=4,Nn=5,Ft=6;function fn(e,t,n,r,i,o){var l=e.__transition;if(!l)e.__transition={};else if(n in l)return;Eu(e,n,{name:t,index:r,group:i,on:xu,tween:bu,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:Ki})}function nr(e,t){var n=ke(e,t);if(n.state>Ki)throw new Error("too late; already scheduled");return n}function Me(e,t){var n=ke(e,t);if(n.state>Lt)throw new Error("too late; already running");return n}function ke(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function Eu(e,t,n){var r=e.__transition,i;r[t]=n,n.timer=Yi(o,0,n.time);function o(s){n.state=Fr,n.timer.restart(l,n.delay,n.time),n.delay<=s&&l(s-n.delay)}function l(s){var u,f,d,p;if(n.state!==Fr)return c();for(u in r)if(p=r[u],p.name===n.name){if(p.state===Lt)return Lr(l);p.state===qr?(p.state=Ft,p.timer.stop(),p.on.call("interrupt",e,e.__data__,p.index,p.group),delete r[u]):+u<t&&(p.state=Ft,p.timer.stop(),p.on.call("cancel",e,e.__data__,p.index,p.group),delete r[u])}if(Lr(function(){n.state===Lt&&(n.state=qr,n.timer.restart(a,n.delay,n.time),a(s))}),n.state=Vn,n.on.call("start",e,e.__data__,n.index,n.group),n.state===Vn){for(n.state=Lt,i=new Array(d=n.tween.length),u=0,f=-1;u<d;++u)(p=n.tween[u].value.call(e,e.__data__,n.index,n.group))&&(i[++f]=p);i.length=f+1}}function a(s){for(var u=s<n.duration?n.ease.call(null,s/n.duration):(n.timer.restart(c),n.state=Nn,1),f=-1,d=i.length;++f<d;)i[f].call(e,u);n.state===Nn&&(n.on.call("end",e,e.__data__,n.index,n.group),c())}function c(){n.state=Ft,n.timer.stop(),delete r[t];for(var s in r)return;delete e.__transition}}function Su(e,t){var n=e.__transition,r,i,o=!0,l;if(n){t=t==null?null:t+"";for(l in n){if((r=n[l]).name!==t){o=!1;continue}i=r.state>Vn&&r.state<Nn,r.state=Ft,r.timer.stop(),r.on.call(i?"interrupt":"cancel",e,e.__data__,r.index,r.group),delete n[l]}o&&delete e.__transition}}function ku(e){return this.each(function(){Su(this,e)})}function Ru(e,t){var n,r;return function(){var i=Me(this,e),o=i.tween;if(o!==n){r=n=o;for(var l=0,a=r.length;l<a;++l)if(r[l].name===t){r=r.slice(),r.splice(l,1);break}}i.tween=r}}function Pu(e,t,n){var r,i;if(typeof n!="function")throw new Error;return function(){var o=Me(this,e),l=o.tween;if(l!==r){i=(r=l).slice();for(var a={name:t,value:n},c=0,s=i.length;c<s;++c)if(i[c].name===t){i[c]=a;break}c===s&&i.push(a)}o.tween=i}}function Cu(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r=ke(this.node(),n).tween,i=0,o=r.length,l;i<o;++i)if((l=r[i]).name===e)return l.value;return null}return this.each((t==null?Ru:Pu)(n,e,t))}function rr(e,t,n){var r=e._id;return e.each(function(){var i=Me(this,r);(i.value||(i.value={}))[t]=n.apply(this,arguments)}),function(i){return ke(i,r).value[t]}}function Wi(e,t){var n;return(typeof t=="number"?Ue:t instanceof xt?Or:(n=xt(t))?(t=n,Or):du)(e,t)}function $u(e){return function(){this.removeAttribute(e)}}function Mu(e){return function(){this.removeAttributeNS(e.space,e.local)}}function Au(e,t,n){var r,i=n+"",o;return function(){var l=this.getAttribute(e);return l===i?null:l===r?o:o=t(r=l,n)}}function Vu(e,t,n){var r,i=n+"",o;return function(){var l=this.getAttributeNS(e.space,e.local);return l===i?null:l===r?o:o=t(r=l,n)}}function Nu(e,t,n){var r,i,o;return function(){var l,a=n(this),c;return a==null?void this.removeAttribute(e):(l=this.getAttribute(e),c=a+"",l===c?null:l===r&&c===i?o:(i=c,o=t(r=l,a)))}}function Iu(e,t,n){var r,i,o;return function(){var l,a=n(this),c;return a==null?void this.removeAttributeNS(e.space,e.local):(l=this.getAttributeNS(e.space,e.local),c=a+"",l===c?null:l===r&&c===i?o:(i=c,o=t(r=l,a)))}}function Tu(e,t){var n=un(e),r=n==="transform"?gu:Wi;return this.attrTween(e,typeof t=="function"?(n.local?Iu:Nu)(n,r,rr(this,"attr."+e,t)):t==null?(n.local?Mu:$u)(n):(n.local?Vu:Au)(n,r,t))}function Ou(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function Uu(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function Du(e,t){var n,r;function i(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&Uu(e,o)),n}return i._value=t,i}function Lu(e,t){var n,r;function i(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&Ou(e,o)),n}return i._value=t,i}function Fu(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!="function")throw new Error;var r=un(e);return this.tween(n,(r.local?Du:Lu)(r,t))}function qu(e,t){return function(){nr(this,e).delay=+t.apply(this,arguments)}}function zu(e,t){return t=+t,function(){nr(this,e).delay=t}}function Hu(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?qu:zu)(t,e)):ke(this.node(),t).delay}function Bu(e,t){return function(){Me(this,e).duration=+t.apply(this,arguments)}}function Xu(e,t){return t=+t,function(){Me(this,e).duration=t}}function Gu(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?Bu:Xu)(t,e)):ke(this.node(),t).duration}function Yu(e,t){if(typeof t!="function")throw new Error;return function(){Me(this,e).ease=t}}function Ku(e){var t=this._id;return arguments.length?this.each(Yu(t,e)):ke(this.node(),t).ease}function Wu(e,t){return function(){var n=t.apply(this,arguments);if(typeof n!="function")throw new Error;Me(this,e).ease=n}}function Qu(e){if(typeof e!="function")throw new Error;return this.each(Wu(this._id,e))}function Zu(e){typeof e!="function"&&(e=$i(e));for(var t=this._groups,n=t.length,r=new Array(n),i=0;i<n;++i)for(var o=t[i],l=o.length,a=r[i]=[],c,s=0;s<l;++s)(c=o[s])&&e.call(c,c.__data__,s,o)&&a.push(c);return new Ie(r,this._parents,this._name,this._id)}function Ju(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,r=t.length,i=n.length,o=Math.min(r,i),l=new Array(r),a=0;a<o;++a)for(var c=t[a],s=n[a],u=c.length,f=l[a]=new Array(u),d,p=0;p<u;++p)(d=c[p]||s[p])&&(f[p]=d);for(;a<r;++a)l[a]=t[a];return new Ie(l,this._parents,this._name,this._id)}function ju(e){return(e+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||t==="start"})}function ec(e,t,n){var r,i,o=ju(t)?nr:Me;return function(){var l=o(this,e),a=l.on;a!==r&&(i=(r=a).copy()).on(t,n),l.on=i}}function tc(e,t){var n=this._id;return arguments.length<2?ke(this.node(),n).on.on(e):this.each(ec(n,e,t))}function nc(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}function rc(){return this.on("end.remove",nc(this._id))}function ic(e){var t=this._name,n=this._id;typeof e!="function"&&(e=Jn(e));for(var r=this._groups,i=r.length,o=new Array(i),l=0;l<i;++l)for(var a=r[l],c=a.length,s=o[l]=new Array(c),u,f,d=0;d<c;++d)(u=a[d])&&(f=e.call(u,u.__data__,d,a))&&("__data__"in u&&(f.__data__=u.__data__),s[d]=f,fn(s[d],t,n,d,s,ke(u,n)));return new Ie(o,this._parents,t,n)}function oc(e){var t=this._name,n=this._id;typeof e!="function"&&(e=Ci(e));for(var r=this._groups,i=r.length,o=[],l=[],a=0;a<i;++a)for(var c=r[a],s=c.length,u,f=0;f<s;++f)if(u=c[f]){for(var d=e.call(u,u.__data__,f,c),p,_=ke(u,n),E=0,R=d.length;E<R;++E)(p=d[E])&&fn(p,t,n,E,d,_);o.push(d),l.push(u)}return new Ie(o,l,t,n)}var lc=Pt.prototype.constructor;function ac(){return new lc(this._groups,this._parents)}function sc(e,t){var n,r,i;return function(){var o=je(this,e),l=(this.style.removeProperty(e),je(this,e));return o===l?null:o===n&&l===r?i:i=t(n=o,r=l)}}function Qi(e){return function(){this.style.removeProperty(e)}}function uc(e,t,n){var r,i=n+"",o;return function(){var l=je(this,e);return l===i?null:l===r?o:o=t(r=l,n)}}function cc(e,t,n){var r,i,o;return function(){var l=je(this,e),a=n(this),c=a+"";return a==null&&(c=a=(this.style.removeProperty(e),je(this,e))),l===c?null:l===r&&c===i?o:(i=c,o=t(r=l,a))}}function fc(e,t){var n,r,i,o="style."+t,l="end."+o,a;return function(){var c=Me(this,e),s=c.on,u=c.value[o]==null?a||(a=Qi(t)):void 0;(s!==n||i!==u)&&(r=(n=s).copy()).on(l,i=u),c.on=r}}function dc(e,t,n){var r=(e+="")=="transform"?mu:Wi;return t==null?this.styleTween(e,sc(e,r)).on("end.style."+e,Qi(e)):typeof t=="function"?this.styleTween(e,cc(e,r,rr(this,"style."+e,t))).each(fc(this._id,e)):this.styleTween(e,uc(e,r,t),n).on("end.style."+e,null)}function pc(e,t,n){return function(r){this.style.setProperty(e,t.call(this,r),n)}}function hc(e,t,n){var r,i;function o(){var l=t.apply(this,arguments);return l!==i&&(r=(i=l)&&pc(e,l,n)),r}return o._value=t,o}function mc(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;return this.tween(r,hc(e,t,n??""))}function gc(e){return function(){this.textContent=e}}function vc(e){return function(){var t=e(this);this.textContent=t??""}}function _c(e){return this.tween("text",typeof e=="function"?vc(rr(this,"text",e)):gc(e==null?"":e+""))}function yc(e){return function(t){this.textContent=e.call(this,t)}}function wc(e){var t,n;function r(){var i=e.apply(this,arguments);return i!==n&&(t=(n=i)&&yc(i)),t}return r._value=e,r}function xc(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,wc(e))}function bc(){for(var e=this._name,t=this._id,n=Zi(),r=this._groups,i=r.length,o=0;o<i;++o)for(var l=r[o],a=l.length,c,s=0;s<a;++s)if(c=l[s]){var u=ke(c,t);fn(c,e,n,s,l,{time:u.time+u.delay+u.duration,delay:0,duration:u.duration,ease:u.ease})}return new Ie(r,this._parents,e,n)}function Ec(){var e,t,n=this,r=n._id,i=n.size();return new Promise(function(o,l){var a={value:l},c={value:function(){--i===0&&o()}};n.each(function(){var s=Me(this,r),u=s.on;u!==e&&(t=(e=u).copy(),t._.cancel.push(a),t._.interrupt.push(a),t._.end.push(c)),s.on=t}),i===0&&o()})}var Sc=0;function Ie(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}function Zi(){return++Sc}var Ve=Pt.prototype;Ie.prototype={constructor:Ie,select:ic,selectAll:oc,selectChild:Ve.selectChild,selectChildren:Ve.selectChildren,filter:Zu,merge:Ju,selection:ac,transition:bc,call:Ve.call,nodes:Ve.nodes,node:Ve.node,size:Ve.size,empty:Ve.empty,each:Ve.each,on:tc,attr:Tu,attrTween:Fu,style:dc,styleTween:mc,text:_c,textTween:xc,remove:rc,tween:Cu,delay:Hu,duration:Gu,ease:Ku,easeVarying:Qu,end:Ec,[Symbol.iterator]:Ve[Symbol.iterator]};function kc(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var Rc={time:null,delay:0,duration:250,ease:kc};function Pc(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}function Cc(e){var t,n;e instanceof Ie?(t=e._id,e=e._name):(t=Zi(),(n=Rc).time=tr(),e=e==null?null:e+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var l=r[o],a=l.length,c,s=0;s<a;++s)(c=l[s])&&fn(c,e,t,s,l,n||Pc(c,t));return new Ie(r,this._parents,e,t)}Pt.prototype.interrupt=ku;Pt.prototype.transition=Cc;function $c(e){if(!e.ok)throw new Error(e.status+" "+e.statusText);if(!(e.status===204||e.status===205))return e.json()}function Mc(e,t){return fetch(e,t).then($c)}var J=1e-6,Y=Math.PI,ge=Y/2,zr=Y/4,ye=Y*2,ve=180/Y,ie=Y/180,ee=Math.abs,Ji=Math.atan,Et=Math.atan2,te=Math.cos,Ac=Math.exp,Vc=Math.log,ne=Math.sin,Nc=Math.sign||function(e){return e>0?1:e<0?-1:0},He=Math.sqrt,Ic=Math.tan;function Tc(e){return e>1?0:e<-1?Y:Math.acos(e)}function St(e){return e>1?ge:e<-1?-ge:Math.asin(e)}function be(){}function Qt(e,t){e&&Br.hasOwnProperty(e.type)&&Br[e.type](e,t)}var Hr={Feature:function(e,t){Qt(e.geometry,t)},FeatureCollection:function(e,t){for(var n=e.features,r=-1,i=n.length;++r<i;)Qt(n[r].geometry,t)}},Br={Sphere:function(e,t){t.sphere()},Point:function(e,t){e=e.coordinates,t.point(e[0],e[1],e[2])},MultiPoint:function(e,t){for(var n=e.coordinates,r=-1,i=n.length;++r<i;)e=n[r],t.point(e[0],e[1],e[2])},LineString:function(e,t){In(e.coordinates,t,0)},MultiLineString:function(e,t){for(var n=e.coordinates,r=-1,i=n.length;++r<i;)In(n[r],t,0)},Polygon:function(e,t){Xr(e.coordinates,t)},MultiPolygon:function(e,t){for(var n=e.coordinates,r=-1,i=n.length;++r<i;)Xr(n[r],t)},GeometryCollection:function(e,t){for(var n=e.geometries,r=-1,i=n.length;++r<i;)Qt(n[r],t)}};function In(e,t,n){var r=-1,i=e.length-n,o;for(t.lineStart();++r<i;)o=e[r],t.point(o[0],o[1],o[2]);t.lineEnd()}function Xr(e,t){var n=-1,r=e.length;for(t.polygonStart();++n<r;)In(e[n],t,1);t.polygonEnd()}function Ge(e,t){e&&Hr.hasOwnProperty(e.type)?Hr[e.type](e,t):Qt(e,t)}function Tn(e){return[Et(e[1],e[0]),St(e[2])]}function tt(e){var t=e[0],n=e[1],r=te(n);return[r*te(t),r*ne(t),ne(n)]}function Nt(e,t){return e[0]*t[0]+e[1]*t[1]+e[2]*t[2]}function Zt(e,t){return[e[1]*t[2]-e[2]*t[1],e[2]*t[0]-e[0]*t[2],e[0]*t[1]-e[1]*t[0]]}function yn(e,t){e[0]+=t[0],e[1]+=t[1],e[2]+=t[2]}function It(e,t){return[e[0]*t,e[1]*t,e[2]*t]}function On(e){var t=He(e[0]*e[0]+e[1]*e[1]+e[2]*e[2]);e[0]/=t,e[1]/=t,e[2]/=t}function Un(e,t){function n(r,i){return r=e(r,i),t(r[0],r[1])}return e.invert&&t.invert&&(n.invert=function(r,i){return r=t.invert(r,i),r&&e.invert(r[0],r[1])}),n}function Dn(e,t){return ee(e)>Y&&(e-=Math.round(e/ye)*ye),[e,t]}Dn.invert=Dn;function ji(e,t,n){return(e%=ye)?t||n?Un(Yr(e),Kr(t,n)):Yr(e):t||n?Kr(t,n):Dn}function Gr(e){return function(t,n){return t+=e,ee(t)>Y&&(t-=Math.round(t/ye)*ye),[t,n]}}function Yr(e){var t=Gr(e);return t.invert=Gr(-e),t}function Kr(e,t){var n=te(e),r=ne(e),i=te(t),o=ne(t);function l(a,c){var s=te(c),u=te(a)*s,f=ne(a)*s,d=ne(c),p=d*n+u*r;return[Et(f*i-p*o,u*n-d*r),St(p*i+f*o)]}return l.invert=function(a,c){var s=te(c),u=te(a)*s,f=ne(a)*s,d=ne(c),p=d*i-f*o;return[Et(f*i+d*o,u*n+p*r),St(p*n-u*r)]},l}function Oc(e){e=ji(e[0]*ie,e[1]*ie,e.length>2?e[2]*ie:0);function t(n){return n=e(n[0]*ie,n[1]*ie),n[0]*=ve,n[1]*=ve,n}return t.invert=function(n){return n=e.invert(n[0]*ie,n[1]*ie),n[0]*=ve,n[1]*=ve,n},t}function Uc(e,t,n,r,i,o){if(n){var l=te(t),a=ne(t),c=r*n;i==null?(i=t+r*ye,o=t-c/2):(i=Wr(l,i),o=Wr(l,o),(r>0?i<o:i>o)&&(i+=r*ye));for(var s,u=i;r>0?u>o:u<o;u-=c)s=Tn([l,-a*te(u),-a*ne(u)]),e.point(s[0],s[1])}}function Wr(e,t){t=tt(t),t[0]-=e,On(t);var n=Tc(-t[1]);return((-t[2]<0?-n:n)+ye-J)%ye}function eo(){var e=[],t;return{point:function(n,r,i){t.push([n,r,i])},lineStart:function(){e.push(t=[])},lineEnd:be,rejoin:function(){e.length>1&&e.push(e.pop().concat(e.shift()))},result:function(){var n=e;return e=[],t=null,n}}}function qt(e,t){return ee(e[0]-t[0])<J&&ee(e[1]-t[1])<J}function Tt(e,t,n,r){this.x=e,this.z=t,this.o=n,this.e=r,this.v=!1,this.n=this.p=null}function to(e,t,n,r,i){var o=[],l=[],a,c;if(e.forEach(function(_){if(!((E=_.length-1)<=0)){var E,R=_[0],A=_[E],S;if(qt(R,A)){if(!R[2]&&!A[2]){for(i.lineStart(),a=0;a<E;++a)i.point((R=_[a])[0],R[1]);i.lineEnd();return}A[0]+=2*J}o.push(S=new Tt(R,_,null,!0)),l.push(S.o=new Tt(R,null,S,!1)),o.push(S=new Tt(A,_,null,!1)),l.push(S.o=new Tt(A,null,S,!0))}}),!!o.length){for(l.sort(t),Qr(o),Qr(l),a=0,c=l.length;a<c;++a)l[a].e=n=!n;for(var s=o[0],u,f;;){for(var d=s,p=!0;d.v;)if((d=d.n)===s)return;u=d.z,i.lineStart();do{if(d.v=d.o.v=!0,d.e){if(p)for(a=0,c=u.length;a<c;++a)i.point((f=u[a])[0],f[1]);else r(d.x,d.n.x,1,i);d=d.n}else{if(p)for(u=d.p.z,a=u.length-1;a>=0;--a)i.point((f=u[a])[0],f[1]);else r(d.x,d.p.x,-1,i);d=d.p}d=d.o,u=d.z,p=!p}while(!d.v);i.lineEnd()}}}function Qr(e){if(t=e.length){for(var t,n=0,r=e[0],i;++n<t;)r.n=i=e[n],i.p=r,r=i;r.n=i=e[0],i.p=r}}function wn(e){return ee(e[0])<=Y?e[0]:Nc(e[0])*((ee(e[0])+Y)%ye-Y)}function Dc(e,t){var n=wn(t),r=t[1],i=ne(r),o=[ne(n),-te(n),0],l=0,a=0,c=new Fe;i===1?r=ge+J:i===-1&&(r=-ge-J);for(var s=0,u=e.length;s<u;++s)if(d=(f=e[s]).length)for(var f,d,p=f[d-1],_=wn(p),E=p[1]/2+zr,R=ne(E),A=te(E),S=0;S<d;++S,_=$,R=M,A=x,p=P){var P=f[S],$=wn(P),O=P[1]/2+zr,M=ne(O),x=te(O),g=$-_,h=g>=0?1:-1,v=h*g,y=v>Y,T=R*M;if(c.add(Et(T*h*ne(v),A*x+T*te(v))),l+=y?g+h*ye:g,y^_>=n^$>=n){var V=Zt(tt(p),tt(P));On(V);var U=Zt(o,V);On(U);var w=(y^g>=0?-1:1)*St(U[2]);(r>w||r===w&&(V[0]||V[1]))&&(a+=y^g>=0?1:-1)}}return(l<-1e-6||l<J&&c<-1e-12)^a&1}function no(e,t,n,r){return function(i){var o=t(i),l=eo(),a=t(l),c=!1,s,u,f,d={point:p,lineStart:E,lineEnd:R,polygonStart:function(){d.point=A,d.lineStart=S,d.lineEnd=P,u=[],s=[]},polygonEnd:function(){d.point=p,d.lineStart=E,d.lineEnd=R,u=ki(u);var $=Dc(s,r);u.length?(c||(i.polygonStart(),c=!0),to(u,Fc,$,n,i)):$&&(c||(i.polygonStart(),c=!0),i.lineStart(),n(null,null,1,i),i.lineEnd()),c&&(i.polygonEnd(),c=!1),u=s=null},sphere:function(){i.polygonStart(),i.lineStart(),n(null,null,1,i),i.lineEnd(),i.polygonEnd()}};function p($,O){e($,O)&&i.point($,O)}function _($,O){o.point($,O)}function E(){d.point=_,o.lineStart()}function R(){d.point=p,o.lineEnd()}function A($,O){f.push([$,O]),a.point($,O)}function S(){a.lineStart(),f=[]}function P(){A(f[0][0],f[0][1]),a.lineEnd();var $=a.clean(),O=l.result(),M,x=O.length,g,h,v;if(f.pop(),s.push(f),f=null,!!x){if($&1){if(h=O[0],(g=h.length-1)>0){for(c||(i.polygonStart(),c=!0),i.lineStart(),M=0;M<g;++M)i.point((v=h[M])[0],v[1]);i.lineEnd()}return}x>1&&$&2&&O.push(O.pop().concat(O.shift())),u.push(O.filter(Lc))}}return d}}function Lc(e){return e.length>1}function Fc(e,t){return((e=e.x)[0]<0?e[1]-ge-J:ge-e[1])-((t=t.x)[0]<0?t[1]-ge-J:ge-t[1])}const Zr=no(function(){return!0},qc,Hc,[-Y,-ge]);function qc(e){var t=NaN,n=NaN,r=NaN,i;return{lineStart:function(){e.lineStart(),i=1},point:function(o,l){var a=o>0?Y:-Y,c=ee(o-t);ee(c-Y)<J?(e.point(t,n=(n+l)/2>0?ge:-ge),e.point(r,n),e.lineEnd(),e.lineStart(),e.point(a,n),e.point(o,n),i=0):r!==a&&c>=Y&&(ee(t-r)<J&&(t-=r*J),ee(o-a)<J&&(o-=a*J),n=zc(t,n,o,l),e.point(r,n),e.lineEnd(),e.lineStart(),e.point(a,n),i=0),e.point(t=o,n=l),r=a},lineEnd:function(){e.lineEnd(),t=n=NaN},clean:function(){return 2-i}}}function zc(e,t,n,r){var i,o,l=ne(e-n);return ee(l)>J?Ji((ne(t)*(o=te(r))*ne(n)-ne(r)*(i=te(t))*ne(e))/(i*o*l)):(t+r)/2}function Hc(e,t,n,r){var i;if(e==null)i=n*ge,r.point(-Y,i),r.point(0,i),r.point(Y,i),r.point(Y,0),r.point(Y,-i),r.point(0,-i),r.point(-Y,-i),r.point(-Y,0),r.point(-Y,i);else if(ee(e[0]-t[0])>J){var o=e[0]<t[0]?Y:-Y;i=n*o/2,r.point(-o,i),r.point(0,i),r.point(o,i)}else r.point(t[0],t[1])}function Bc(e){var t=te(e),n=2*ie,r=t>0,i=ee(t)>J;function o(u,f,d,p){Uc(p,e,n,d,u,f)}function l(u,f){return te(u)*te(f)>t}function a(u){var f,d,p,_,E;return{lineStart:function(){_=p=!1,E=1},point:function(R,A){var S=[R,A],P,$=l(R,A),O=r?$?0:s(R,A):$?s(R+(R<0?Y:-Y),A):0;if(!f&&(_=p=$)&&u.lineStart(),$!==p&&(P=c(f,S),(!P||qt(f,P)||qt(S,P))&&(S[2]=1)),$!==p)E=0,$?(u.lineStart(),P=c(S,f),u.point(P[0],P[1])):(P=c(f,S),u.point(P[0],P[1],2),u.lineEnd()),f=P;else if(i&&f&&r^$){var M;!(O&d)&&(M=c(S,f,!0))&&(E=0,r?(u.lineStart(),u.point(M[0][0],M[0][1]),u.point(M[1][0],M[1][1]),u.lineEnd()):(u.point(M[1][0],M[1][1]),u.lineEnd(),u.lineStart(),u.point(M[0][0],M[0][1],3)))}$&&(!f||!qt(f,S))&&u.point(S[0],S[1]),f=S,p=$,d=O},lineEnd:function(){p&&u.lineEnd(),f=null},clean:function(){return E|(_&&p)<<1}}}function c(u,f,d){var p=tt(u),_=tt(f),E=[1,0,0],R=Zt(p,_),A=Nt(R,R),S=R[0],P=A-S*S;if(!P)return!d&&u;var $=t*A/P,O=-t*S/P,M=Zt(E,R),x=It(E,$),g=It(R,O);yn(x,g);var h=M,v=Nt(x,h),y=Nt(h,h),T=v*v-y*(Nt(x,x)-1);if(!(T<0)){var V=He(T),U=It(h,(-v-V)/y);if(yn(U,x),U=Tn(U),!d)return U;var w=u[0],D=f[0],K=u[1],Q=f[1],j;D<w&&(j=w,w=D,D=j);var C=D-w,B=ee(C-Y)<J,fe=B||C<J;if(!B&&Q<K&&(j=K,K=Q,Q=j),fe?B?K+Q>0^U[1]<(ee(U[0]-w)<J?K:Q):K<=U[1]&&U[1]<=Q:C>Y^(w<=U[0]&&U[0]<=D)){var ue=It(h,(-v+V)/y);return yn(ue,x),[U,Tn(ue)]}}}function s(u,f){var d=r?e:Y-e,p=0;return u<-d?p|=1:u>d&&(p|=2),f<-d?p|=4:f>d&&(p|=8),p}return no(l,a,o,r?[0,-e]:[-Y,e-Y])}function Xc(e,t,n,r,i,o){var l=e[0],a=e[1],c=t[0],s=t[1],u=0,f=1,d=c-l,p=s-a,_;if(_=n-l,!(!d&&_>0)){if(_/=d,d<0){if(_<u)return;_<f&&(f=_)}else if(d>0){if(_>f)return;_>u&&(u=_)}if(_=i-l,!(!d&&_<0)){if(_/=d,d<0){if(_>f)return;_>u&&(u=_)}else if(d>0){if(_<u)return;_<f&&(f=_)}if(_=r-a,!(!p&&_>0)){if(_/=p,p<0){if(_<u)return;_<f&&(f=_)}else if(p>0){if(_>f)return;_>u&&(u=_)}if(_=o-a,!(!p&&_<0)){if(_/=p,p<0){if(_>f)return;_>u&&(u=_)}else if(p>0){if(_<u)return;_<f&&(f=_)}return u>0&&(e[0]=l+u*d,e[1]=a+u*p),f<1&&(t[0]=l+f*d,t[1]=a+f*p),!0}}}}}var Ot=1e9,Ut=-1e9;function Gc(e,t,n,r){function i(s,u){return e<=s&&s<=n&&t<=u&&u<=r}function o(s,u,f,d){var p=0,_=0;if(s==null||(p=l(s,f))!==(_=l(u,f))||c(s,u)<0^f>0)do d.point(p===0||p===3?e:n,p>1?r:t);while((p=(p+f+4)%4)!==_);else d.point(u[0],u[1])}function l(s,u){return ee(s[0]-e)<J?u>0?0:3:ee(s[0]-n)<J?u>0?2:1:ee(s[1]-t)<J?u>0?1:0:u>0?3:2}function a(s,u){return c(s.x,u.x)}function c(s,u){var f=l(s,1),d=l(u,1);return f!==d?f-d:f===0?u[1]-s[1]:f===1?s[0]-u[0]:f===2?s[1]-u[1]:u[0]-s[0]}return function(s){var u=s,f=eo(),d,p,_,E,R,A,S,P,$,O,M,x={point:g,lineStart:T,lineEnd:V,polygonStart:v,polygonEnd:y};function g(w,D){i(w,D)&&u.point(w,D)}function h(){for(var w=0,D=0,K=p.length;D<K;++D)for(var Q=p[D],j=1,C=Q.length,B=Q[0],fe,ue,q=B[0],ce=B[1];j<C;++j)fe=q,ue=ce,B=Q[j],q=B[0],ce=B[1],ue<=r?ce>r&&(q-fe)*(r-ue)>(ce-ue)*(e-fe)&&++w:ce<=r&&(q-fe)*(r-ue)<(ce-ue)*(e-fe)&&--w;return w}function v(){u=f,d=[],p=[],M=!0}function y(){var w=h(),D=M&&w,K=(d=ki(d)).length;(D||K)&&(s.polygonStart(),D&&(s.lineStart(),o(null,null,1,s),s.lineEnd()),K&&to(d,a,w,o,s),s.polygonEnd()),u=s,d=p=_=null}function T(){x.point=U,p&&p.push(_=[]),O=!0,$=!1,S=P=NaN}function V(){d&&(U(E,R),A&&$&&f.rejoin(),d.push(f.result())),x.point=g,$&&u.lineEnd()}function U(w,D){var K=i(w,D);if(p&&_.push([w,D]),O)E=w,R=D,A=K,O=!1,K&&(u.lineStart(),u.point(w,D));else if(K&&$)u.point(w,D);else{var Q=[S=Math.max(Ut,Math.min(Ot,S)),P=Math.max(Ut,Math.min(Ot,P))],j=[w=Math.max(Ut,Math.min(Ot,w)),D=Math.max(Ut,Math.min(Ot,D))];Xc(Q,j,e,t,n,r)?($||(u.lineStart(),u.point(Q[0],Q[1])),u.point(j[0],j[1]),K||u.lineEnd(),M=!1):K&&(u.lineStart(),u.point(w,D),M=!1)}S=w,P=D,$=K}return x}}const Ln=e=>e;var xn=new Fe,Fn=new Fe,ro,io,qn,zn,Ne={point:be,lineStart:be,lineEnd:be,polygonStart:function(){Ne.lineStart=Yc,Ne.lineEnd=Wc},polygonEnd:function(){Ne.lineStart=Ne.lineEnd=Ne.point=be,xn.add(ee(Fn)),Fn=new Fe},result:function(){var e=xn/2;return xn=new Fe,e}};function Yc(){Ne.point=Kc}function Kc(e,t){Ne.point=oo,ro=qn=e,io=zn=t}function oo(e,t){Fn.add(zn*e-qn*t),qn=e,zn=t}function Wc(){oo(ro,io)}var nt=1/0,Jt=nt,kt=-nt,jt=kt,en={point:Qc,lineStart:be,lineEnd:be,polygonStart:be,polygonEnd:be,result:function(){var e=[[nt,Jt],[kt,jt]];return kt=jt=-(Jt=nt=1/0),e}};function Qc(e,t){e<nt&&(nt=e),e>kt&&(kt=e),t<Jt&&(Jt=t),t>jt&&(jt=t)}var Hn=0,Bn=0,st=0,tn=0,nn=0,Ye=0,Xn=0,Gn=0,ut=0,lo,ao,Pe,Ce,xe={point:ze,lineStart:Jr,lineEnd:jr,polygonStart:function(){xe.lineStart=jc,xe.lineEnd=ef},polygonEnd:function(){xe.point=ze,xe.lineStart=Jr,xe.lineEnd=jr},result:function(){var e=ut?[Xn/ut,Gn/ut]:Ye?[tn/Ye,nn/Ye]:st?[Hn/st,Bn/st]:[NaN,NaN];return Hn=Bn=st=tn=nn=Ye=Xn=Gn=ut=0,e}};function ze(e,t){Hn+=e,Bn+=t,++st}function Jr(){xe.point=Zc}function Zc(e,t){xe.point=Jc,ze(Pe=e,Ce=t)}function Jc(e,t){var n=e-Pe,r=t-Ce,i=He(n*n+r*r);tn+=i*(Pe+e)/2,nn+=i*(Ce+t)/2,Ye+=i,ze(Pe=e,Ce=t)}function jr(){xe.point=ze}function jc(){xe.point=tf}function ef(){so(lo,ao)}function tf(e,t){xe.point=so,ze(lo=Pe=e,ao=Ce=t)}function so(e,t){var n=e-Pe,r=t-Ce,i=He(n*n+r*r);tn+=i*(Pe+e)/2,nn+=i*(Ce+t)/2,Ye+=i,i=Ce*e-Pe*t,Xn+=i*(Pe+e),Gn+=i*(Ce+t),ut+=i*3,ze(Pe=e,Ce=t)}function uo(e){this._context=e}uo.prototype={_radius:4.5,pointRadius:function(e){return this._radius=e,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){this._line===0&&this._context.closePath(),this._point=NaN},point:function(e,t){switch(this._point){case 0:{this._context.moveTo(e,t),this._point=1;break}case 1:{this._context.lineTo(e,t);break}default:{this._context.moveTo(e+this._radius,t),this._context.arc(e,t,this._radius,0,ye);break}}},result:be};var Yn=new Fe,bn,co,fo,ct,ft,Rt={point:be,lineStart:function(){Rt.point=nf},lineEnd:function(){bn&&po(co,fo),Rt.point=be},polygonStart:function(){bn=!0},polygonEnd:function(){bn=null},result:function(){var e=+Yn;return Yn=new Fe,e}};function nf(e,t){Rt.point=po,co=ct=e,fo=ft=t}function po(e,t){ct-=e,ft-=t,Yn.add(He(ct*ct+ft*ft)),ct=e,ft=t}let ei,rn,ti,ni;class ri{constructor(t){this._append=t==null?ho:rf(t),this._radius=4.5,this._=""}pointRadius(t){return this._radius=+t,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){this._line===0&&(this._+="Z"),this._point=NaN}point(t,n){switch(this._point){case 0:{this._append`M${t},${n}`,this._point=1;break}case 1:{this._append`L${t},${n}`;break}default:{if(this._append`M${t},${n}`,this._radius!==ti||this._append!==rn){const r=this._radius,i=this._;this._="",this._append`m0,${r}a${r},${r} 0 1,1 0,${-2*r}a${r},${r} 0 1,1 0,${2*r}z`,ti=r,rn=this._append,ni=this._,this._=i}this._+=ni;break}}}result(){const t=this._;return this._="",t.length?t:null}}function ho(e){let t=1;this._+=e[0];for(const n=e.length;t<n;++t)this._+=arguments[t]+e[t]}function rf(e){const t=Math.floor(e);if(!(t>=0))throw new RangeError(`invalid digits: ${e}`);if(t>15)return ho;if(t!==ei){const n=10**t;ei=t,rn=function(i){let o=1;this._+=i[0];for(const l=i.length;o<l;++o)this._+=Math.round(arguments[o]*n)/n+i[o]}}return rn}function ii(e,t){let n=3,r=4.5,i,o;function l(a){return a&&(typeof r=="function"&&o.pointRadius(+r.apply(this,arguments)),Ge(a,i(o))),o.result()}return l.area=function(a){return Ge(a,i(Ne)),Ne.result()},l.measure=function(a){return Ge(a,i(Rt)),Rt.result()},l.bounds=function(a){return Ge(a,i(en)),en.result()},l.centroid=function(a){return Ge(a,i(xe)),xe.result()},l.projection=function(a){return arguments.length?(i=a==null?(e=null,Ln):(e=a).stream,l):e},l.context=function(a){return arguments.length?(o=a==null?(t=null,new ri(n)):new uo(t=a),typeof r!="function"&&o.pointRadius(r),l):t},l.pointRadius=function(a){return arguments.length?(r=typeof a=="function"?a:(o.pointRadius(+a),+a),l):r},l.digits=function(a){if(!arguments.length)return n;if(a==null)n=null;else{const c=Math.floor(a);if(!(c>=0))throw new RangeError(`invalid digits: ${a}`);n=c}return t===null&&(o=new ri(n)),l},l.projection(e).digits(n).context(t)}function ir(e){return function(t){var n=new Kn;for(var r in e)n[r]=e[r];return n.stream=t,n}}function Kn(){}Kn.prototype={constructor:Kn,point:function(e,t){this.stream.point(e,t)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};function or(e,t,n){var r=e.clipExtent&&e.clipExtent();return e.scale(150).translate([0,0]),r!=null&&e.clipExtent(null),Ge(n,e.stream(en)),t(en.result()),r!=null&&e.clipExtent(r),e}function mo(e,t,n){return or(e,function(r){var i=t[1][0]-t[0][0],o=t[1][1]-t[0][1],l=Math.min(i/(r[1][0]-r[0][0]),o/(r[1][1]-r[0][1])),a=+t[0][0]+(i-l*(r[1][0]+r[0][0]))/2,c=+t[0][1]+(o-l*(r[1][1]+r[0][1]))/2;e.scale(150*l).translate([a,c])},n)}function of(e,t,n){return mo(e,[[0,0],t],n)}function lf(e,t,n){return or(e,function(r){var i=+t,o=i/(r[1][0]-r[0][0]),l=(i-o*(r[1][0]+r[0][0]))/2,a=-o*r[0][1];e.scale(150*o).translate([l,a])},n)}function af(e,t,n){return or(e,function(r){var i=+t,o=i/(r[1][1]-r[0][1]),l=-o*r[0][0],a=(i-o*(r[1][1]+r[0][1]))/2;e.scale(150*o).translate([l,a])},n)}var oi=16,sf=te(30*ie);function li(e,t){return+t?cf(e,t):uf(e)}function uf(e){return ir({point:function(t,n){t=e(t,n),this.stream.point(t[0],t[1])}})}function cf(e,t){function n(r,i,o,l,a,c,s,u,f,d,p,_,E,R){var A=s-r,S=u-i,P=A*A+S*S;if(P>4*t&&E--){var $=l+d,O=a+p,M=c+_,x=He($*$+O*O+M*M),g=St(M/=x),h=ee(ee(M)-1)<J||ee(o-f)<J?(o+f)/2:Et(O,$),v=e(h,g),y=v[0],T=v[1],V=y-r,U=T-i,w=S*V-A*U;(w*w/P>t||ee((A*V+S*U)/P-.5)>.3||l*d+a*p+c*_<sf)&&(n(r,i,o,l,a,c,y,T,h,$/=x,O/=x,M,E,R),R.point(y,T),n(y,T,h,$,O,M,s,u,f,d,p,_,E,R))}}return function(r){var i,o,l,a,c,s,u,f,d,p,_,E,R={point:A,lineStart:S,lineEnd:$,polygonStart:function(){r.polygonStart(),R.lineStart=O},polygonEnd:function(){r.polygonEnd(),R.lineStart=S}};function A(g,h){g=e(g,h),r.point(g[0],g[1])}function S(){f=NaN,R.point=P,r.lineStart()}function P(g,h){var v=tt([g,h]),y=e(g,h);n(f,d,u,p,_,E,f=y[0],d=y[1],u=g,p=v[0],_=v[1],E=v[2],oi,r),r.point(f,d)}function $(){R.point=A,r.lineEnd()}function O(){S(),R.point=M,R.lineEnd=x}function M(g,h){P(i=g,h),o=f,l=d,a=p,c=_,s=E,R.point=P}function x(){n(f,d,u,p,_,E,o,l,i,a,c,s,oi,r),R.lineEnd=$,$()}return R}}var ff=ir({point:function(e,t){this.stream.point(e*ie,t*ie)}});function df(e){return ir({point:function(t,n){var r=e(t,n);return this.stream.point(r[0],r[1])}})}function pf(e,t,n,r,i){function o(l,a){return l*=r,a*=i,[t+e*l,n-e*a]}return o.invert=function(l,a){return[(l-t)/e*r,(n-a)/e*i]},o}function ai(e,t,n,r,i,o){if(!o)return pf(e,t,n,r,i);var l=te(o),a=ne(o),c=l*e,s=a*e,u=l/e,f=a/e,d=(a*n-l*t)/e,p=(a*t+l*n)/e;function _(E,R){return E*=r,R*=i,[c*E-s*R+t,n-s*E-c*R]}return _.invert=function(E,R){return[r*(u*E-f*R+d),i*(p-f*E-u*R)]},_}function hf(e){return mf(function(){return e})()}function mf(e){var t,n=150,r=480,i=250,o=0,l=0,a=0,c=0,s=0,u,f=0,d=1,p=1,_=null,E=Zr,R=null,A,S,P,$=Ln,O=.5,M,x,g,h,v;function y(w){return g(w[0]*ie,w[1]*ie)}function T(w){return w=g.invert(w[0],w[1]),w&&[w[0]*ve,w[1]*ve]}y.stream=function(w){return h&&v===w?h:h=ff(df(u)(E(M($(v=w)))))},y.preclip=function(w){return arguments.length?(E=w,_=void 0,U()):E},y.postclip=function(w){return arguments.length?($=w,R=A=S=P=null,U()):$},y.clipAngle=function(w){return arguments.length?(E=+w?Bc(_=w*ie):(_=null,Zr),U()):_*ve},y.clipExtent=function(w){return arguments.length?($=w==null?(R=A=S=P=null,Ln):Gc(R=+w[0][0],A=+w[0][1],S=+w[1][0],P=+w[1][1]),U()):R==null?null:[[R,A],[S,P]]},y.scale=function(w){return arguments.length?(n=+w,V()):n},y.translate=function(w){return arguments.length?(r=+w[0],i=+w[1],V()):[r,i]},y.center=function(w){return arguments.length?(o=w[0]%360*ie,l=w[1]%360*ie,V()):[o*ve,l*ve]},y.rotate=function(w){return arguments.length?(a=w[0]%360*ie,c=w[1]%360*ie,s=w.length>2?w[2]%360*ie:0,V()):[a*ve,c*ve,s*ve]},y.angle=function(w){return arguments.length?(f=w%360*ie,V()):f*ve},y.reflectX=function(w){return arguments.length?(d=w?-1:1,V()):d<0},y.reflectY=function(w){return arguments.length?(p=w?-1:1,V()):p<0},y.precision=function(w){return arguments.length?(M=li(x,O=w*w),U()):He(O)},y.fitExtent=function(w,D){return mo(y,w,D)},y.fitSize=function(w,D){return of(y,w,D)},y.fitWidth=function(w,D){return lf(y,w,D)},y.fitHeight=function(w,D){return af(y,w,D)};function V(){var w=ai(n,0,0,d,p,f).apply(null,t(o,l)),D=ai(n,r-w[0],i-w[1],d,p,f);return u=ji(a,c,s),x=Un(t,D),g=Un(u,x),M=li(x,O),U()}function U(){return h=v=null,y}return function(){return t=e.apply(this,arguments),y.invert=t.invert&&T,V()}}function lr(e,t){return[e,Vc(Ic((ge+t)/2))]}lr.invert=function(e,t){return[e,2*Ji(Ac(t))-ge]};function gf(){return vf(lr).scale(961/ye)}function vf(e){var t=hf(e),n=t.center,r=t.scale,i=t.translate,o=t.clipExtent,l=null,a,c,s;t.scale=function(f){return arguments.length?(r(f),u()):r()},t.translate=function(f){return arguments.length?(i(f),u()):i()},t.center=function(f){return arguments.length?(n(f),u()):n()},t.clipExtent=function(f){return arguments.length?(f==null?l=a=c=s=null:(l=+f[0][0],a=+f[0][1],c=+f[1][0],s=+f[1][1]),u()):l==null?null:[[l,a],[c,s]]};function u(){var f=Y*r(),d=t(Oc(t.rotate()).invert([0,0]));return o(l==null?[[d[0]-f,d[1]-f],[d[0]+f,d[1]+f]]:e===lr?[[Math.max(d[0]-f,l),a],[Math.min(d[0]+f,c),s]]:[[l,Math.max(d[1]-f,a)],[c,Math.min(d[1]+f,s)]])}return u()}function dt(e,t,n){this.k=e,this.x=t,this.y=n}dt.prototype={constructor:dt,scale:function(e){return e===1?this:new dt(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new dt(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};dt.prototype;const _f={class:"clock-container"},yf={class:"holographic-clock",viewBox:"0 0 100 100"},wf={class:"clock-markers"},xf=["x1","y1","x2","y2"],bf={class:"clock-hands"},Ef=["x2","y2"],Sf=["x2","y2"],kf=["x2","y2"],Rf=["id"],Pf=["id"],Cf={__name:"VisualScreen",setup(e){const t=X(null),n=X(null),r=X(0),i=X(!1),o=new Map,l=X({x:0,y:0}),a=X(0),c=X(0),s=X(0),u=X(0);let f;const d=X([{id:"china",url:"https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json",center:[104,35],scaleFactor:.75,duration:5e3},{id:"sichuan",url:"https://geo.datav.aliyun.com/areas_v3/bound/510000_full.json",center:[103,30],scaleFactor:3,duration:5e3},{id:"guangan",url:"https://geo.datav.aliyun.com/areas_v3/bound/511600_full.json",center:[106.6,30.5],scaleFactor:30,duration:5e3},{id:"yuechi",url:"https://geo.datav.aliyun.com/areas_v3/bound/511621.json",center:[106.4,30.5],scaleFactor:50,duration:5e3}]),p={longitude:106.43,latitude:30.55},_=()=>t.value?{width:t.value.clientWidth,height:t.value.clientHeight}:{width:window.innerWidth,height:window.innerHeight},E=()=>{const g=new Date;a.value=g.getHours()%12,c.value=g.getMinutes(),s.value=g.getSeconds(),u.value=g.getMilliseconds()},R=g=>{if(o.has(g.id))return;const h=_(),v=$t(`#${g.id}-map`).attr("viewBox",`0 0 ${h.width} ${h.height}`).attr("preserveAspectRatio","xMidYMid meet");Mc(g.url).then(y=>{const T=gf().center(g.center).scale(Math.min(h.width,h.height)*g.scaleFactor).translate([h.width/2,h.height/2]);o.set(g.id,{projection:T,data:y}),v.selectAll(".boundary").data(y.features).enter().append("path").attr("class","boundary").attr("d",ii().projection(T)),A(g.id)}).catch(y=>console.error("地图加载失败:",y))},A=g=>{const{projection:h}=o.get(g)||{};if(!h)return;const[v,y]=h([p.longitude,p.latitude]);l.value={x:v,y}},S=()=>{const g=_();d.value.forEach(h=>{const{projection:v,data:y}=o.get(h.id)||{};!v||!y||(v.scale(Math.min(g.width,g.height)*h.scaleFactor).translate([g.width/2,g.height/2]),$t(`#${h.id}-map`).attr("viewBox",`0 0 ${g.width} ${g.height}`).selectAll(".boundary").attr("d",ii().projection(v)),h.id===d.value[r.value].id&&A(h.id))})},P=g=>{if(i.value)return;i.value=!0;const h=d.value.length,v=(r.value+g+h)%h,y=d.value[r.value],T=d.value[v];$t(`#${y.id}-container`).transition().duration(1500).style("opacity",0).on("end",()=>{$t(`#${T.id}-container`).style("opacity",0).classed("map-visible",!0).transition().duration(1500).style("opacity",1).on("end",()=>{r.value=v,i.value=!1,A(T.id)})})},$=()=>{const{width:h,height:v}=_();for(let y=0;y<100;y++){const T=document.createElement("div");T.className="particle";const V=Math.random()*h,U=Math.random()*v,w=Math.random()*10,D=Math.random()*2+1;T.style.left=`${V}px`,T.style.top=`${U}px`,T.style.animationDelay=`${w}s`,T.style.width=`${D}px`,T.style.height=`${D}px`,n.value.appendChild(T)}};let O;const M=()=>{O=setInterval(()=>{P(1)},d.value[0].duration+1500)};ln(()=>{E(),f=setInterval(E,50),d.value.forEach(R),$(),M(),document.addEventListener("fullscreenchange",x)}),Co(()=>{clearInterval(f),clearInterval(O),o.clear(),document.removeEventListener("fullscreenchange",x)});const x=()=>{En(()=>{S(),n.value&&(n.value.innerHTML=""),$()})};return Wn([()=>window.innerWidth,()=>window.innerHeight,()=>{var g;return(g=t.value)==null?void 0:g.clientWidth},()=>{var g;return(g=t.value)==null?void 0:g.clientHeight}],()=>{En(S)}),(g,h)=>(ae(),we("div",{class:"holographic-container",ref_key:"container",ref:t},[h[3]||(h[3]=L("div",{class:"slogan-container"},[L("div",null,"对党忠诚  服务人民"),L("div",null,"执法公正  纪律严明")],-1)),h[4]||(h[4]=L("div",{class:"title-container"},[L("div",{class:"holographic-title"},"岳池县公安局情报指挥中心")],-1)),L("div",_f,[(ae(),we("svg",yf,[h[0]||(h[0]=L("circle",{cx:"50",cy:"50",r:"45",fill:"rgba(0,0,0,0)",stroke:"#00e5ff","stroke-width":"0.5"},null,-1)),h[1]||(h[1]=L("circle",{cx:"50",cy:"50",r:"42",fill:"rgba(5,15,44,0.5)"},null,-1)),L("g",wf,[(ae(),we(gt,null,zt(12,v=>L("line",{key:v,x1:50+38*Math.cos((v*30-90)*Math.PI/180),y1:50+38*Math.sin((v*30-90)*Math.PI/180),x2:50+42*Math.cos((v*30-90)*Math.PI/180),y2:50+42*Math.sin((v*30-90)*Math.PI/180),stroke:"#00e5ff","stroke-width":"1.5"},null,8,xf)),64))]),L("g",bf,[L("line",{class:"hour-hand",x1:50,y1:50,x2:50+20*Math.cos((a.value*30+c.value*.5-90)*Math.PI/180),y2:50+20*Math.sin((a.value*30+c.value*.5-90)*Math.PI/180),stroke:"#9e4edd","stroke-width":"3","stroke-linecap":"round"},null,8,Ef),L("line",{class:"minute-hand",x1:50,y1:50,x2:50+30*Math.cos((c.value*6+s.value*.1-90)*Math.PI/180),y2:50+30*Math.sin((c.value*6+s.value*.1-90)*Math.PI/180),stroke:"#ff4d4d","stroke-width":"2","stroke-linecap":"round"},null,8,Sf),L("line",{class:"second-hand",x1:50,y1:50,x2:50+38*Math.cos((s.value*6+u.value*.006-90)*Math.PI/180),y2:50+38*Math.sin((s.value*6+u.value*.006-90)*Math.PI/180),stroke:"#00e5ff","stroke-width":"1","stroke-linecap":"round"},null,8,kf)]),h[2]||(h[2]=L("circle",{cx:"50",cy:"50",r:"2",fill:"#fff"},null,-1))]))]),L("div",{ref_key:"particlesContainer",ref:n,class:"particles-container"},null,512),h[5]||(h[5]=L("div",{class:"hologram-grid"},null,-1)),h[6]||(h[6]=L("div",{class:"scan-line-vertical"},null,-1)),h[7]||(h[7]=L("div",{class:"scan-line-horizontal"},null,-1)),h[8]||(h[8]=L("div",{class:"hologram-frame"},[L("div")],-1)),(ae(!0),we(gt,null,zt(d.value,(v,y)=>(ae(),we("div",{key:v.id,id:`${v.id}-container`,class:$o(["map-container",{"map-visible":r.value===y}])},[(ae(),we("svg",{id:`${v.id}-map`,class:"map-svg"},null,8,Pf)),r.value===y?(ae(),we("div",{key:0,class:"location-marker",style:Qn({left:l.value.x+"px",top:l.value.y+"px"})},null,4)):Ht("",!0)],10,Rf))),128))],512))}},$f=[{path:"/unit-management",name:"UnitManagement",component:Ql},{path:"/user-management",name:"UserManagement",component:da},{path:"/visual-screen",name:"VisualScreen",component:Cf}],Mf=Il({history:ul(),routes:$f}),dn=Mo(Yl);dn.use(Ao);for(const[e,t]of Object.entries(Vo))dn.component(e,t);dn.use(Mf);dn.mount("#app");
