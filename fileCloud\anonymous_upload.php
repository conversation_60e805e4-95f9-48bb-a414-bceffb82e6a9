<?php
/**
 * 文件网盘系统 - 匿名上传页面
 * 创建时间: 2025-07-03
 * 更新时间: 2025-07-29
 */

require_once 'functions.php';

// 处理文件上传
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['file'])) {
    // 验证CSRF令牌
    if (!validateCsrfToken($_POST['csrf_token'] ?? '')) {
        jsonResponse(['success' => false, 'message' => '无效的请求'], 400);
    }

    $file = $_FILES['file'];
    $shareByUser = isset($_POST['share_by_user']) && $_POST['share_by_user'] === '1';
    $shareByCode = isset($_POST['share_by_code']) && $_POST['share_by_code'] === '1';
    $selectedUserIds = $_POST['selected_users'] ?? '';
    $expireDays = (int)($_POST['expire_days'] ?? DEFAULT_EXPIRE_DAYS);

    // 验证分享方式
    if (!$shareByUser && !$shareByCode) {
        jsonResponse(['success' => false, 'message' => '请至少选择一种分享方式'], 400);
    }

    // 如果选择按用户分享，验证是否选择了用户
    if ($shareByUser) {
        if (empty($selectedUserIds)) {
            jsonResponse(['success' => false, 'message' => '请先选择要分享的用户'], 400);
        }

        // 解析用户ID列表
        $userIdArray = array_filter(array_map('intval', explode(',', $selectedUserIds)));
        if (empty($userIdArray)) {
            jsonResponse(['success' => false, 'message' => '用户选择无效'], 400);
        }
    } else {
        $userIdArray = [];
    }

    // 验证文件
    if ($file['error'] !== UPLOAD_ERR_OK) {
        jsonResponse(['success' => false, 'message' => '文件上传失败：' . $file['error']], 400);
    }

    if ($file['size'] > MAX_FILE_SIZE) {
        jsonResponse(['success' => false, 'message' => '文件大小超过限制（' . formatFileSize(MAX_FILE_SIZE) . '）'], 400);
    }

    if (!isValidFileType($file['name'])) {
        jsonResponse(['success' => false, 'message' => '不支持的文件类型'], 400);
    }

    // 生成文件信息
    $originalName = $file['name'];
    $storedName = generateSecureFilename($originalName);
    $uploadPath = getUploadPath();
    $fullPath = $uploadPath . $storedName;
    $shareCode = generateShareCode();
    $fileType = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
    $uploadIp = getClientRealIP(); // 获取上传者IP地址

    // 计算过期时间
    $expirationTime = null;
    if ($expireDays > 0) {
        $expirationTime = date('Y-m-d H:i:s', time() + ($expireDays * 24 * 60 * 60));
    }

    // 移动文件到目标目录
    if (!move_uploaded_file($file['tmp_name'], $fullPath)) {
        jsonResponse(['success' => false, 'message' => '文件保存失败'], 500);
    }

    try {
        // 开始事务
        $pdo->beginTransaction();

        // 保存文件信息到数据库（使用特殊的匿名用户ID：0）
        $isPublic = $shareByCode ? 1 : 0; // 如果启用分享码则设为公开
        $stmt = $pdo->prepare("
            INSERT INTO filecloud_info
            (user_id, original_name, stored_name, file_size, file_type, share_code, expiration_time, is_public, file_path, uploadIp)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            0, // 匿名用户ID设为0
            $originalName,
            $storedName,
            $file['size'],
            $fileType,
            $shareCode,
            $expirationTime,
            $isPublic,
            $fullPath,
            $uploadIp
        ]);

        $fileId = $pdo->lastInsertId();

        // 如果选择按用户分享，自动分享给选定的用户
        $sharedCount = 0;
        if ($shareByUser && !empty($userIdArray)) {
            $shareStmt = $pdo->prepare("
                INSERT INTO filecloud_share (file_id, from_user_id, to_user_id, share_time)
                VALUES (?, ?, ?, NOW())
            ");

            foreach ($userIdArray as $userId) {
                if ($shareStmt->execute([$fileId, 0, $userId])) { // from_user_id也设为0表示匿名分享
                    $sharedCount++;
                }
            }
        }

        // 提交事务
        $pdo->commit();

        // 获取分享对象信息
        $sharedUsers = [];
        if ($shareByUser && !empty($userIdArray)) {
            $userStmt = $pdo->prepare("
                SELECT u.name, u.organization_unitName
                FROM 3_user u
                WHERE u.id IN (" . implode(',', array_fill(0, count($userIdArray), '?')) . ")
            ");
            $userStmt->execute($userIdArray);
            $sharedUsers = $userStmt->fetchAll(PDO::FETCH_ASSOC);
        }

        // 构建响应数据
        $responseData = [
            'success' => true,
            'message' => '文件上传成功',
            'file_info' => [
                'file_name' => $originalName,
                'file_size' => $file['size'],
                'file_type' => $fileType,
                'share_code' => $shareCode,
                'expire_days' => $expireDays,
                'upload_time' => date('Y-m-d H:i:s'),
                'uploader' => '匿名用户',
                'share_by_code' => $shareByCode,
                'share_by_user' => $shareByUser,
                'shared_users' => $sharedUsers,
                'shared_count' => $sharedCount,
                'is_public' => $isPublic
            ]
        ];

        jsonResponse($responseData);

    } catch (PDOException $e) {
        // 回滚事务
        $pdo->rollBack();
        // 删除已上传的文件
        if (file_exists($fullPath)) {
            unlink($fullPath);
        }
        error_log('匿名上传数据库错误: ' . $e->getMessage());
        jsonResponse(['success' => false, 'message' => '数据库保存失败'], 500);
    }
}

$csrfToken = generateCsrfToken();
$maxFileSize = formatFileSize(MAX_FILE_SIZE);
$allowedTypes = implode(', ', ALLOWED_EXTENSIONS);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= h(SITE_TITLE) ?> - 匿名上传</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 3px dashed #dee2e6;
            border-radius: 12px;
            padding: 60px 20px;
            text-align: center;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .upload-area.dragover {
            border-color: #0d6efd;
            background: #e7f3ff;
            transform: scale(1.02);
        }
        
        .upload-area:hover {
            border-color: #0d6efd;
            background: #f0f7ff;
        }
        
        .upload-icon {
            font-size: 4rem;
            color: #6c757d;
            margin-bottom: 1rem;
        }
        
        .file-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .progress-container {
            display: none;
            margin-top: 20px;
        }
        
        .success-message {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            margin-top: 20px;
            display: none;
        }

        .user-selection-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .selected-users-display {
            background: white;
            border-radius: 6px;
            padding: 10px;
            margin-top: 10px;
            min-height: 50px;
            border: 1px solid #dee2e6;
        }

        .user-tag {
            display: inline-block;
            background: #0d6efd;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            margin: 2px;
            font-size: 0.875rem;
        }

        .user-tag .remove-user {
            margin-left: 5px;
            cursor: pointer;
            opacity: 0.8;
        }

        .user-tag .remove-user:hover {
            opacity: 1;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        .step {
            display: flex;
            align-items: center;
            margin: 0 10px;
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #dee2e6;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 8px;
        }

        .step.active .step-number {
            background: #0d6efd;
            color: white;
        }

        .step.completed .step-number {
            background: #28a745;
            color: white;
        }

        .step-arrow {
            margin: 0 10px;
            color: #dee2e6;
        }

        .anonymous-notice {
            background: linear-gradient(135deg, #17a2b8, #20c997);
            color: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="download.php">
                <i class="bi bi-cloud-upload me-2"></i><?= h(SITE_TITLE) ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="download.php">
                            <i class="bi bi-download me-1"></i>分享码下载
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="anonymous_upload.php">
                            <i class="bi bi-cloud-upload me-1"></i>匿名上传
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="login.php">
                            <i class="bi bi-box-arrow-in-right me-1"></i>登录
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- 匿名上传说明 -->
                <div class="anonymous-notice">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-info-circle-fill me-3" style="font-size: 1.5rem;"></i>
                        <div>
                            <h6 class="mb-1">匿名上传功能</h6>
                            <small>无需登录即可上传文件并设置分享方式。支持按用户名分享或按分享码分享。</small>
                        </div>
                    </div>
                </div>

                <div class="card shadow-lg border-0">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-cloud-upload me-2"></i>匿名文件上传
                        </h4>
                        <small class="text-light">最大文件大小：<?= h($maxFileSize) ?> | 支持格式：<?= h($allowedTypes) ?></small>
                    </div>
                    <div class="card-body p-4">
                        <!-- 上传表单 -->
                        <form id="uploadForm" enctype="multipart/form-data">
                            <input type="hidden" name="csrf_token" value="<?= h($csrfToken) ?>">
                            <input type="hidden" name="selected_users" id="selectedUsersInput">

                            <!-- 文件上传区域 -->
                            <div class="mb-4">
                                <h5 class="mb-3">
                                    <i class="bi bi-cloud-upload me-2"></i>选择文件
                                </h5>

                                <!-- 拖拽上传区域 -->
                                <div class="upload-area" id="uploadArea">
                                    <div class="upload-icon">
                                        <i class="bi bi-cloud-upload"></i>
                                    </div>
                                    <h5 class="mb-3">拖拽文件到此处或点击选择文件</h5>
                                    <p class="text-muted mb-3">支持单文件上传，最大 <?= h($maxFileSize) ?></p>
                                    <input type="file" id="fileInput" name="file" class="d-none" accept=".<?= implode(',.', ALLOWED_EXTENSIONS) ?>">
                                    <button type="button" class="btn btn-primary btn-lg" onclick="document.getElementById('fileInput').click()">
                                        <i class="bi bi-plus-circle me-2"></i>选择文件
                                    </button>
                                </div>

                                <!-- 文件信息显示 -->
                                <div id="fileInfoSection" style="display: none;">
                                    <div class="file-info">
                                        <h6 class="mb-3">
                                            <i class="bi bi-file-earmark me-2"></i>已选择文件
                                        </h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <strong>文件名：</strong>
                                                <span id="fileName" class="text-break"></span>
                                            </div>
                                            <div class="col-md-3">
                                                <strong>大小：</strong>
                                                <span id="fileSize"></span>
                                            </div>
                                            <div class="col-md-3">
                                                <strong>类型：</strong>
                                                <span id="fileType"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 分享方式选择 -->
                            <div class="mb-4">
                                <h5 class="mb-3">
                                    <i class="bi bi-share me-2"></i>分享方式
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="shareByUser" name="share_by_user" value="1" checked>
                                            <label class="form-check-label" for="shareByUser">
                                                <i class="bi bi-people me-1"></i>按用户名分享
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="shareByCode" name="share_by_code" value="1">
                                            <label class="form-check-label" for="shareByCode">
                                                <i class="bi bi-key me-1"></i>按分享码分享
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 用户选择区域 -->
                            <div class="user-selection-section" id="userSelectionSection">
                                <h5 class="mb-3">
                                    <i class="bi bi-people me-2"></i>选择分享用户
                                </h5>
                                <div class="mb-3">
                                    <label for="shareUserSearch" class="form-label">搜索用户</label>
                                    <input type="text" class="form-control" id="shareUserSearch"
                                           placeholder="输入用户姓名或组织单位名称搜索..." autocomplete="off">
                                    <small class="form-text text-muted">搜索并选择要分享的用户</small>
                                </div>

                                <div id="searchResults" class="list-group mb-3" style="display: none;">
                                    <!-- 搜索结果将通过JavaScript动态添加 -->
                                </div>

                                <div class="d-flex align-items-center mb-3">
                                    <div class="form-check me-2">
                                        <input class="form-check-input" type="checkbox" id="selectAllResults">
                                        <label class="form-check-label" for="selectAllResults">全选/取消全选</label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">已选择的用户</label>
                                    <div class="selected-users-display" id="selectedUsersDisplay">
                                        <span class="text-muted">尚未选择用户</span>
                                    </div>
                                    <button type="button" class="btn btn-outline-danger btn-sm mt-2" id="clearAllUsers" style="display: none;">
                                        <i class="bi bi-trash me-1"></i>清除全部
                                    </button>
                                </div>
                            </div>

                            <!-- 分享时长设置 -->
                            <div class="mb-4">
                                <h5 class="mb-3">
                                    <i class="bi bi-calendar me-2"></i>分享时长
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <select class="form-select" id="expireDays" name="expire_days">
                                            <option value="1">1天</option>
                                            <option value="7">7天</option>
                                            <option value="30" selected>30天</option>
                                            <option value="90">90天</option>
                                            <option value="180">180天</option>
                                            <option value="365">1年</option>
                                            <option value="0">永不过期</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-success btn-lg flex-grow-1" id="uploadBtn">
                                    <i class="bi bi-upload me-2"></i>开始上传并分享
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-lg" id="resetBtn">
                                    <i class="bi bi-arrow-clockwise me-2"></i>重置
                                </button>
                            </div>
                        </form>

                        <!-- 上传进度 -->
                        <div class="progress-container">
                            <h6 class="mb-2">
                                <i class="bi bi-upload me-2"></i>上传进度
                            </h6>
                            <div class="progress mb-3" style="height: 12px;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar" style="width: 0%">
                                </div>
                            </div>
                            <div class="text-center">
                                <small class="text-muted" id="uploadStatus">准备上传...</small>
                            </div>
                        </div>

                        <!-- 成功弹窗 -->
                        <div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header bg-success text-white">
                                        <h5 class="modal-title" id="successModalLabel">
                                            <i class="bi bi-check-circle-fill me-2"></i>文件上传成功
                                        </h5>
                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <strong>文件名：</strong>
                                                    <span id="modalFileName" class="text-break"></span>
                                                </div>
                                                <div class="mb-3">
                                                    <strong>文件大小：</strong>
                                                    <span id="modalFileSize"></span>
                                                </div>
                                                <div class="mb-3">
                                                    <strong>分享时长：</strong>
                                                    <span id="modalExpireDays"></span>
                                                </div>
                                                <div class="mb-3">
                                                    <strong>创建人：</strong>
                                                    <span id="modalUploader">匿名用户</span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <strong>创建时间：</strong>
                                                    <span id="modalUploadTime"></span>
                                                </div>
                                                <div class="mb-3" id="modalShareCodeSection">
                                                    <strong>分享码：</strong>
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" id="modalShareCode" readonly>
                                                        <button class="btn btn-outline-secondary" type="button" onclick="copyShareCodeToClipboard()">
                                                            <i class="bi bi-clipboard"></i> 复制
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="mb-3" id="modalSharedUsersSection">
                                                    <strong>分享对象：</strong>
                                                    <div id="modalSharedUsers" class="text-muted"></div>
                                                </div>
                                                <div class="mb-3">
                                                    <strong>公开访问：</strong>
                                                    <span id="modalIsPublic" class="badge"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <a href="download.php" class="btn btn-primary">
                                            <i class="bi bi-download me-1"></i>分享码下载
                                        </a>
                                        <button type="button" class="btn btn-success" onclick="uploadAnother()">
                                            <i class="bi bi-plus-circle me-1"></i>继续上传
                                        </button>
                                        <button type="button" class="btn btn-secondary" onclick="window.close()">关闭</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let selectedUsers = new Map(); // 存储选中的用户 {id: {name, organization_unitName}}
        let userSearchResults = [];
        let selectedFile = null;
        let currentShareCode = '';

        // DOM元素
        const userSelectionSection = document.getElementById('userSelectionSection');
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfoSection = document.getElementById('fileInfoSection');
        const uploadForm = document.getElementById('uploadForm');
        const progressContainer = document.querySelector('.progress-container');
        const shareByUserCheckbox = document.getElementById('shareByUser');
        const shareByCodeCheckbox = document.getElementById('shareByCode');
        const clearAllUsersBtn = document.getElementById('clearAllUsers');
        const resetBtn = document.getElementById('resetBtn');
        const uploadBtn = document.getElementById('uploadBtn');

        // 分享方式切换
        shareByUserCheckbox.addEventListener('change', function() {
            userSelectionSection.style.display = this.checked ? 'block' : 'none';
            if (!this.checked) {
                // 清空用户选择
                selectedUsers.clear();
                updateSelectedUsersDisplay();
                document.getElementById('shareUserSearch').value = '';
                document.getElementById('searchResults').style.display = 'none';
                // 自动勾选按分享码分享
                shareByCodeCheckbox.checked = true;
            }
            updateUploadButtonState();
        });

        shareByCodeCheckbox.addEventListener('change', function() {
            // 如果取消勾选分享码，且用户名分享也未勾选，则自动勾选用户名分享
            if (!this.checked && !shareByUserCheckbox.checked) {
                shareByUserCheckbox.checked = true;
                userSelectionSection.style.display = 'block';
            }
            updateUploadButtonState();
        });

        // 清除全部用户
        clearAllUsersBtn.addEventListener('click', function() {
            selectedUsers.clear();
            updateSelectedUsersDisplay();

            // 取消所有复选框
            const checkboxes = document.querySelectorAll('#searchResults input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });

            document.getElementById('selectAllResults').checked = false;
        });

        // 重置按钮
        resetBtn.addEventListener('click', function() {
            resetForm();
        });

        // 用户搜索功能
        document.getElementById('shareUserSearch').addEventListener('input', function(e) {
            const keyword = e.target.value.trim();
            if (keyword.length < 1) {
                document.getElementById('searchResults').style.display = 'none';
                return;
            }

            const formData = new FormData();
            formData.append('controlCode', 'query');
            formData.append('search_keyword', keyword);

            fetch('api/user_manage.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const resultsContainer = document.getElementById('searchResults');
                resultsContainer.innerHTML = '';

                if (data.status === 1 && data.data.length > 0) {
                    userSearchResults = data.data;

                    data.data.forEach(user => {
                        const resultItem = document.createElement('div');
                        resultItem.className = 'list-group-item';
                        resultItem.dataset.userId = user.id;

                        const isSelected = selectedUsers.has(user.id);
                        resultItem.innerHTML = `
                            <div class="form-check d-flex align-items-center">
                                <input type="checkbox" class="form-check-input me-2"
                                       id="user-${user.id}"
                                       ${isSelected ? 'checked' : ''}
                                       onchange="toggleUserSelection(${user.id}, this.checked)">
                                <label class="form-check-label flex-grow-1" for="user-${user.id}">
                                    ${user.name}（${user.organization_unitName}）
                                </label>
                            </div>
                        `;
                        resultsContainer.appendChild(resultItem);
                    });

                    resultsContainer.style.display = 'block';
                } else {
                    const noResult = document.createElement('div');
                    noResult.className = 'list-group-item text-muted';
                    noResult.textContent = '未找到匹配的用户';
                    resultsContainer.appendChild(noResult);
                    resultsContainer.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('搜索用户失败:', error);
                showAlert('搜索用户失败，请稍后重试', 'danger');
            });
        });

        // 切换用户选择
        function toggleUserSelection(userId, isSelected) {
            const user = userSearchResults.find(u => u.id == userId);
            if (!user) return;

            if (isSelected) {
                selectedUsers.set(userId, user);
            } else {
                selectedUsers.delete(userId);
            }

            updateSelectedUsersDisplay();
            updateUploadButtonState();
        }

        // 更新已选用户显示
        function updateSelectedUsersDisplay() {
            const display = document.getElementById('selectedUsersDisplay');

            if (selectedUsers.size === 0) {
                display.innerHTML = '<span class="text-muted">尚未选择用户</span>';
                clearAllUsersBtn.style.display = 'none';
            } else {
                const userTags = Array.from(selectedUsers.values()).map(user =>
                    `<span class="user-tag">
                        ${user.name}（${user.organization_unitName}）
                        <span class="remove-user" onclick="removeUser(${user.id})">&times;</span>
                    </span>`
                ).join('');
                display.innerHTML = userTags;
                clearAllUsersBtn.style.display = 'inline-block';
            }

            // 更新隐藏字段
            const userIds = Array.from(selectedUsers.keys()).join(',');
            document.getElementById('selectedUsersInput').value = userIds;
        }

        // 移除用户
        function removeUser(userId) {
            selectedUsers.delete(userId);
            updateSelectedUsersDisplay();

            // 更新搜索结果中的复选框状态
            const checkbox = document.getElementById(`user-${userId}`);
            if (checkbox) {
                checkbox.checked = false;
            }
        }

        // 更新上传按钮状态
        function updateUploadButtonState() {
            const shareByUser = shareByUserCheckbox.checked;
            const shareByCode = shareByCodeCheckbox.checked;
            const hasUsers = selectedUsers.size > 0;
            const hasFile = selectedFile !== null;

            // 至少选择一种分享方式，如果选择按用户分享则必须选择用户，必须选择文件
            const canUpload = (shareByUser || shareByCode) &&
                             (!shareByUser || hasUsers) &&
                             hasFile;

            uploadBtn.disabled = !canUpload;
        }

        // 全选/取消全选
        document.getElementById('selectAllResults').addEventListener('change', function(e) {
            const checkboxes = document.querySelectorAll('#searchResults input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = e.target.checked;
                const userId = parseInt(checkbox.id.replace('user-', ''));
                toggleUserSelection(userId, e.target.checked);
            });
        });

        // 文件拖拽功能
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        // 文件选择
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });

        // 处理文件选择
        function handleFileSelect(file) {
            const maxSize = <?= MAX_FILE_SIZE ?>;
            const allowedTypes = <?= json_encode(ALLOWED_EXTENSIONS) ?>;

            // 验证文件大小
            if (file.size > maxSize) {
                showAlert('文件大小超过限制（<?= h($maxFileSize) ?>）', 'danger');
                return;
            }

            // 验证文件类型
            const extension = file.name.split('.').pop().toLowerCase();
            if (!allowedTypes.includes(extension)) {
                showAlert('不支持的文件类型', 'danger');
                return;
            }

            selectedFile = file;

            // 显示文件信息
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            document.getElementById('fileType').textContent = extension.toUpperCase();

            // 显示文件信息区域
            fileInfoSection.style.display = 'block';
            uploadArea.style.display = 'none';

            updateUploadButtonState();
        }

        // 重置文件选择
        function resetFileSelection() {
            selectedFile = null;
            fileInput.value = '';
            uploadArea.style.display = 'block';
            fileInfoSection.style.display = 'none';
            updateUploadButtonState();
        }

        // 重置整个表单
        function resetForm() {
            // 重置分享方式
            shareByUserCheckbox.checked = true;
            shareByCodeCheckbox.checked = false;

            // 重置用户选择
            selectedUsers.clear();
            updateSelectedUsersDisplay();
            document.getElementById('shareUserSearch').value = '';
            document.getElementById('searchResults').style.display = 'none';
            document.getElementById('selectAllResults').checked = false;

            // 重置分享时长
            document.getElementById('expireDays').value = '30';

            // 重置文件选择
            resetFileSelection();

            // 显示/隐藏相关区域
            userSelectionSection.style.display = 'block';

            updateUploadButtonState();
        }

        // 表单提交
        uploadForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const shareByUser = shareByUserCheckbox.checked;
            const shareByCode = shareByCodeCheckbox.checked;

            // 验证分享方式
            if (!shareByUser && !shareByCode) {
                showAlert('请至少选择一种分享方式', 'danger');
                return;
            }

            // 验证文件
            if (!selectedFile) {
                showAlert('请先选择文件', 'danger');
                return;
            }

            // 如果选择按用户分享，验证是否选择了用户
            if (shareByUser && selectedUsers.size === 0) {
                showAlert('请先选择要分享的用户', 'danger');
                return;
            }

            const formData = new FormData(uploadForm);
            formData.append('file', selectedFile);

            // 显示进度条
            progressContainer.style.display = 'block';
            uploadForm.style.display = 'none';

            try {
                const xhr = new XMLHttpRequest();

                // 上传进度
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        updateProgress(percentComplete);
                    }
                });

                xhr.onload = function() {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            showSuccessModal(response.file_info);
                        } else {
                            showAlert(response.message, 'danger');
                            resetToForm();
                        }
                    } else {
                        showAlert('上传失败，请稍后重试', 'danger');
                        resetToForm();
                    }
                };

                xhr.onerror = function() {
                    showAlert('网络错误，请检查网络连接', 'danger');
                    resetToForm();
                };

                xhr.open('POST', 'anonymous_upload.php');
                xhr.send(formData);

            } catch (error) {
                showAlert('上传失败：' + error.message, 'danger');
                resetToForm();
            }
        });

        // 更新进度条
        function updateProgress(percent) {
            const progressBar = document.querySelector('.progress-bar');
            const statusText = document.getElementById('uploadStatus');

            progressBar.style.width = percent + '%';

            if (percent < 100) {
                statusText.textContent = `上传中... ${percent.toFixed(1)}%`;
            } else {
                statusText.textContent = '处理中，请稍候...';
            }
        }

        // 显示成功弹窗
        function showSuccessModal(fileInfo) {
            currentShareCode = fileInfo.share_code;

            // 填充弹窗信息
            document.getElementById('modalFileName').textContent = fileInfo.file_name;
            document.getElementById('modalFileSize').textContent = formatFileSize(fileInfo.file_size);
            document.getElementById('modalUploadTime').textContent = fileInfo.upload_time;
            document.getElementById('modalUploader').textContent = fileInfo.uploader;

            // 分享时长
            const expireDaysText = fileInfo.expire_days == 0 ? '永不过期' : fileInfo.expire_days + '天';
            document.getElementById('modalExpireDays').textContent = expireDaysText;

            // 分享码（仅当启用分享码时显示）
            const shareCodeSection = document.getElementById('modalShareCodeSection');
            if (fileInfo.share_by_code) {
                document.getElementById('modalShareCode').value = fileInfo.share_code;
                shareCodeSection.style.display = 'block';
            } else {
                shareCodeSection.style.display = 'none';
            }

            // 分享对象（仅当选择用户时显示）
            const sharedUsersSection = document.getElementById('modalSharedUsersSection');
            if (fileInfo.share_by_user && fileInfo.shared_users.length > 0) {
                const userList = fileInfo.shared_users.map(user =>
                    `${user.organization_unitName}-${user.name}`
                ).join('、');
                document.getElementById('modalSharedUsers').textContent = userList;
                sharedUsersSection.style.display = 'block';
            } else {
                sharedUsersSection.style.display = 'none';
            }

            // 公开访问
            const isPublicBadge = document.getElementById('modalIsPublic');
            if (fileInfo.is_public) {
                isPublicBadge.textContent = '是';
                isPublicBadge.className = 'badge bg-success';
            } else {
                isPublicBadge.textContent = '否';
                isPublicBadge.className = 'badge bg-secondary';
            }

            // 隐藏进度条，显示弹窗
            progressContainer.style.display = 'none';
            const modal = new bootstrap.Modal(document.getElementById('successModal'));
            modal.show();
        }

        function resetToForm() {
            progressContainer.style.display = 'none';
            uploadForm.style.display = 'block';
        }

        // 继续上传
        function uploadAnother() {
            // 刷新页面
            window.location.reload();
        }

        // 复制分享码
        function copyShareCodeToClipboard() {
            const shareCodeInput = document.getElementById('modalShareCode');
            const shareCode = shareCodeInput.value || currentShareCode;

            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(shareCode).then(() => {
                    showAlert('分享码已复制到剪贴板', 'success');
                }).catch(() => {
                    fallbackCopyTextToClipboard(shareCode);
                });
            } else {
                fallbackCopyTextToClipboard(shareCode);
            }
        }

        // 备用复制方法
        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    showAlert('分享码已复制到剪贴板', 'success');
                } else {
                    showAlert('复制失败，请手动复制', 'warning');
                }
            } catch (err) {
                showAlert('复制失败，请手动复制', 'warning');
            }

            document.body.removeChild(textArea);
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes >= 1073741824) {
                return (bytes / 1073741824).toFixed(2) + ' GB';
            } else if (bytes >= 1048576) {
                return (bytes / 1048576).toFixed(2) + ' MB';
            } else if (bytes >= 1024) {
                return (bytes / 1024).toFixed(2) + ' KB';
            } else {
                return bytes + ' B';
            }
        }

        // 显示提示消息
        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', alertHtml);

            setTimeout(() => {
                const alert = document.querySelector('.alert:last-child');
                if (alert) {
                    bootstrap.Alert.getOrCreateInstance(alert).close();
                }
            }, 5000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化显示状态
            userSelectionSection.style.display = 'block';
            updateUploadButtonState();
        });
    </script>
</body>
</html>
