import{s as wo,d as di,u as re,a as xo,c as he,p as hn,r as H,w as Wn,h as pi,n as Xt,i as Je,b as ze,_ as an,o as at,e as ce,f as fe,g as q,j as h,k as w,l as L,m as bo,F as He,q as ne,t as Eo,v as Ve,x as J,y as je,z as et,A as So,B as hi,C as fr,D as dr,E as Q,G as ko,H as Qn,I as Zn,J as mi,K as Jn,L as jn,M as Ro,N as Co,O as er,P as $o,Q as Po,R as Vo,S as Mo,T as Ao}from"./index-CAaVVVtz.js";/*!
  * vue-router v4.5.1
  * (c) 2025 <PERSON>
  * @license MIT
  */const Ke=typeof document<"u";function _i(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function No(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&_i(e.default)}const Z=Object.assign;function mn(e,t){const n={};for(const r in t){const i=t[r];n[r]=Re(i)?i.map(e):e(i)}return n}const vt=()=>{},Re=Array.isArray,gi=/#/g,Io=/&/g,To=/\//g,Uo=/=/g,Oo=/\?/g,vi=/\+/g,Do=/%5B/g,Lo=/%5D/g,yi=/%5E/g,Fo=/%60/g,wi=/%7B/g,qo=/%7C/g,xi=/%7D/g,zo=/%20/g;function tr(e){return encodeURI(""+e).replace(qo,"|").replace(Do,"[").replace(Lo,"]")}function Ho(e){return tr(e).replace(wi,"{").replace(xi,"}").replace(yi,"^")}function Sn(e){return tr(e).replace(vi,"%2B").replace(zo,"+").replace(gi,"%23").replace(Io,"%26").replace(Fo,"`").replace(wi,"{").replace(xi,"}").replace(yi,"^")}function Bo(e){return Sn(e).replace(Uo,"%3D")}function Xo(e){return tr(e).replace(gi,"%23").replace(Oo,"%3F")}function Go(e){return e==null?"":Xo(e).replace(To,"%2F")}function wt(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Yo=/\/$/,Ko=e=>e.replace(Yo,"");function _n(e,t,n="/"){let r,i={},o="",l="";const u=t.indexOf("#");let f=t.indexOf("?");return u<f&&u>=0&&(f=-1),f>-1&&(r=t.slice(0,f),o=t.slice(f+1,u>-1?u:t.length),i=e(o)),u>-1&&(r=r||t.slice(0,u),l=t.slice(u,t.length)),r=Jo(r??t,n),{fullPath:r+(o&&"?")+o+l,path:r,query:i,hash:wt(l)}}function Wo(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function pr(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Qo(e,t,n){const r=t.matched.length-1,i=n.matched.length-1;return r>-1&&r===i&&tt(t.matched[r],n.matched[i])&&bi(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function tt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function bi(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Zo(e[n],t[n]))return!1;return!0}function Zo(e,t){return Re(e)?hr(e,t):Re(t)?hr(t,e):e===t}function hr(e,t){return Re(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function Jo(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),i=r[r.length-1];(i===".."||i===".")&&r.push("");let o=n.length-1,l,u;for(l=0;l<r.length;l++)if(u=r[l],u!==".")if(u==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+r.slice(l).join("/")}const Oe={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var xt;(function(e){e.pop="pop",e.push="push"})(xt||(xt={}));var yt;(function(e){e.back="back",e.forward="forward",e.unknown=""})(yt||(yt={}));function jo(e){if(!e)if(Ke){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Ko(e)}const el=/^[^#]+#/;function tl(e,t){return e.replace(el,"#")+t}function nl(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const sn=()=>({left:window.scrollX,top:window.scrollY});function rl(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),i=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!i)return;t=nl(i,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function mr(e,t){return(history.state?history.state.position-t:-1)+e}const kn=new Map;function il(e,t){kn.set(e,t)}function ol(e){const t=kn.get(e);return kn.delete(e),t}let ll=()=>location.protocol+"//"+location.host;function Ei(e,t){const{pathname:n,search:r,hash:i}=t,o=e.indexOf("#");if(o>-1){let u=i.includes(e.slice(o))?e.slice(o).length:1,f=i.slice(u);return f[0]!=="/"&&(f="/"+f),pr(f,"")}return pr(n,e)+r+i}function al(e,t,n,r){let i=[],o=[],l=null;const u=({state:d})=>{const p=Ei(e,location),g=n.value,R=t.value;let x=0;if(d){if(n.value=p,t.value=d,l&&l===g){l=null;return}x=R?d.position-R.position:0}else r(p);i.forEach(S=>{S(n.value,g,{delta:x,type:xt.pop,direction:x?x>0?yt.forward:yt.back:yt.unknown})})};function f(){l=n.value}function s(d){i.push(d);const p=()=>{const g=i.indexOf(d);g>-1&&i.splice(g,1)};return o.push(p),p}function a(){const{history:d}=window;d.state&&d.replaceState(Z({},d.state,{scroll:sn()}),"")}function c(){for(const d of o)d();o=[],window.removeEventListener("popstate",u),window.removeEventListener("beforeunload",a)}return window.addEventListener("popstate",u),window.addEventListener("beforeunload",a,{passive:!0}),{pauseListeners:f,listen:s,destroy:c}}function _r(e,t,n,r=!1,i=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:i?sn():null}}function sl(e){const{history:t,location:n}=window,r={value:Ei(e,n)},i={value:t.state};i.value||o(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(f,s,a){const c=e.indexOf("#"),d=c>-1?(n.host&&document.querySelector("base")?e:e.slice(c))+f:ll()+e+f;try{t[a?"replaceState":"pushState"](s,"",d),i.value=s}catch(p){console.error(p),n[a?"replace":"assign"](d)}}function l(f,s){const a=Z({},t.state,_r(i.value.back,f,i.value.forward,!0),s,{position:i.value.position});o(f,a,!0),r.value=f}function u(f,s){const a=Z({},i.value,t.state,{forward:f,scroll:sn()});o(a.current,a,!0);const c=Z({},_r(r.value,f,null),{position:a.position+1},s);o(f,c,!1),r.value=f}return{location:r,state:i,push:u,replace:l}}function ul(e){e=jo(e);const t=sl(e),n=al(e,t.state,t.location,t.replace);function r(o,l=!0){l||n.pauseListeners(),history.go(o)}const i=Z({location:"",base:e,go:r,createHref:tl.bind(null,e)},t,n);return Object.defineProperty(i,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(i,"state",{enumerable:!0,get:()=>t.state.value}),i}function cl(e){return typeof e=="string"||e&&typeof e=="object"}function Si(e){return typeof e=="string"||typeof e=="symbol"}const ki=Symbol("");var gr;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(gr||(gr={}));function nt(e,t){return Z(new Error,{type:e,[ki]:!0},t)}function Ne(e,t){return e instanceof Error&&ki in e&&(t==null||!!(e.type&t))}const vr="[^/]+?",fl={sensitive:!1,strict:!1,start:!0,end:!0},dl=/[.+*?^${}()[\]/\\]/g;function pl(e,t){const n=Z({},fl,t),r=[];let i=n.start?"^":"";const o=[];for(const s of e){const a=s.length?[]:[90];n.strict&&!s.length&&(i+="/");for(let c=0;c<s.length;c++){const d=s[c];let p=40+(n.sensitive?.25:0);if(d.type===0)c||(i+="/"),i+=d.value.replace(dl,"\\$&"),p+=40;else if(d.type===1){const{value:g,repeatable:R,optional:x,regexp:S}=d;o.push({name:g,repeatable:R,optional:x});const E=S||vr;if(E!==vr){p+=10;try{new RegExp(`(${E})`)}catch(A){throw new Error(`Invalid custom RegExp for param "${g}" (${E}): `+A.message)}}let $=R?`((?:${E})(?:/(?:${E}))*)`:`(${E})`;c||($=x&&s.length<2?`(?:/${$})`:"/"+$),x&&($+="?"),i+=$,p+=20,x&&(p+=-8),R&&(p+=-20),E===".*"&&(p+=-50)}a.push(p)}r.push(a)}if(n.strict&&n.end){const s=r.length-1;r[s][r[s].length-1]+=.7000000000000001}n.strict||(i+="/?"),n.end?i+="$":n.strict&&!i.endsWith("/")&&(i+="(?:/|$)");const l=new RegExp(i,n.sensitive?"":"i");function u(s){const a=s.match(l),c={};if(!a)return null;for(let d=1;d<a.length;d++){const p=a[d]||"",g=o[d-1];c[g.name]=p&&g.repeatable?p.split("/"):p}return c}function f(s){let a="",c=!1;for(const d of e){(!c||!a.endsWith("/"))&&(a+="/"),c=!1;for(const p of d)if(p.type===0)a+=p.value;else if(p.type===1){const{value:g,repeatable:R,optional:x}=p,S=g in s?s[g]:"";if(Re(S)&&!R)throw new Error(`Provided param "${g}" is an array but it is not repeatable (* or + modifiers)`);const E=Re(S)?S.join("/"):S;if(!E)if(x)d.length<2&&(a.endsWith("/")?a=a.slice(0,-1):c=!0);else throw new Error(`Missing required param "${g}"`);a+=E}}return a||"/"}return{re:l,score:r,keys:o,parse:u,stringify:f}}function hl(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Ri(e,t){let n=0;const r=e.score,i=t.score;for(;n<r.length&&n<i.length;){const o=hl(r[n],i[n]);if(o)return o;n++}if(Math.abs(i.length-r.length)===1){if(yr(r))return 1;if(yr(i))return-1}return i.length-r.length}function yr(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const ml={type:0,value:""},_l=/[a-zA-Z0-9_]/;function gl(e){if(!e)return[[]];if(e==="/")return[[ml]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(p){throw new Error(`ERR (${n})/"${s}": ${p}`)}let n=0,r=n;const i=[];let o;function l(){o&&i.push(o),o=[]}let u=0,f,s="",a="";function c(){s&&(n===0?o.push({type:0,value:s}):n===1||n===2||n===3?(o.length>1&&(f==="*"||f==="+")&&t(`A repeatable param (${s}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:s,regexp:a,repeatable:f==="*"||f==="+",optional:f==="*"||f==="?"})):t("Invalid state to consume buffer"),s="")}function d(){s+=f}for(;u<e.length;){if(f=e[u++],f==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:f==="/"?(s&&c(),l()):f===":"?(c(),n=1):d();break;case 4:d(),n=r;break;case 1:f==="("?n=2:_l.test(f)?d():(c(),n=0,f!=="*"&&f!=="?"&&f!=="+"&&u--);break;case 2:f===")"?a[a.length-1]=="\\"?a=a.slice(0,-1)+f:n=3:a+=f;break;case 3:c(),n=0,f!=="*"&&f!=="?"&&f!=="+"&&u--,a="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${s}"`),c(),l(),i}function vl(e,t,n){const r=pl(gl(e.path),n),i=Z(r,{record:e,parent:t,children:[],alias:[]});return t&&!i.record.aliasOf==!t.record.aliasOf&&t.children.push(i),i}function yl(e,t){const n=[],r=new Map;t=Er({strict:!1,end:!0,sensitive:!1},t);function i(c){return r.get(c)}function o(c,d,p){const g=!p,R=xr(c);R.aliasOf=p&&p.record;const x=Er(t,c),S=[R];if("alias"in c){const A=typeof c.alias=="string"?[c.alias]:c.alias;for(const I of A)S.push(xr(Z({},R,{components:p?p.record.components:R.components,path:I,aliasOf:p?p.record:R})))}let E,$;for(const A of S){const{path:I}=A;if(d&&I[0]!=="/"){const N=d.record.path,b=N[N.length-1]==="/"?"":"/";A.path=d.record.path+(I&&b+I)}if(E=vl(A,d,x),p?p.alias.push(E):($=$||E,$!==E&&$.alias.push(E),g&&c.name&&!br(E)&&l(c.name)),Ci(E)&&f(E),R.children){const N=R.children;for(let b=0;b<N.length;b++)o(N[b],E,p&&p.children[b])}p=p||E}return $?()=>{l($)}:vt}function l(c){if(Si(c)){const d=r.get(c);d&&(r.delete(c),n.splice(n.indexOf(d),1),d.children.forEach(l),d.alias.forEach(l))}else{const d=n.indexOf(c);d>-1&&(n.splice(d,1),c.record.name&&r.delete(c.record.name),c.children.forEach(l),c.alias.forEach(l))}}function u(){return n}function f(c){const d=bl(c,n);n.splice(d,0,c),c.record.name&&!br(c)&&r.set(c.record.name,c)}function s(c,d){let p,g={},R,x;if("name"in c&&c.name){if(p=r.get(c.name),!p)throw nt(1,{location:c});x=p.record.name,g=Z(wr(d.params,p.keys.filter($=>!$.optional).concat(p.parent?p.parent.keys.filter($=>$.optional):[]).map($=>$.name)),c.params&&wr(c.params,p.keys.map($=>$.name))),R=p.stringify(g)}else if(c.path!=null)R=c.path,p=n.find($=>$.re.test(R)),p&&(g=p.parse(R),x=p.record.name);else{if(p=d.name?r.get(d.name):n.find($=>$.re.test(d.path)),!p)throw nt(1,{location:c,currentLocation:d});x=p.record.name,g=Z({},d.params,c.params),R=p.stringify(g)}const S=[];let E=p;for(;E;)S.unshift(E.record),E=E.parent;return{name:x,path:R,params:g,matched:S,meta:xl(S)}}e.forEach(c=>o(c));function a(){n.length=0,r.clear()}return{addRoute:o,resolve:s,removeRoute:l,clearRoutes:a,getRoutes:u,getRecordMatcher:i}}function wr(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function xr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:wl(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function wl(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function br(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function xl(e){return e.reduce((t,n)=>Z(t,n.meta),{})}function Er(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function bl(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;Ri(e,t[o])<0?r=o:n=o+1}const i=El(e);return i&&(r=t.lastIndexOf(i,r-1)),r}function El(e){let t=e;for(;t=t.parent;)if(Ci(t)&&Ri(e,t)===0)return t}function Ci({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Sl(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let i=0;i<r.length;++i){const o=r[i].replace(vi," "),l=o.indexOf("="),u=wt(l<0?o:o.slice(0,l)),f=l<0?null:wt(o.slice(l+1));if(u in t){let s=t[u];Re(s)||(s=t[u]=[s]),s.push(f)}else t[u]=f}return t}function Sr(e){let t="";for(let n in e){const r=e[n];if(n=Bo(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(Re(r)?r.map(o=>o&&Sn(o)):[r&&Sn(r)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function kl(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=Re(r)?r.map(i=>i==null?null:""+i):r==null?r:""+r)}return t}const Rl=Symbol(""),kr=Symbol(""),un=Symbol(""),$i=Symbol(""),Rn=Symbol("");function ut(){let e=[];function t(r){return e.push(r),()=>{const i=e.indexOf(r);i>-1&&e.splice(i,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function De(e,t,n,r,i,o=l=>l()){const l=r&&(r.enterCallbacks[i]=r.enterCallbacks[i]||[]);return()=>new Promise((u,f)=>{const s=d=>{d===!1?f(nt(4,{from:n,to:t})):d instanceof Error?f(d):cl(d)?f(nt(2,{from:t,to:d})):(l&&r.enterCallbacks[i]===l&&typeof d=="function"&&l.push(d),u())},a=o(()=>e.call(r&&r.instances[i],t,n,s));let c=Promise.resolve(a);e.length<3&&(c=c.then(s)),c.catch(d=>f(d))})}function gn(e,t,n,r,i=o=>o()){const o=[];for(const l of e)for(const u in l.components){let f=l.components[u];if(!(t!=="beforeRouteEnter"&&!l.instances[u]))if(_i(f)){const a=(f.__vccOpts||f)[t];a&&o.push(De(a,n,r,l,u,i))}else{let s=f();o.push(()=>s.then(a=>{if(!a)throw new Error(`Couldn't resolve component "${u}" at "${l.path}"`);const c=No(a)?a.default:a;l.mods[u]=a,l.components[u]=c;const p=(c.__vccOpts||c)[t];return p&&De(p,n,r,l,u,i)()}))}}return o}function Rr(e){const t=Je(un),n=Je($i),r=he(()=>{const f=re(e.to);return t.resolve(f)}),i=he(()=>{const{matched:f}=r.value,{length:s}=f,a=f[s-1],c=n.matched;if(!a||!c.length)return-1;const d=c.findIndex(tt.bind(null,a));if(d>-1)return d;const p=Cr(f[s-2]);return s>1&&Cr(a)===p&&c[c.length-1].path!==p?c.findIndex(tt.bind(null,f[s-2])):d}),o=he(()=>i.value>-1&&Ml(n.params,r.value.params)),l=he(()=>i.value>-1&&i.value===n.matched.length-1&&bi(n.params,r.value.params));function u(f={}){if(Vl(f)){const s=t[re(e.replace)?"replace":"push"](re(e.to)).catch(vt);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>s),s}return Promise.resolve()}return{route:r,href:he(()=>r.value.href),isActive:o,isExactActive:l,navigate:u}}function Cl(e){return e.length===1?e[0]:e}const $l=di({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Rr,setup(e,{slots:t}){const n=ze(Rr(e)),{options:r}=Je(un),i=he(()=>({[$r(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[$r(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&Cl(t.default(n));return e.custom?o:pi("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:i.value},o)}}}),Pl=$l;function Vl(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Ml(e,t){for(const n in t){const r=t[n],i=e[n];if(typeof r=="string"){if(r!==i)return!1}else if(!Re(i)||i.length!==r.length||r.some((o,l)=>o!==i[l]))return!1}return!0}function Cr(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const $r=(e,t,n)=>e??t??n,Al=di({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=Je(Rn),i=he(()=>e.route||r.value),o=Je(kr,0),l=he(()=>{let s=re(o);const{matched:a}=i.value;let c;for(;(c=a[s])&&!c.components;)s++;return s}),u=he(()=>i.value.matched[l.value]);hn(kr,he(()=>l.value+1)),hn(Rl,u),hn(Rn,i);const f=H();return Wn(()=>[f.value,u.value,e.name],([s,a,c],[d,p,g])=>{a&&(a.instances[c]=s,p&&p!==a&&s&&s===d&&(a.leaveGuards.size||(a.leaveGuards=p.leaveGuards),a.updateGuards.size||(a.updateGuards=p.updateGuards))),s&&a&&(!p||!tt(a,p)||!d)&&(a.enterCallbacks[c]||[]).forEach(R=>R(s))},{flush:"post"}),()=>{const s=i.value,a=e.name,c=u.value,d=c&&c.components[a];if(!d)return Pr(n.default,{Component:d,route:s});const p=c.props[a],g=p?p===!0?s.params:typeof p=="function"?p(s):p:null,x=pi(d,Z({},g,t,{onVnodeUnmounted:S=>{S.component.isUnmounted&&(c.instances[a]=null)},ref:f}));return Pr(n.default,{Component:x,route:s})||x}}});function Pr(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Nl=Al;function Il(e){const t=yl(e.routes,e),n=e.parseQuery||Sl,r=e.stringifyQuery||Sr,i=e.history,o=ut(),l=ut(),u=ut(),f=wo(Oe);let s=Oe;Ke&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const a=mn.bind(null,k=>""+k),c=mn.bind(null,Go),d=mn.bind(null,wt);function p(k,O){let T,z;return Si(k)?(T=t.getRecordMatcher(k),z=O):z=k,t.addRoute(z,T)}function g(k){const O=t.getRecordMatcher(k);O&&t.removeRoute(O)}function R(){return t.getRoutes().map(k=>k.record)}function x(k){return!!t.getRecordMatcher(k)}function S(k,O){if(O=Z({},O||f.value),typeof k=="string"){const X=_n(n,k,O.path),ue=t.resolve({path:X.path},O),st=i.createHref(X.fullPath);return Z(X,ue,{params:d(ue.params),hash:wt(X.hash),redirectedFrom:void 0,href:st})}let T;if(k.path!=null)T=Z({},k,{path:_n(n,k.path,O.path).path});else{const X=Z({},k.params);for(const ue in X)X[ue]==null&&delete X[ue];T=Z({},k,{params:c(X)}),O.params=c(O.params)}const z=t.resolve(T,O),j=k.hash||"";z.params=a(d(z.params));const ae=Wo(r,Z({},k,{hash:Ho(j),path:z.path})),Y=i.createHref(ae);return Z({fullPath:ae,hash:j,query:r===Sr?kl(k.query):k.query||{}},z,{redirectedFrom:void 0,href:Y})}function E(k){return typeof k=="string"?_n(n,k,f.value.path):Z({},k)}function $(k,O){if(s!==k)return nt(8,{from:O,to:k})}function A(k){return b(k)}function I(k){return A(Z(E(k),{replace:!0}))}function N(k){const O=k.matched[k.matched.length-1];if(O&&O.redirect){const{redirect:T}=O;let z=typeof T=="function"?T(k):T;return typeof z=="string"&&(z=z.includes("?")||z.includes("#")?z=E(z):{path:z},z.params={}),Z({query:k.query,hash:k.hash,params:z.path!=null?{}:k.params},z)}}function b(k,O){const T=s=S(k),z=f.value,j=k.state,ae=k.force,Y=k.replace===!0,X=N(T);if(X)return b(Z(E(X),{state:typeof X=="object"?Z({},j,X.state):j,force:ae,replace:Y}),O||T);const ue=T;ue.redirectedFrom=O;let st;return!ae&&Qo(r,z,T)&&(st=nt(16,{to:ue,from:z}),G(z,z,!0,!1)),(st?Promise.resolve(st):y(ue,z)).catch(me=>Ne(me)?Ne(me,2)?me:M(me):W(me,ue,z)).then(me=>{if(me){if(Ne(me,2))return b(Z({replace:Y},E(me.to),{state:typeof me.to=="object"?Z({},j,me.to.state):j,force:ae}),O||ue)}else me=V(ue,z,!0,Y,j);return m(ue,z,me),me})}function _(k,O){const T=$(k,O);return T?Promise.reject(T):Promise.resolve()}function C(k){const O=Se.values().next().value;return O&&typeof O.runWithContext=="function"?O.runWithContext(k):k()}function y(k,O){let T;const[z,j,ae]=Tl(k,O);T=gn(z.reverse(),"beforeRouteLeave",k,O);for(const X of z)X.leaveGuards.forEach(ue=>{T.push(De(ue,k,O))});const Y=_.bind(null,k,O);return T.push(Y),xe(T).then(()=>{T=[];for(const X of o.list())T.push(De(X,k,O));return T.push(Y),xe(T)}).then(()=>{T=gn(j,"beforeRouteUpdate",k,O);for(const X of j)X.updateGuards.forEach(ue=>{T.push(De(ue,k,O))});return T.push(Y),xe(T)}).then(()=>{T=[];for(const X of ae)if(X.beforeEnter)if(Re(X.beforeEnter))for(const ue of X.beforeEnter)T.push(De(ue,k,O));else T.push(De(X.beforeEnter,k,O));return T.push(Y),xe(T)}).then(()=>(k.matched.forEach(X=>X.enterCallbacks={}),T=gn(ae,"beforeRouteEnter",k,O,C),T.push(Y),xe(T))).then(()=>{T=[];for(const X of l.list())T.push(De(X,k,O));return T.push(Y),xe(T)}).catch(X=>Ne(X,8)?X:Promise.reject(X))}function m(k,O,T){u.list().forEach(z=>C(()=>z(k,O,T)))}function V(k,O,T,z,j){const ae=$(k,O);if(ae)return ae;const Y=O===Oe,X=Ke?history.state:{};T&&(z||Y?i.replace(k.fullPath,Z({scroll:Y&&X&&X.scroll},j)):i.push(k.fullPath,j)),f.value=k,G(k,O,T,Y),M()}let P;function U(){P||(P=i.listen((k,O,T)=>{if(!B.listening)return;const z=S(k),j=N(z);if(j){b(Z(j,{replace:!0,force:!0}),z).catch(vt);return}s=z;const ae=f.value;Ke&&il(mr(ae.fullPath,T.delta),sn()),y(z,ae).catch(Y=>Ne(Y,12)?Y:Ne(Y,2)?(b(Z(E(Y.to),{force:!0}),z).then(X=>{Ne(X,20)&&!T.delta&&T.type===xt.pop&&i.go(-1,!1)}).catch(vt),Promise.reject()):(T.delta&&i.go(-T.delta,!1),W(Y,z,ae))).then(Y=>{Y=Y||V(z,ae,!1),Y&&(T.delta&&!Ne(Y,8)?i.go(-T.delta,!1):T.type===xt.pop&&Ne(Y,20)&&i.go(-1,!1)),m(z,ae,Y)}).catch(vt)}))}let v=ut(),D=ut(),F;function W(k,O,T){M(k);const z=D.list();return z.length?z.forEach(j=>j(k,O,T)):console.error(k),Promise.reject(k)}function ie(){return F&&f.value!==Oe?Promise.resolve():new Promise((k,O)=>{v.add([k,O])})}function M(k){return F||(F=!k,U(),v.list().forEach(([O,T])=>k?T(k):O()),v.reset()),k}function G(k,O,T,z){const{scrollBehavior:j}=e;if(!Ke||!j)return Promise.resolve();const ae=!T&&ol(mr(k.fullPath,0))||(z||!T)&&history.state&&history.state.scroll||null;return Xt().then(()=>j(k,O,ae)).then(Y=>Y&&rl(Y)).catch(Y=>W(Y,k,O))}const de=k=>i.go(k);let pe;const Se=new Set,B={currentRoute:f,listening:!0,addRoute:p,removeRoute:g,clearRoutes:t.clearRoutes,hasRoute:x,getRoutes:R,resolve:S,options:e,push:A,replace:I,go:de,back:()=>de(-1),forward:()=>de(1),beforeEach:o.add,beforeResolve:l.add,afterEach:u.add,onError:D.add,isReady:ie,install(k){const O=this;k.component("RouterLink",Pl),k.component("RouterView",Nl),k.config.globalProperties.$router=O,Object.defineProperty(k.config.globalProperties,"$route",{enumerable:!0,get:()=>re(f)}),Ke&&!pe&&f.value===Oe&&(pe=!0,A(i.location).catch(j=>{}));const T={};for(const j in Oe)Object.defineProperty(T,j,{get:()=>f.value[j],enumerable:!0});k.provide(un,O),k.provide($i,xo(T)),k.provide(Rn,f);const z=k.unmount;Se.add(k),k.unmount=function(){Se.delete(k),Se.size<1&&(s=Oe,P&&P(),P=null,f.value=Oe,pe=!1,F=!1),z()}}};function xe(k){return k.reduce((O,T)=>O.then(()=>C(T)),Promise.resolve())}return B}function Tl(e,t){const n=[],r=[],i=[],o=Math.max(t.matched.length,e.matched.length);for(let l=0;l<o;l++){const u=t.matched[l];u&&(e.matched.find(s=>tt(s,u))?r.push(u):n.push(u));const f=e.matched[l];f&&(t.matched.find(s=>tt(s,f))||i.push(f))}return[n,r,i]}function Ul(){return Je(un)}const Ol={class:"app-container"},Dl={class:"grid-container"},Ll={class:"header"},Fl={class:"header-left"},ql={class:"user-info"},zl={class:"el-dropdown-link"},Hl={class:"user-name"},Bl={class:"sidebar"},Xl={class:"content",ref:"contentRef"},Gl={__name:"App",setup(e){const t=Ul(),n=H(""),r=H([]),i=H(!1),o=H(""),l=H(null);at(async()=>{await u(),await f(),t.push("/unit-management")});const u=async()=>{const N=await ce.post("/api/get_user_info.php");n.value=N.user.name},f=async()=>{const N=await ce.post("/api/get_user_app.php");r.value=N.data},s=N=>{console.log("点击的菜单对应的路由是:",N),t.push(N)},a=()=>{i.value=!i.value},c=H(!1),d=H({oldPassword:"",newPassword:"",confirmPassword:""}),p=H(null),g=H(!1),R=H(!1),x=()=>{g.value=!g.value},S=()=>{R.value=!R.value},E=async N=>{N==="logout"?(await ce.get("/api/logout.php"),window.location.href="login.html"):N==="changePassword"&&(c.value=!0)},$=async()=>{p.value&&await p.value.validate(N=>{if(N){if(d.value.newPassword!==d.value.confirmPassword){Q.error("两次输入的密码不一致");return}const b=new FormData;b.append("old_password",d.value.oldPassword),b.append("new_password",d.value.newPassword),ce.post("/api/change_password.php",b).then(()=>{Q.success("密码修改成功，请重新登录"),c.value=!1,d.value={oldPassword:"",newPassword:"",confirmPassword:""},setTimeout(()=>{window.location.href="login.html"},3e3)}).catch(()=>{Q.error("密码修改失败")})}})},A=ze({oldPassword:[{required:!0,message:"请输入旧密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"}],confirmPassword:[{required:!0,message:"请输入确认新密码",trigger:"blur"},{validator:(N,b,_)=>{b!==d.value.newPassword?_(new Error("两次输入的密码不一致")):_()},trigger:"blur"}]}),I=()=>{const N=l.value;N&&(document.fullscreenElement?document.exitFullscreen():N.requestFullscreen().catch(b=>{console.error("全屏失败:",b),Q.error("全屏功能不支持")}))};return(N,b)=>{const _=L("el-button"),C=L("el-icon"),y=L("el-avatar"),m=L("el-dropdown-item"),V=L("el-dropdown-menu"),P=L("el-dropdown"),U=L("el-menu-item"),v=L("el-menu"),D=L("router-view"),F=L("el-input"),W=L("el-form-item"),ie=L("el-form"),M=L("el-dialog");return ne(),fe(He,null,[q("div",Ol,[q("div",Dl,[q("div",Ll,[q("div",Fl,[h(_,{onClick:a,type:"text",class:"menu-btn"},{default:w(()=>b[5]||(b[5]=[q("i",{class:"el-icon-menu"},null,-1)])),_:1,__:[5]}),b[6]||(b[6]=q("img",{src:bo,alt:"Logo",class:"header-logo"},null,-1)),b[7]||(b[7]=q("span",{class:"logo"},"CloudPivot",-1))])]),q("div",ql,[h(P,{onCommand:E},{dropdown:w(()=>[h(V,null,{default:w(()=>[h(m,{command:"profile"},{default:w(()=>b[8]||(b[8]=[J("个人信息")])),_:1,__:[8]}),h(m,{command:"changePassword"},{default:w(()=>b[9]||(b[9]=[J("修改密码")])),_:1,__:[9]}),h(m,{command:"logout"},{default:w(()=>b[10]||(b[10]=[J("退出登录")])),_:1,__:[10]})]),_:1})]),default:w(()=>[q("span",zl,[h(y,{size:"small"},{default:w(()=>[h(C,null,{default:w(()=>[h(re(Eo),{style:{color:"#409EFF"}})]),_:1})]),_:1}),q("span",Hl,Ve(n.value),1)])]),_:1})]),q("div",Bl,[h(v,{"default-active":o.value,class:"el-menu-vertical",mode:"vertical",collapse:i.value,onOpen:N.handleOpen,onClose:N.handleClose},{default:w(()=>[(ne(!0),fe(He,null,je(r.value,G=>(ne(),et(U,{key:G.id,index:G.id.toString(),router:G.url,onClick:de=>s(G.url)},{title:w(()=>[q("span",null,Ve(G.application_name),1)]),_:2},1032,["index","router","onClick"]))),128))]),_:1},8,["default-active","collapse","onOpen","onClose"])]),q("div",Xl,[h(_,{onClick:I,type:"text",class:"fullscreen-btn"},{default:w(()=>[h(C,null,{default:w(()=>[h(re(So))]),_:1})]),_:1}),q("div",{class:"fullscreen-target",ref_key:"fullscreenTargetRef",ref:l},[h(D,{ref:"routerViewRef"},null,512)],512)],512)])]),h(M,{modelValue:c.value,"onUpdate:modelValue":b[4]||(b[4]=G=>c.value=G),width:"400px"},{default:w(()=>[h(ie,{model:d.value,ref_key:"passwordFormRef",ref:p,rules:A,"label-width":"120px",onSubmit:hi($,["prevent"])},{default:w(()=>[h(W,{label:"旧密码",prop:"oldPassword",required:""},{default:w(()=>[h(F,{modelValue:d.value.oldPassword,"onUpdate:modelValue":b[0]||(b[0]=G=>d.value.oldPassword=G),type:"password",placeholder:"请输入旧密码"},null,8,["modelValue"])]),_:1}),h(W,{label:"新密码",prop:"newPassword",required:""},{default:w(()=>[h(F,{modelValue:d.value.newPassword,"onUpdate:modelValue":b[1]||(b[1]=G=>d.value.newPassword=G),type:g.value?"text":"password",placeholder:"请输入新密码"},{suffix:w(()=>[h(_,{icon:g.value?re(fr):re(dr),onClick:x,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),h(W,{label:"确认新密码",prop:"confirmPassword",required:""},{default:w(()=>[h(F,{modelValue:d.value.confirmPassword,"onUpdate:modelValue":b[2]||(b[2]=G=>d.value.confirmPassword=G),type:R.value?"text":"password",placeholder:"请输入确认新密码"},{suffix:w(()=>[h(_,{icon:R.value?re(fr):re(dr),onClick:S,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),h(W,null,{default:w(()=>[h(_,{type:"primary","native-type":"submit"},{default:w(()=>b[11]||(b[11]=[J("确定")])),_:1,__:[11]}),h(_,{onClick:b[3]||(b[3]=G=>c.value=!1)},{default:w(()=>b[12]||(b[12]=[J("取消")])),_:1,__:[12]})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])],64)}}},Yl=an(Gl,[["__scopeId","data-v-727c4254"]]),Kl={class:"unit-management"},Wl={style:{"text-align":"right",margin:"10px"}},Ql={__name:"UnitManagement",setup(e){const t=H(!1),n={expandTrigger:"hover",checkStrictly:!0,value:"id",label:"unit_name",children:"children"},r=y=>{if(y&&y.length>0){const m=y[y.length-1],V=o(m);V&&(i.parentId=m,c.value=V.children||[])}else i.parentId=null,c.value=[]},i=ze({id:null,name:"",parentId:null,parentIdPath:[],code:"",sort:0});Wn(()=>i.parentId,(y,m)=>{if(console.log("parentId 发生变化，旧值: ",m,"新值: ",y),y){const V=o(y);V&&(c.value=V.children||[])}else c.value=[]},{immediate:!1});const o=(y,m=l.value)=>{for(let V=0;V<m.length;V++){if(m[V].id===y)return m[V];if(m[V].children){const P=o(y,m[V].children);if(P)return P}}return null},l=H([]),u=H(new Set),f=he(()=>{const y=[],m=(V,P=0,U=null)=>{V.forEach(v=>{y.push({...v,level:P,parentId:U,expanded:u.value.has(v.id)}),v.children&&m(v.children,P+1,v.id)})};return m(l.value),y}),s=he(()=>{const y=[],m=V=>{if(V.level===0)return!0;let P=f.value.find(U=>U.id===V.parentId);for(;P;){if(!P.expanded)return!1;P=f.value.find(U=>U.id===P.parentId)}return!0};return f.value.forEach(V=>{m(V)&&y.push(V)}),y}),a=H(!1),c=H([]),d=H(null);at(async()=>{await E()});const p=H(!1),g=y=>{const m=[];function V(P,U){for(const v of P){const D=[...U,v.id];if(v.id===y)return m.push(...D),!0;if(v.children&&v.children.length>0&&V(v.children,D))return!0}return!1}return V(l.value,[]),m},R=y=>{if(d.value&&d.value.resetFields(),t.value=!1,i.id=null,i.name="",i.code="",i.parentId=null,i.parentIdPath=[],i.sort_order=0,p.value=!!y,y){const m=o(y);m&&(i.parentId=y,console.log("parentId位置一:",y),i.parentIdPath=g(y),c.value=m.children||[])}else i.parentId=null,i.parentIdPath=[],c.value=[];a.value=!0,console.log("dialogVisible 已设为 true")},x=y=>{d.value&&d.value.resetFields(),t.value=!0,i.id=y.id;let m={currentUnit:y.unit_name,selectedSortOrder:null,selectedUnit:null};i.name=y.unit_name,i.code=y.code,i.parentId=y.parent_id,i.parentIdPath=g(y.parent_id);const V=o(y.parent_id);if(V){c.value=(V.children||[]).filter(v=>v.id!==y.id);const P=V.children||[],U=P.findIndex(v=>v.id===y.id);if(U>0){const v=P[U-1];i.sort_order=v.sort_order,m.selectedUnit=v.unit_name}else i.sort_order=0,m.selectedUnit="最前";m.selectedSortOrder=i.sort_order}else{c.value=l.value.filter(U=>U.id!==y.id);const P=l.value.findIndex(U=>U.id===y.id);if(P>0){const U=l.value[P-1];i.sort_order=U.sort_order,m.selectedUnit=U.unit_name}else i.sort_order=0,m.selectedUnit="最前";m.selectedSortOrder=i.sort_order}console.log("排序选项:",c.value),console.log("自动选择结果:",m),a.value=!0},S=()=>{d.value.validate(async y=>{if(y)try{const m=new FormData;i.id?(m.append("action","edit"),m.append("id",i.id)):m.append("action","add"),m.append("sort_order",i.sort_order+1),m.append("unit_name",i.name),m.append("code",i.code),m.append("parent_id",i.parentId||null);const V=await ce.post("api/unit_manage.php",m,{headers:{"Content-Type":"multipart/form-data"}});V.status===1?(await E(),a.value=!1,Q.success(i.id?"编辑成功":"新增成功")):Q.error(V.message)}catch(m){console.error("保存单位失败:",m),Q.error("保存单位失败，请稍后重试")}})},E=async()=>{const y=await ce.post("api/get_unit_info.php");l.value=[y.data],console.log("获取单位数据成功:",l.value);const m=V=>{V.forEach(P=>{P.children&&P.children.length>0&&(u.value.add(P.id),m(P.children))})};m(l.value)},$=y=>{er.confirm(`确定要删除 ${y.unit_name} 吗？`,"Warning",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const m=new FormData;m.append("action","del"),m.append("id",y.id),await ce.post("api/unit_manage.php",m),Q.success("删除成功"),await E()})},A=y=>{const m=new Set(u.value);m.has(y.id)?m.delete(y.id):m.add(y.id),u.value=m},I=y=>((y.parentId?o(y.parentId):{children:l.value}).children||[]).findIndex(U=>U.id===y.id)===0,N=y=>{const V=(y.parentId?o(y.parentId):{children:l.value}).children||[];return V.findIndex(U=>U.id===y.id)===V.length-1},b=async y=>{const m=new FormData;m.append("action","edit"),m.append("id",y.id),m.append("sort_order",y.sort_order-1),m.append("unit_name",y.unit_name),m.append("code",y.code),m.append("parent_id",y.parentId),await ce.post("api/unit_manage.php",m),await E()},_=async y=>{const m=new FormData;m.append("action","edit"),m.append("id",y.id),m.append("sort_order",y.sort_order+1),m.append("unit_name",y.unit_name),m.append("code",y.code),m.append("parent_id",y.parentId),await ce.post("api/unit_manage.php",m),await E()},C=he(()=>c.value.filter(y=>y.id!==i.id));return(y,m)=>{const V=L("el-button"),P=L("el-col"),U=L("el-row"),v=L("el-table-column"),D=L("el-icon"),F=L("el-button-group"),W=L("el-table"),ie=L("el-input"),M=L("el-form-item"),G=L("el-cascader"),de=L("el-option"),pe=L("el-select"),Se=L("el-form");return ne(),fe("div",Kl,[h(U,null,{default:w(()=>[h(P,{span:24},{default:w(()=>[q("div",Wl,[h(V,{type:"primary",round:"",onClick:m[0]||(m[0]=B=>R(null))},{default:w(()=>m[7]||(m[7]=[J("添加单位")])),_:1,__:[7]})])]),_:1})]),_:1}),h(W,{data:s.value,style:{width:"100%"},border:""},{default:w(()=>[h(v,{label:"操作",width:"60",align:"center"},{default:w(B=>[B.row.children&&B.row.children.length?(ne(),et(V,{key:0,size:"mini",type:"text",onClick:xe=>A(B.row)},{default:w(()=>[J(Ve(B.row.expanded?"-":"+"),1)]),_:2},1032,["onClick"])):Qn("",!0)]),_:1}),h(v,{prop:"unit_name",label:"单位名称"},{default:w(B=>[q("span",{style:Zn({paddingLeft:`${B.row.level*20}px`})},Ve(B.row.unit_name),5)]),_:1}),h(v,{prop:"code",label:"组织机构代码",width:"250"}),h(v,{label:"操作",width:"350"},{default:w(B=>[h(F,null,{default:w(()=>[h(V,{size:"mini",type:"primary",onClick:xe=>R(B.row.id)},{default:w(()=>[h(D,null,{default:w(()=>[h(re(mi))]),_:1})]),_:2},1032,["onClick"]),h(V,{size:"mini",type:"warning",onClick:xe=>x(B.row)},{default:w(()=>[h(D,null,{default:w(()=>[h(re(Jn))]),_:1})]),_:2},1032,["onClick"]),h(V,{size:"mini",type:"danger",onClick:xe=>$(B.row)},{default:w(()=>[h(D,null,{default:w(()=>[h(re(jn))]),_:1})]),_:2},1032,["onClick"]),h(V,{size:"mini",type:"info",onClick:xe=>b(B.row),disabled:I(B.row)},{default:w(()=>[h(D,null,{default:w(()=>[h(re(Ro))]),_:1})]),_:2},1032,["onClick","disabled"]),h(V,{size:"mini",type:"info",onClick:xe=>_(B.row),disabled:N(B.row)},{default:w(()=>[h(D,null,{default:w(()=>[h(re(Co))]),_:1})]),_:2},1032,["onClick","disabled"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),h(re(ko),{modelValue:a.value,"onUpdate:modelValue":m[6]||(m[6]=B=>a.value=B),title:"",width:"450px","close-on-click-modal":!1},{footer:w(()=>[h(V,{onClick:m[5]||(m[5]=B=>a.value=!1)},{default:w(()=>m[8]||(m[8]=[J("取消")])),_:1,__:[8]}),h(V,{type:"primary",onClick:S},{default:w(()=>m[9]||(m[9]=[J("确定")])),_:1,__:[9]})]),default:w(()=>[h(Se,{model:i,ref_key:"formRef",ref:d,"label-width":"130px"},{default:w(()=>[h(M,{label:"单位名称",prop:"name",required:""},{default:w(()=>[h(ie,{modelValue:i.name,"onUpdate:modelValue":m[1]||(m[1]=B=>i.name=B),required:""},null,8,["modelValue"])]),_:1}),h(M,{label:"组织机构代码",prop:"code"},{default:w(()=>[h(ie,{modelValue:i.code,"onUpdate:modelValue":m[2]||(m[2]=B=>i.code=B)},null,8,["modelValue"])]),_:1}),h(M,{label:"上级单位",prop:"parentId"},{default:w(()=>[h(G,{modelValue:i.parentIdPath,"onUpdate:modelValue":m[3]||(m[3]=B=>i.parentIdPath=B),options:l.value,props:n,onChange:r,style:{width:"100%"},"show-all-levels":!1,placeholder:"请选择上级单位"},null,8,["modelValue","options"])]),_:1}),h(M,{label:"排序",prop:"sort",required:""},{default:w(()=>[h(pe,{modelValue:i.sort_order,"onUpdate:modelValue":m[4]||(m[4]=B=>i.sort_order=B),placeholder:"请选择排序位置"},{default:w(()=>[h(de,{label:"置于 最前",value:0}),(ne(!0),fe(He,null,je(C.value,B=>(ne(),et(de,{key:B.id,label:`置于 ${B.unit_name} 之后`,value:B.sort_order},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Zl={class:"user-management-container"},Jl={class:"left-panel"},jl=["onClick"],ea={class:"right-panel"},ta={class:"action-buttons"},na={class:"form-row"},ra={class:"form-row"},ia={class:"form-row"},oa={class:"form-row"},la={class:"form-row"},aa={class:"form-row"},sa={class:"form-row"},ua={class:"form-row"},ca={class:"dialog-footer"},fa={__name:"UserManagement",setup(e){const t=H([]),n=H([]),r=H(""),i=H([]),o=H([]),l=H(!1),u=he(()=>i.value),f=he(()=>r.value?n.value.filter(b=>b.show&&b.unit_name.toLowerCase().includes(r.value.toLowerCase())):n.value.filter(b=>b.show)),s=ze({name:"",id_number:"",phone:"",archive_birthdate:null,gender:null,short_code:"",alt_phone_1:"",alt_phone_2:"",landline:"",organization_unit:null,work_unit:null,employment_date:null,political_status:"",party_join_date:null,personnel_type:"",police_number:"",is_assisting_officer:null,employment_status:"",job_rank:"",current_rank_date:null,position:"",current_position_date:null,sorted_order:null,remark:""}),a={name:[{required:!0,message:"",trigger:"blur"}],id_number:[{required:!1,message:"",trigger:"blur"},{pattern:/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,message:"请输入正确的身份证号",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],archive_birthdate:[{required:!0,message:"请选择档案出生时间",trigger:"change"}]},c=H(null);at(async()=>{try{const b=await ce.post("api/get_unit_info.php");t.value=[b.data],d(b.data),console.log("获取部门数据成功:",t.value);const _=new FormData;_.append("controlCode","query"),_.append("page",1),_.append("pagesize",100);const C=await ce.post("api/user_manage1.php",_,{headers:{"Content-Type":"multipart/form-data"}});i.value=C.data,console.log("获取用户数据成功:",i.value)}catch(b){console.error("获取数据失败:",b),Q.error("获取数据失败，请稍后重试")}});const d=(b,_=0,C=null)=>{const y={...b,level:_,expanded:b.children&&b.children.length>0,parent:C,indent:_*20,show:!0};n.value.push(y),b.children&&b.children.length>0&&b.children.forEach(m=>{d(m,_+1,y)})},p=b=>{b.expanded=!b.expanded;const _=n.value.indexOf(b)+1;let C=b.level+1;for(let y=_;y<n.value.length;y++){const m=n.value[y];if(m.level<=b.level)break;m.level===C?m.show=b.expanded:m.level>C&&(m.show=b.expanded&&n.value[y-1].show)}},g=async b=>{console.log("点击的部门:",b)},R=()=>{l.value=!0,c.value&&c.value.resetFields()},x=()=>{if(o.value.length===0){Q.warning("请先选择要删除的用户");return}Q.success(`已删除 ${o.value.length} 个用户`),o.value=[]},S=b=>{console.log("编辑用户:",b)},E=b=>{console.log("查看用户详情:",b)},$=b=>{console.log("删除用户:",b)},A=b=>{o.value=b},I=b=>{console.log("点击的部门名称:",b)},N=async()=>{c.value&&await c.value.validate(async b=>{if(b)try{console.log("提交的用户数据:",s),Q.success("模拟新增用户成功"),l.value=!1}catch(_){console.error("新增用户失败:",_),Q.error("新增用户失败，请稍后重试")}})};return(b,_)=>{const C=L("el-input"),y=L("el-button"),m=L("el-table-column"),V=L("el-table"),P=L("el-form-item"),U=L("el-date-picker"),v=L("el-option"),D=L("el-select"),F=L("el-cascader"),W=L("el-form"),ie=L("el-dialog");return ne(),fe("div",Zl,[q("div",Jl,[h(C,{modelValue:r.value,"onUpdate:modelValue":_[0]||(_[0]=M=>r.value=M),placeholder:"搜索单位",class:"department-search"},null,8,["modelValue"]),h(V,{data:f.value,border:"",class:"department-table",onRowClick:g},{default:w(()=>[h(m,{label:"操作",width:"80"},{default:w(({row:M})=>[M.children&&M.children.length>0?(ne(),et(y,{key:0,type:"text",size:"small",onClick:hi(G=>p(M),["stop"])},{default:w(()=>[J(Ve(M.expanded?"-":"+"),1)]),_:2},1032,["onClick"])):Qn("",!0)]),_:1}),h(m,{prop:"unit_name",label:"单位名称"},{default:w(({row:M})=>[q("span",{class:"indent",style:Zn({width:`${M.indent}px`})},null,4),q("span",{onClick:G=>I(M),style:{cursor:"pointer"}},Ve(M.unit_name),9,jl)]),_:1})]),_:1},8,["data"])]),q("div",ea,[q("div",ta,[h(y,{type:"primary",onClick:R},{default:w(()=>_[27]||(_[27]=[J("新增")])),_:1,__:[27]}),h(y,{type:"danger",onClick:x},{default:w(()=>_[28]||(_[28]=[J("删除")])),_:1,__:[28]})]),h(V,{data:u.value,border:"",class:"user-table",onSelectionChange:A},{default:w(()=>[h(m,{type:"selection",width:"55"}),h(m,{prop:"index",label:"序号",width:"80"}),h(m,{prop:"username",label:"用户名"}),h(m,{prop:"account",label:"账号"}),h(m,{prop:"role",label:"角色"}),h(m,{prop:"email",label:"邮箱"}),h(m,{prop:"createTime",label:"创建时间"}),h(m,{label:"操作",width:"200"},{default:w(({row:M})=>[h(y,{type:"primary",onClick:G=>S(M)},{default:w(()=>_[29]||(_[29]=[J("编辑")])),_:2,__:[29]},1032,["onClick"]),h(y,{type:"success",onClick:G=>E(M)},{default:w(()=>_[30]||(_[30]=[J("详情")])),_:2,__:[30]},1032,["onClick"]),h(y,{type:"danger",onClick:G=>$(M)},{default:w(()=>_[31]||(_[31]=[J("删除")])),_:2,__:[31]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),h(ie,{modelValue:l.value,"onUpdate:modelValue":_[26]||(_[26]=M=>l.value=M),title:"",width:"1000px"},{footer:w(()=>[q("span",ca,[h(y,{onClick:_[25]||(_[25]=M=>l.value=!1)},{default:w(()=>_[33]||(_[33]=[J("取消")])),_:1,__:[33]}),h(y,{type:"primary",onClick:N},{default:w(()=>_[34]||(_[34]=[J("确定")])),_:1,__:[34]})])]),default:w(()=>[h(W,{model:s,rules:a,ref_key:"newUserFormRef",ref:c,"label-width":"120px",inline:!1,class:"user-form"},{default:w(()=>[q("div",na,[h(P,{label:"姓名",prop:"name",style:{flex:"1"}},{default:w(()=>[h(C,{modelValue:s.name,"onUpdate:modelValue":_[1]||(_[1]=M=>s.name=M)},null,8,["modelValue"])]),_:1}),h(P,{label:"身份证号",prop:"id_number",style:{flex:"1"}},{default:w(()=>[h(C,{modelValue:s.id_number,"onUpdate:modelValue":_[2]||(_[2]=M=>s.id_number=M),maxlength:"18"},null,8,["modelValue"])]),_:1}),h(P,{label:"手机号码",prop:"phone",style:{flex:"1"}},{default:w(()=>[h(C,{modelValue:s.phone,"onUpdate:modelValue":_[3]||(_[3]=M=>s.phone=M),maxlength:"11"},null,8,["modelValue"])]),_:1})]),q("div",ra,[h(P,{label:"档案出生日期",prop:"archive_birthdate",style:{flex:"1"}},{default:w(()=>[h(U,{modelValue:s.archive_birthdate,"onUpdate:modelValue":_[4]||(_[4]=M=>s.archive_birthdate=M),type:"date"},null,8,["modelValue"])]),_:1}),h(P,{label:"性别",prop:"gender",style:{flex:"1"}},{default:w(()=>[h(D,{modelValue:s.gender,"onUpdate:modelValue":_[5]||(_[5]=M=>s.gender=M)},{default:w(()=>[h(v,{label:"未知",value:"0"}),h(v,{label:"男",value:"1"}),h(v,{label:"女",value:"2"})]),_:1},8,["modelValue"])]),_:1}),h(P,{label:"备注",prop:"remark",style:{flex:"1"}},{default:w(()=>[h(C,{modelValue:s.remark,"onUpdate:modelValue":_[6]||(_[6]=M=>s.remark=M)},null,8,["modelValue"])]),_:1})]),q("div",ia,[h(P,{label:"短号",prop:"short_code",style:{flex:"1"}},{default:w(()=>[h(C,{modelValue:s.short_code,"onUpdate:modelValue":_[7]||(_[7]=M=>s.short_code=M)},null,8,["modelValue"]),_[32]||(_[32]=q("div",{class:""},null,-1))]),_:1,__:[32]}),h(P,{label:"手机号码2",prop:"alt_phone_1",style:{flex:"1"}},{default:w(()=>[h(C,{modelValue:s.alt_phone_1,"onUpdate:modelValue":_[8]||(_[8]=M=>s.alt_phone_1=M)},null,8,["modelValue"])]),_:1}),h(P,{label:"手机号码3",prop:"alt_phone_2",style:{flex:"1"}},{default:w(()=>[h(C,{modelValue:s.alt_phone_2,"onUpdate:modelValue":_[9]||(_[9]=M=>s.alt_phone_2=M)},null,8,["modelValue"])]),_:1})]),q("div",oa,[h(P,{label:"座机",prop:"landline",style:{flex:"1"}},{default:w(()=>[h(C,{modelValue:s.landline,"onUpdate:modelValue":_[10]||(_[10]=M=>s.landline=M)},null,8,["modelValue"])]),_:1}),h(P,{label:"编制单位",prop:"organization_unit",style:{flex:"1"},rules:[{required:!0,message:"请选择编制单位",trigger:"change"}]},{default:w(()=>[h(F,{modelValue:s.organization_unit,"onUpdate:modelValue":_[11]||(_[11]=M=>s.organization_unit=M),options:b.organizationOptions,props:{expandTrigger:"hover"},placeholder:"请选择编制单位"},null,8,["modelValue","options"])]),_:1}),h(P,{label:"工作单位",prop:"work_unit",style:{flex:"1"},rules:[{required:!0,message:"请选择工作单位",trigger:"change"}]},{default:w(()=>[h(F,{modelValue:s.work_unit,"onUpdate:modelValue":_[12]||(_[12]=M=>s.work_unit=M),options:b.workUnitOptions,props:{expandTrigger:"hover"},placeholder:"请选择工作单位"},null,8,["modelValue","options"])]),_:1})]),q("div",la,[h(P,{label:"参工时间",prop:"employment_date",style:{flex:"1"}},{default:w(()=>[h(U,{modelValue:s.employment_date,"onUpdate:modelValue":_[13]||(_[13]=M=>s.employment_date=M),type:"date"},null,8,["modelValue"])]),_:1}),h(P,{label:"政治面貌",prop:"political_status",style:{flex:"1"}},{default:w(()=>[h(D,{modelValue:s.political_status,"onUpdate:modelValue":_[14]||(_[14]=M=>s.political_status=M),placeholder:"请选择政治面貌"},{default:w(()=>[h(v,{label:"中共党员",value:"中共党员"}),h(v,{label:"中共预备党员",value:"中共预备党员"}),h(v,{label:"共青团员",value:"共青团员"}),h(v,{label:"民主党派",value:"民主党派"}),h(v,{label:"无党派人士",value:"无党派人士"}),h(v,{label:"群众",value:"群众"})]),_:1},8,["modelValue"])]),_:1}),h(P,{label:"加入组织时间",prop:"party_join_date",style:{flex:"1"}},{default:w(()=>[h(U,{modelValue:s.party_join_date,"onUpdate:modelValue":_[15]||(_[15]=M=>s.party_join_date=M),type:"date"},null,8,["modelValue"])]),_:1})]),q("div",aa,[h(P,{label:"人员身份",prop:"personnel_type",style:{flex:"1"}},{default:w(()=>[h(D,{modelValue:s.personnel_type,"onUpdate:modelValue":_[16]||(_[16]=M=>s.personnel_type=M)},{default:w(()=>[h(v,{label:"民警",value:"民警"}),h(v,{label:"职工",value:"职工"}),h(v,{label:"辅警",value:"辅警"}),h(v,{label:"机关工勤",value:"机关工勤"}),h(v,{label:"其他",value:"其他"})]),_:1},8,["modelValue"])]),_:1}),h(P,{label:"警号/辅警号",prop:"police_number",style:{flex:"1"}},{default:w(()=>[h(C,{modelValue:s.police_number,"onUpdate:modelValue":_[17]||(_[17]=M=>s.police_number=M)},null,8,["modelValue"])]),_:1}),h(P,{label:"带辅民警",prop:"is_assisting_officer",style:{flex:"1"}},{default:w(()=>[h(D,{modelValue:s.is_assisting_officer,"onUpdate:modelValue":_[18]||(_[18]=M=>s.is_assisting_officer=M)},{default:w(()=>[h(v,{label:"否",value:"0"}),h(v,{label:"是",value:"1"})]),_:1},8,["modelValue"])]),_:1})]),q("div",sa,[h(P,{label:"人员状态",prop:"employment_status",style:{flex:"1"}},{default:w(()=>[h(D,{modelValue:s.employment_status,"onUpdate:modelValue":_[19]||(_[19]=M=>s.employment_status=M)},{default:w(()=>[h(v,{label:"在职",value:"在职"}),h(v,{label:"调离",value:"调离"}),h(v,{label:"退休",value:"退休"}),h(v,{label:"开除",value:"开除"}),h(v,{label:"借调出局",value:"借调出局"})]),_:1},8,["modelValue"])]),_:1}),h(P,{label:"职级",prop:"job_rank",style:{flex:"1"}},{default:w(()=>[h(D,{modelValue:s.job_rank,"onUpdate:modelValue":_[20]||(_[20]=M=>s.job_rank=M)},{default:w(()=>[h(v,{label:"初级",value:"初级"}),h(v,{label:"中级",value:"中级"}),h(v,{label:"高级",value:"高级"})]),_:1},8,["modelValue"])]),_:1}),h(P,{label:"任现职级时间",prop:"current_rank_date",style:{flex:"1"}},{default:w(()=>[h(U,{modelValue:s.current_rank_date,"onUpdate:modelValue":_[21]||(_[21]=M=>s.current_rank_date=M),type:"date"},null,8,["modelValue"])]),_:1})]),q("div",ua,[h(P,{label:"职务",prop:"position",style:{flex:"1"}},{default:w(()=>[h(C,{modelValue:s.position,"onUpdate:modelValue":_[22]||(_[22]=M=>s.position=M)},null,8,["modelValue"])]),_:1}),h(P,{label:"任现职务时间",prop:"current_position_date",style:{flex:"1"}},{default:w(()=>[h(U,{modelValue:s.current_position_date,"onUpdate:modelValue":_[23]||(_[23]=M=>s.current_position_date=M),type:"date"},null,8,["modelValue"])]),_:1}),h(P,{label:"人员排序",prop:"sorted_order",style:{flex:"1"}},{default:w(()=>[h(C,{modelValue:s.sorted_order,"onUpdate:modelValue":_[24]||(_[24]=M=>s.sorted_order=M),modelModifiers:{number:!0},type:"number"},null,8,["modelValue"])]),_:1})])]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},da=an(fa,[["__scopeId","data-v-cf0c049a"]]);class Be{constructor(){this._partials=new Float64Array(32),this._n=0}add(t){const n=this._partials;let r=0;for(let i=0;i<this._n&&i<32;i++){const o=n[i],l=t+o,u=Math.abs(t)<Math.abs(o)?t-(l-o):o-(l-t);u&&(n[r++]=u),t=l}return n[r]=t,this._n=r+1,this}valueOf(){const t=this._partials;let n=this._n,r,i,o,l=0;if(n>0){for(l=t[--n];n>0&&(r=l,i=t[--n],l=r+i,o=i-(l-r),!o););n>0&&(o<0&&t[n-1]<0||o>0&&t[n-1]>0)&&(i=o*2,r=l+i,i==r-l&&(l=r))}return l}}function*pa(e){for(const t of e)yield*t}function Pi(e){return Array.from(pa(e))}var ha={value:()=>{}};function Vi(){for(var e=0,t=arguments.length,n={},r;e<t;++e){if(!(r=arguments[e]+"")||r in n||/[\s.]/.test(r))throw new Error("illegal type: "+r);n[r]=[]}return new qt(n)}function qt(e){this._=e}function ma(e,t){return e.trim().split(/^|\s+/).map(function(n){var r="",i=n.indexOf(".");if(i>=0&&(r=n.slice(i+1),n=n.slice(0,i)),n&&!t.hasOwnProperty(n))throw new Error("unknown type: "+n);return{type:n,name:r}})}qt.prototype=Vi.prototype={constructor:qt,on:function(e,t){var n=this._,r=ma(e+"",n),i,o=-1,l=r.length;if(arguments.length<2){for(;++o<l;)if((i=(e=r[o]).type)&&(i=_a(n[i],e.name)))return i;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++o<l;)if(i=(e=r[o]).type)n[i]=Vr(n[i],e.name,t);else if(t==null)for(i in n)n[i]=Vr(n[i],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new qt(e)},call:function(e,t){if((i=arguments.length-2)>0)for(var n=new Array(i),r=0,i,o;r<i;++r)n[r]=arguments[r+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(o=this._[e],r=0,i=o.length;r<i;++r)o[r].value.apply(t,n)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var r=this._[e],i=0,o=r.length;i<o;++i)r[i].value.apply(t,n)}};function _a(e,t){for(var n=0,r=e.length,i;n<r;++n)if((i=e[n]).name===t)return i.value}function Vr(e,t,n){for(var r=0,i=e.length;r<i;++r)if(e[r].name===t){e[r]=ha,e=e.slice(0,r).concat(e.slice(r+1));break}return n!=null&&e.push({name:t,value:n}),e}var Cn="http://www.w3.org/1999/xhtml";const Mr={svg:"http://www.w3.org/2000/svg",xhtml:Cn,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function cn(e){var t=e+="",n=t.indexOf(":");return n>=0&&(t=e.slice(0,n))!=="xmlns"&&(e=e.slice(n+1)),Mr.hasOwnProperty(t)?{space:Mr[t],local:e}:e}function ga(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===Cn&&t.documentElement.namespaceURI===Cn?t.createElement(e):t.createElementNS(n,e)}}function va(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function Mi(e){var t=cn(e);return(t.local?va:ga)(t)}function ya(){}function nr(e){return e==null?ya:function(){return this.querySelector(e)}}function wa(e){typeof e!="function"&&(e=nr(e));for(var t=this._groups,n=t.length,r=new Array(n),i=0;i<n;++i)for(var o=t[i],l=o.length,u=r[i]=new Array(l),f,s,a=0;a<l;++a)(f=o[a])&&(s=e.call(f,f.__data__,a,o))&&("__data__"in f&&(s.__data__=f.__data__),u[a]=s);return new ye(r,this._parents)}function xa(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function ba(){return[]}function Ai(e){return e==null?ba:function(){return this.querySelectorAll(e)}}function Ea(e){return function(){return xa(e.apply(this,arguments))}}function Sa(e){typeof e=="function"?e=Ea(e):e=Ai(e);for(var t=this._groups,n=t.length,r=[],i=[],o=0;o<n;++o)for(var l=t[o],u=l.length,f,s=0;s<u;++s)(f=l[s])&&(r.push(e.call(f,f.__data__,s,l)),i.push(f));return new ye(r,i)}function Ni(e){return function(){return this.matches(e)}}function Ii(e){return function(t){return t.matches(e)}}var ka=Array.prototype.find;function Ra(e){return function(){return ka.call(this.children,e)}}function Ca(){return this.firstElementChild}function $a(e){return this.select(e==null?Ca:Ra(typeof e=="function"?e:Ii(e)))}var Pa=Array.prototype.filter;function Va(){return Array.from(this.children)}function Ma(e){return function(){return Pa.call(this.children,e)}}function Aa(e){return this.selectAll(e==null?Va:Ma(typeof e=="function"?e:Ii(e)))}function Na(e){typeof e!="function"&&(e=Ni(e));for(var t=this._groups,n=t.length,r=new Array(n),i=0;i<n;++i)for(var o=t[i],l=o.length,u=r[i]=[],f,s=0;s<l;++s)(f=o[s])&&e.call(f,f.__data__,s,o)&&u.push(f);return new ye(r,this._parents)}function Ti(e){return new Array(e.length)}function Ia(){return new ye(this._enter||this._groups.map(Ti),this._parents)}function Gt(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}Gt.prototype={constructor:Gt,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function Ta(e){return function(){return e}}function Ua(e,t,n,r,i,o){for(var l=0,u,f=t.length,s=o.length;l<s;++l)(u=t[l])?(u.__data__=o[l],r[l]=u):n[l]=new Gt(e,o[l]);for(;l<f;++l)(u=t[l])&&(i[l]=u)}function Oa(e,t,n,r,i,o,l){var u,f,s=new Map,a=t.length,c=o.length,d=new Array(a),p;for(u=0;u<a;++u)(f=t[u])&&(d[u]=p=l.call(f,f.__data__,u,t)+"",s.has(p)?i[u]=f:s.set(p,f));for(u=0;u<c;++u)p=l.call(e,o[u],u,o)+"",(f=s.get(p))?(r[u]=f,f.__data__=o[u],s.delete(p)):n[u]=new Gt(e,o[u]);for(u=0;u<a;++u)(f=t[u])&&s.get(d[u])===f&&(i[u]=f)}function Da(e){return e.__data__}function La(e,t){if(!arguments.length)return Array.from(this,Da);var n=t?Oa:Ua,r=this._parents,i=this._groups;typeof e!="function"&&(e=Ta(e));for(var o=i.length,l=new Array(o),u=new Array(o),f=new Array(o),s=0;s<o;++s){var a=r[s],c=i[s],d=c.length,p=Fa(e.call(a,a&&a.__data__,s,r)),g=p.length,R=u[s]=new Array(g),x=l[s]=new Array(g),S=f[s]=new Array(d);n(a,c,R,x,S,p,t);for(var E=0,$=0,A,I;E<g;++E)if(A=R[E]){for(E>=$&&($=E+1);!(I=x[$])&&++$<g;);A._next=I||null}}return l=new ye(l,r),l._enter=u,l._exit=f,l}function Fa(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function qa(){return new ye(this._exit||this._groups.map(Ti),this._parents)}function za(e,t,n){var r=this.enter(),i=this,o=this.exit();return typeof e=="function"?(r=e(r),r&&(r=r.selection())):r=r.append(e+""),t!=null&&(i=t(i),i&&(i=i.selection())),n==null?o.remove():n(o),r&&i?r.merge(i).order():i}function Ha(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,i=n.length,o=r.length,l=Math.min(i,o),u=new Array(i),f=0;f<l;++f)for(var s=n[f],a=r[f],c=s.length,d=u[f]=new Array(c),p,g=0;g<c;++g)(p=s[g]||a[g])&&(d[g]=p);for(;f<i;++f)u[f]=n[f];return new ye(u,this._parents)}function Ba(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r=e[t],i=r.length-1,o=r[i],l;--i>=0;)(l=r[i])&&(o&&l.compareDocumentPosition(o)^4&&o.parentNode.insertBefore(l,o),o=l);return this}function Xa(e){e||(e=Ga);function t(c,d){return c&&d?e(c.__data__,d.__data__):!c-!d}for(var n=this._groups,r=n.length,i=new Array(r),o=0;o<r;++o){for(var l=n[o],u=l.length,f=i[o]=new Array(u),s,a=0;a<u;++a)(s=l[a])&&(f[a]=s);f.sort(t)}return new ye(i,this._parents).order()}function Ga(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function Ya(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function Ka(){return Array.from(this)}function Wa(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],i=0,o=r.length;i<o;++i){var l=r[i];if(l)return l}return null}function Qa(){let e=0;for(const t of this)++e;return e}function Za(){return!this.node()}function Ja(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var i=t[n],o=0,l=i.length,u;o<l;++o)(u=i[o])&&e.call(u,u.__data__,o,i);return this}function ja(e){return function(){this.removeAttribute(e)}}function es(e){return function(){this.removeAttributeNS(e.space,e.local)}}function ts(e,t){return function(){this.setAttribute(e,t)}}function ns(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function rs(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttribute(e):this.setAttribute(e,n)}}function is(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function os(e,t){var n=cn(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((t==null?n.local?es:ja:typeof t=="function"?n.local?is:rs:n.local?ns:ts)(n,t))}function Ui(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function ls(e){return function(){this.style.removeProperty(e)}}function as(e,t,n){return function(){this.style.setProperty(e,t,n)}}function ss(e,t,n){return function(){var r=t.apply(this,arguments);r==null?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}function us(e,t,n){return arguments.length>1?this.each((t==null?ls:typeof t=="function"?ss:as)(e,t,n??"")):rt(this.node(),e)}function rt(e,t){return e.style.getPropertyValue(t)||Ui(e).getComputedStyle(e,null).getPropertyValue(t)}function cs(e){return function(){delete this[e]}}function fs(e,t){return function(){this[e]=t}}function ds(e,t){return function(){var n=t.apply(this,arguments);n==null?delete this[e]:this[e]=n}}function ps(e,t){return arguments.length>1?this.each((t==null?cs:typeof t=="function"?ds:fs)(e,t)):this.node()[e]}function Oi(e){return e.trim().split(/^|\s+/)}function rr(e){return e.classList||new Di(e)}function Di(e){this._node=e,this._names=Oi(e.getAttribute("class")||"")}Di.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function Li(e,t){for(var n=rr(e),r=-1,i=t.length;++r<i;)n.add(t[r])}function Fi(e,t){for(var n=rr(e),r=-1,i=t.length;++r<i;)n.remove(t[r])}function hs(e){return function(){Li(this,e)}}function ms(e){return function(){Fi(this,e)}}function _s(e,t){return function(){(t.apply(this,arguments)?Li:Fi)(this,e)}}function gs(e,t){var n=Oi(e+"");if(arguments.length<2){for(var r=rr(this.node()),i=-1,o=n.length;++i<o;)if(!r.contains(n[i]))return!1;return!0}return this.each((typeof t=="function"?_s:t?hs:ms)(n,t))}function vs(){this.textContent=""}function ys(e){return function(){this.textContent=e}}function ws(e){return function(){var t=e.apply(this,arguments);this.textContent=t??""}}function xs(e){return arguments.length?this.each(e==null?vs:(typeof e=="function"?ws:ys)(e)):this.node().textContent}function bs(){this.innerHTML=""}function Es(e){return function(){this.innerHTML=e}}function Ss(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t??""}}function ks(e){return arguments.length?this.each(e==null?bs:(typeof e=="function"?Ss:Es)(e)):this.node().innerHTML}function Rs(){this.nextSibling&&this.parentNode.appendChild(this)}function Cs(){return this.each(Rs)}function $s(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Ps(){return this.each($s)}function Vs(e){var t=typeof e=="function"?e:Mi(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function Ms(){return null}function As(e,t){var n=typeof e=="function"?e:Mi(e),r=t==null?Ms:typeof t=="function"?t:nr(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})}function Ns(){var e=this.parentNode;e&&e.removeChild(this)}function Is(){return this.each(Ns)}function Ts(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Us(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Os(e){return this.select(e?Us:Ts)}function Ds(e){return arguments.length?this.property("__data__",e):this.node().__data__}function Ls(e){return function(t){e.call(this,t,this.__data__)}}function Fs(e){return e.trim().split(/^|\s+/).map(function(t){var n="",r=t.indexOf(".");return r>=0&&(n=t.slice(r+1),t=t.slice(0,r)),{type:t,name:n}})}function qs(e){return function(){var t=this.__on;if(t){for(var n=0,r=-1,i=t.length,o;n<i;++n)o=t[n],(!e.type||o.type===e.type)&&o.name===e.name?this.removeEventListener(o.type,o.listener,o.options):t[++r]=o;++r?t.length=r:delete this.__on}}}function zs(e,t,n){return function(){var r=this.__on,i,o=Ls(t);if(r){for(var l=0,u=r.length;l<u;++l)if((i=r[l]).type===e.type&&i.name===e.name){this.removeEventListener(i.type,i.listener,i.options),this.addEventListener(i.type,i.listener=o,i.options=n),i.value=t;return}}this.addEventListener(e.type,o,n),i={type:e.type,name:e.name,value:t,listener:o,options:n},r?r.push(i):this.__on=[i]}}function Hs(e,t,n){var r=Fs(e+""),i,o=r.length,l;if(arguments.length<2){var u=this.node().__on;if(u){for(var f=0,s=u.length,a;f<s;++f)for(i=0,a=u[f];i<o;++i)if((l=r[i]).type===a.type&&l.name===a.name)return a.value}return}for(u=t?zs:qs,i=0;i<o;++i)this.each(u(r[i],t,n));return this}function qi(e,t,n){var r=Ui(e),i=r.CustomEvent;typeof i=="function"?i=new i(t,n):(i=r.document.createEvent("Event"),n?(i.initEvent(t,n.bubbles,n.cancelable),i.detail=n.detail):i.initEvent(t,!1,!1)),e.dispatchEvent(i)}function Bs(e,t){return function(){return qi(this,e,t)}}function Xs(e,t){return function(){return qi(this,e,t.apply(this,arguments))}}function Gs(e,t){return this.each((typeof t=="function"?Xs:Bs)(e,t))}function*Ys(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],i=0,o=r.length,l;i<o;++i)(l=r[i])&&(yield l)}var zi=[null];function ye(e,t){this._groups=e,this._parents=t}function Vt(){return new ye([[document.documentElement]],zi)}function Ks(){return this}ye.prototype=Vt.prototype={constructor:ye,select:wa,selectAll:Sa,selectChild:$a,selectChildren:Aa,filter:Na,data:La,enter:Ia,exit:qa,join:za,merge:Ha,selection:Ks,order:Ba,sort:Xa,call:Ya,nodes:Ka,node:Wa,size:Qa,empty:Za,each:Ja,attr:os,style:us,property:ps,classed:gs,text:xs,html:ks,raise:Cs,lower:Ps,append:Vs,insert:As,remove:Is,clone:Os,datum:Ds,on:Hs,dispatch:Gs,[Symbol.iterator]:Ys};function At(e){return typeof e=="string"?new ye([[document.querySelector(e)]],[document.documentElement]):new ye([[e]],zi)}function ir(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function Hi(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function Mt(){}var bt=.7,Yt=1/bt,Ze="\\s*([+-]?\\d+)\\s*",Et="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Me="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Ws=/^#([0-9a-f]{3,8})$/,Qs=new RegExp(`^rgb\\(${Ze},${Ze},${Ze}\\)$`),Zs=new RegExp(`^rgb\\(${Me},${Me},${Me}\\)$`),Js=new RegExp(`^rgba\\(${Ze},${Ze},${Ze},${Et}\\)$`),js=new RegExp(`^rgba\\(${Me},${Me},${Me},${Et}\\)$`),eu=new RegExp(`^hsl\\(${Et},${Me},${Me}\\)$`),tu=new RegExp(`^hsla\\(${Et},${Me},${Me},${Et}\\)$`),Ar={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};ir(Mt,St,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Nr,formatHex:Nr,formatHex8:nu,formatHsl:ru,formatRgb:Ir,toString:Ir});function Nr(){return this.rgb().formatHex()}function nu(){return this.rgb().formatHex8()}function ru(){return Bi(this).formatHsl()}function Ir(){return this.rgb().formatRgb()}function St(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=Ws.exec(e))?(n=t[1].length,t=parseInt(t[1],16),n===6?Tr(t):n===3?new _e(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):n===8?Nt(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):n===4?Nt(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=Qs.exec(e))?new _e(t[1],t[2],t[3],1):(t=Zs.exec(e))?new _e(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=Js.exec(e))?Nt(t[1],t[2],t[3],t[4]):(t=js.exec(e))?Nt(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=eu.exec(e))?Dr(t[1],t[2]/100,t[3]/100,1):(t=tu.exec(e))?Dr(t[1],t[2]/100,t[3]/100,t[4]):Ar.hasOwnProperty(e)?Tr(Ar[e]):e==="transparent"?new _e(NaN,NaN,NaN,0):null}function Tr(e){return new _e(e>>16&255,e>>8&255,e&255,1)}function Nt(e,t,n,r){return r<=0&&(e=t=n=NaN),new _e(e,t,n,r)}function iu(e){return e instanceof Mt||(e=St(e)),e?(e=e.rgb(),new _e(e.r,e.g,e.b,e.opacity)):new _e}function $n(e,t,n,r){return arguments.length===1?iu(e):new _e(e,t,n,r??1)}function _e(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}ir(_e,$n,Hi(Mt,{brighter(e){return e=e==null?Yt:Math.pow(Yt,e),new _e(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?bt:Math.pow(bt,e),new _e(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new _e(qe(this.r),qe(this.g),qe(this.b),Kt(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Ur,formatHex:Ur,formatHex8:ou,formatRgb:Or,toString:Or}));function Ur(){return`#${Fe(this.r)}${Fe(this.g)}${Fe(this.b)}`}function ou(){return`#${Fe(this.r)}${Fe(this.g)}${Fe(this.b)}${Fe((isNaN(this.opacity)?1:this.opacity)*255)}`}function Or(){const e=Kt(this.opacity);return`${e===1?"rgb(":"rgba("}${qe(this.r)}, ${qe(this.g)}, ${qe(this.b)}${e===1?")":`, ${e})`}`}function Kt(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function qe(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Fe(e){return e=qe(e),(e<16?"0":"")+e.toString(16)}function Dr(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new ke(e,t,n,r)}function Bi(e){if(e instanceof ke)return new ke(e.h,e.s,e.l,e.opacity);if(e instanceof Mt||(e=St(e)),!e)return new ke;if(e instanceof ke)return e;e=e.rgb();var t=e.r/255,n=e.g/255,r=e.b/255,i=Math.min(t,n,r),o=Math.max(t,n,r),l=NaN,u=o-i,f=(o+i)/2;return u?(t===o?l=(n-r)/u+(n<r)*6:n===o?l=(r-t)/u+2:l=(t-n)/u+4,u/=f<.5?o+i:2-o-i,l*=60):u=f>0&&f<1?0:l,new ke(l,u,f,e.opacity)}function lu(e,t,n,r){return arguments.length===1?Bi(e):new ke(e,t,n,r??1)}function ke(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}ir(ke,lu,Hi(Mt,{brighter(e){return e=e==null?Yt:Math.pow(Yt,e),new ke(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?bt:Math.pow(bt,e),new ke(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,i=2*n-r;return new _e(vn(e>=240?e-240:e+120,i,r),vn(e,i,r),vn(e<120?e+240:e-120,i,r),this.opacity)},clamp(){return new ke(Lr(this.h),It(this.s),It(this.l),Kt(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Kt(this.opacity);return`${e===1?"hsl(":"hsla("}${Lr(this.h)}, ${It(this.s)*100}%, ${It(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Lr(e){return e=(e||0)%360,e<0?e+360:e}function It(e){return Math.max(0,Math.min(1,e||0))}function vn(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}const Xi=e=>()=>e;function au(e,t){return function(n){return e+n*t}}function su(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(r){return Math.pow(e+r*t,n)}}function uu(e){return(e=+e)==1?Gi:function(t,n){return n-t?su(t,n,e):Xi(isNaN(t)?n:t)}}function Gi(e,t){var n=t-e;return n?au(e,n):Xi(isNaN(e)?t:e)}const Fr=function e(t){var n=uu(t);function r(i,o){var l=n((i=$n(i)).r,(o=$n(o)).r),u=n(i.g,o.g),f=n(i.b,o.b),s=Gi(i.opacity,o.opacity);return function(a){return i.r=l(a),i.g=u(a),i.b=f(a),i.opacity=s(a),i+""}}return r.gamma=e,r}(1);function Le(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var Pn=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,yn=new RegExp(Pn.source,"g");function cu(e){return function(){return e}}function fu(e){return function(t){return e(t)+""}}function du(e,t){var n=Pn.lastIndex=yn.lastIndex=0,r,i,o,l=-1,u=[],f=[];for(e=e+"",t=t+"";(r=Pn.exec(e))&&(i=yn.exec(t));)(o=i.index)>n&&(o=t.slice(n,o),u[l]?u[l]+=o:u[++l]=o),(r=r[0])===(i=i[0])?u[l]?u[l]+=i:u[++l]=i:(u[++l]=null,f.push({i:l,x:Le(r,i)})),n=yn.lastIndex;return n<t.length&&(o=t.slice(n),u[l]?u[l]+=o:u[++l]=o),u.length<2?f[0]?fu(f[0].x):cu(t):(t=f.length,function(s){for(var a=0,c;a<t;++a)u[(c=f[a]).i]=c.x(s);return u.join("")})}var qr=180/Math.PI,Vn={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Yi(e,t,n,r,i,o){var l,u,f;return(l=Math.sqrt(e*e+t*t))&&(e/=l,t/=l),(f=e*n+t*r)&&(n-=e*f,r-=t*f),(u=Math.sqrt(n*n+r*r))&&(n/=u,r/=u,f/=u),e*r<t*n&&(e=-e,t=-t,f=-f,l=-l),{translateX:i,translateY:o,rotate:Math.atan2(t,e)*qr,skewX:Math.atan(f)*qr,scaleX:l,scaleY:u}}var Tt;function pu(e){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?Vn:Yi(t.a,t.b,t.c,t.d,t.e,t.f)}function hu(e){return e==null||(Tt||(Tt=document.createElementNS("http://www.w3.org/2000/svg","g")),Tt.setAttribute("transform",e),!(e=Tt.transform.baseVal.consolidate()))?Vn:(e=e.matrix,Yi(e.a,e.b,e.c,e.d,e.e,e.f))}function Ki(e,t,n,r){function i(s){return s.length?s.pop()+" ":""}function o(s,a,c,d,p,g){if(s!==c||a!==d){var R=p.push("translate(",null,t,null,n);g.push({i:R-4,x:Le(s,c)},{i:R-2,x:Le(a,d)})}else(c||d)&&p.push("translate("+c+t+d+n)}function l(s,a,c,d){s!==a?(s-a>180?a+=360:a-s>180&&(s+=360),d.push({i:c.push(i(c)+"rotate(",null,r)-2,x:Le(s,a)})):a&&c.push(i(c)+"rotate("+a+r)}function u(s,a,c,d){s!==a?d.push({i:c.push(i(c)+"skewX(",null,r)-2,x:Le(s,a)}):a&&c.push(i(c)+"skewX("+a+r)}function f(s,a,c,d,p,g){if(s!==c||a!==d){var R=p.push(i(p)+"scale(",null,",",null,")");g.push({i:R-4,x:Le(s,c)},{i:R-2,x:Le(a,d)})}else(c!==1||d!==1)&&p.push(i(p)+"scale("+c+","+d+")")}return function(s,a){var c=[],d=[];return s=e(s),a=e(a),o(s.translateX,s.translateY,a.translateX,a.translateY,c,d),l(s.rotate,a.rotate,c,d),u(s.skewX,a.skewX,c,d),f(s.scaleX,s.scaleY,a.scaleX,a.scaleY,c,d),s=a=null,function(p){for(var g=-1,R=d.length,x;++g<R;)c[(x=d[g]).i]=x.x(p);return c.join("")}}}var mu=Ki(pu,"px, ","px)","deg)"),_u=Ki(hu,", ",")",")"),it=0,ft=0,ct=0,Wi=1e3,Wt,dt,Qt=0,Xe=0,fn=0,kt=typeof performance=="object"&&performance.now?performance:Date,Qi=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function or(){return Xe||(Qi(gu),Xe=kt.now()+fn)}function gu(){Xe=0}function Zt(){this._call=this._time=this._next=null}Zt.prototype=Zi.prototype={constructor:Zt,restart:function(e,t,n){if(typeof e!="function")throw new TypeError("callback is not a function");n=(n==null?or():+n)+(t==null?0:+t),!this._next&&dt!==this&&(dt?dt._next=this:Wt=this,dt=this),this._call=e,this._time=n,Mn()},stop:function(){this._call&&(this._call=null,this._time=1/0,Mn())}};function Zi(e,t,n){var r=new Zt;return r.restart(e,t,n),r}function vu(){or(),++it;for(var e=Wt,t;e;)(t=Xe-e._time)>=0&&e._call.call(void 0,t),e=e._next;--it}function zr(){Xe=(Qt=kt.now())+fn,it=ft=0;try{vu()}finally{it=0,wu(),Xe=0}}function yu(){var e=kt.now(),t=e-Qt;t>Wi&&(fn-=t,Qt=e)}function wu(){for(var e,t=Wt,n,r=1/0;t;)t._call?(r>t._time&&(r=t._time),e=t,t=t._next):(n=t._next,t._next=null,t=e?e._next=n:Wt=n);dt=e,Mn(r)}function Mn(e){if(!it){ft&&(ft=clearTimeout(ft));var t=e-Xe;t>24?(e<1/0&&(ft=setTimeout(zr,e-kt.now()-fn)),ct&&(ct=clearInterval(ct))):(ct||(Qt=kt.now(),ct=setInterval(yu,Wi)),it=1,Qi(zr))}}function Hr(e,t,n){var r=new Zt;return t=t==null?0:+t,r.restart(i=>{r.stop(),e(i+t)},t,n),r}var xu=Vi("start","end","cancel","interrupt"),bu=[],Ji=0,Br=1,An=2,zt=3,Xr=4,Nn=5,Ht=6;function dn(e,t,n,r,i,o){var l=e.__transition;if(!l)e.__transition={};else if(n in l)return;Eu(e,n,{name:t,index:r,group:i,on:xu,tween:bu,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:Ji})}function lr(e,t){var n=Ce(e,t);if(n.state>Ji)throw new Error("too late; already scheduled");return n}function Ae(e,t){var n=Ce(e,t);if(n.state>zt)throw new Error("too late; already running");return n}function Ce(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function Eu(e,t,n){var r=e.__transition,i;r[t]=n,n.timer=Zi(o,0,n.time);function o(s){n.state=Br,n.timer.restart(l,n.delay,n.time),n.delay<=s&&l(s-n.delay)}function l(s){var a,c,d,p;if(n.state!==Br)return f();for(a in r)if(p=r[a],p.name===n.name){if(p.state===zt)return Hr(l);p.state===Xr?(p.state=Ht,p.timer.stop(),p.on.call("interrupt",e,e.__data__,p.index,p.group),delete r[a]):+a<t&&(p.state=Ht,p.timer.stop(),p.on.call("cancel",e,e.__data__,p.index,p.group),delete r[a])}if(Hr(function(){n.state===zt&&(n.state=Xr,n.timer.restart(u,n.delay,n.time),u(s))}),n.state=An,n.on.call("start",e,e.__data__,n.index,n.group),n.state===An){for(n.state=zt,i=new Array(d=n.tween.length),a=0,c=-1;a<d;++a)(p=n.tween[a].value.call(e,e.__data__,n.index,n.group))&&(i[++c]=p);i.length=c+1}}function u(s){for(var a=s<n.duration?n.ease.call(null,s/n.duration):(n.timer.restart(f),n.state=Nn,1),c=-1,d=i.length;++c<d;)i[c].call(e,a);n.state===Nn&&(n.on.call("end",e,e.__data__,n.index,n.group),f())}function f(){n.state=Ht,n.timer.stop(),delete r[t];for(var s in r)return;delete e.__transition}}function Su(e,t){var n=e.__transition,r,i,o=!0,l;if(n){t=t==null?null:t+"";for(l in n){if((r=n[l]).name!==t){o=!1;continue}i=r.state>An&&r.state<Nn,r.state=Ht,r.timer.stop(),r.on.call(i?"interrupt":"cancel",e,e.__data__,r.index,r.group),delete n[l]}o&&delete e.__transition}}function ku(e){return this.each(function(){Su(this,e)})}function Ru(e,t){var n,r;return function(){var i=Ae(this,e),o=i.tween;if(o!==n){r=n=o;for(var l=0,u=r.length;l<u;++l)if(r[l].name===t){r=r.slice(),r.splice(l,1);break}}i.tween=r}}function Cu(e,t,n){var r,i;if(typeof n!="function")throw new Error;return function(){var o=Ae(this,e),l=o.tween;if(l!==r){i=(r=l).slice();for(var u={name:t,value:n},f=0,s=i.length;f<s;++f)if(i[f].name===t){i[f]=u;break}f===s&&i.push(u)}o.tween=i}}function $u(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r=Ce(this.node(),n).tween,i=0,o=r.length,l;i<o;++i)if((l=r[i]).name===e)return l.value;return null}return this.each((t==null?Ru:Cu)(n,e,t))}function ar(e,t,n){var r=e._id;return e.each(function(){var i=Ae(this,r);(i.value||(i.value={}))[t]=n.apply(this,arguments)}),function(i){return Ce(i,r).value[t]}}function ji(e,t){var n;return(typeof t=="number"?Le:t instanceof St?Fr:(n=St(t))?(t=n,Fr):du)(e,t)}function Pu(e){return function(){this.removeAttribute(e)}}function Vu(e){return function(){this.removeAttributeNS(e.space,e.local)}}function Mu(e,t,n){var r,i=n+"",o;return function(){var l=this.getAttribute(e);return l===i?null:l===r?o:o=t(r=l,n)}}function Au(e,t,n){var r,i=n+"",o;return function(){var l=this.getAttributeNS(e.space,e.local);return l===i?null:l===r?o:o=t(r=l,n)}}function Nu(e,t,n){var r,i,o;return function(){var l,u=n(this),f;return u==null?void this.removeAttribute(e):(l=this.getAttribute(e),f=u+"",l===f?null:l===r&&f===i?o:(i=f,o=t(r=l,u)))}}function Iu(e,t,n){var r,i,o;return function(){var l,u=n(this),f;return u==null?void this.removeAttributeNS(e.space,e.local):(l=this.getAttributeNS(e.space,e.local),f=u+"",l===f?null:l===r&&f===i?o:(i=f,o=t(r=l,u)))}}function Tu(e,t){var n=cn(e),r=n==="transform"?_u:ji;return this.attrTween(e,typeof t=="function"?(n.local?Iu:Nu)(n,r,ar(this,"attr."+e,t)):t==null?(n.local?Vu:Pu)(n):(n.local?Au:Mu)(n,r,t))}function Uu(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function Ou(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function Du(e,t){var n,r;function i(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&Ou(e,o)),n}return i._value=t,i}function Lu(e,t){var n,r;function i(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&Uu(e,o)),n}return i._value=t,i}function Fu(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!="function")throw new Error;var r=cn(e);return this.tween(n,(r.local?Du:Lu)(r,t))}function qu(e,t){return function(){lr(this,e).delay=+t.apply(this,arguments)}}function zu(e,t){return t=+t,function(){lr(this,e).delay=t}}function Hu(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?qu:zu)(t,e)):Ce(this.node(),t).delay}function Bu(e,t){return function(){Ae(this,e).duration=+t.apply(this,arguments)}}function Xu(e,t){return t=+t,function(){Ae(this,e).duration=t}}function Gu(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?Bu:Xu)(t,e)):Ce(this.node(),t).duration}function Yu(e,t){if(typeof t!="function")throw new Error;return function(){Ae(this,e).ease=t}}function Ku(e){var t=this._id;return arguments.length?this.each(Yu(t,e)):Ce(this.node(),t).ease}function Wu(e,t){return function(){var n=t.apply(this,arguments);if(typeof n!="function")throw new Error;Ae(this,e).ease=n}}function Qu(e){if(typeof e!="function")throw new Error;return this.each(Wu(this._id,e))}function Zu(e){typeof e!="function"&&(e=Ni(e));for(var t=this._groups,n=t.length,r=new Array(n),i=0;i<n;++i)for(var o=t[i],l=o.length,u=r[i]=[],f,s=0;s<l;++s)(f=o[s])&&e.call(f,f.__data__,s,o)&&u.push(f);return new Ue(r,this._parents,this._name,this._id)}function Ju(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,r=t.length,i=n.length,o=Math.min(r,i),l=new Array(r),u=0;u<o;++u)for(var f=t[u],s=n[u],a=f.length,c=l[u]=new Array(a),d,p=0;p<a;++p)(d=f[p]||s[p])&&(c[p]=d);for(;u<r;++u)l[u]=t[u];return new Ue(l,this._parents,this._name,this._id)}function ju(e){return(e+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||t==="start"})}function ec(e,t,n){var r,i,o=ju(t)?lr:Ae;return function(){var l=o(this,e),u=l.on;u!==r&&(i=(r=u).copy()).on(t,n),l.on=i}}function tc(e,t){var n=this._id;return arguments.length<2?Ce(this.node(),n).on.on(e):this.each(ec(n,e,t))}function nc(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}function rc(){return this.on("end.remove",nc(this._id))}function ic(e){var t=this._name,n=this._id;typeof e!="function"&&(e=nr(e));for(var r=this._groups,i=r.length,o=new Array(i),l=0;l<i;++l)for(var u=r[l],f=u.length,s=o[l]=new Array(f),a,c,d=0;d<f;++d)(a=u[d])&&(c=e.call(a,a.__data__,d,u))&&("__data__"in a&&(c.__data__=a.__data__),s[d]=c,dn(s[d],t,n,d,s,Ce(a,n)));return new Ue(o,this._parents,t,n)}function oc(e){var t=this._name,n=this._id;typeof e!="function"&&(e=Ai(e));for(var r=this._groups,i=r.length,o=[],l=[],u=0;u<i;++u)for(var f=r[u],s=f.length,a,c=0;c<s;++c)if(a=f[c]){for(var d=e.call(a,a.__data__,c,f),p,g=Ce(a,n),R=0,x=d.length;R<x;++R)(p=d[R])&&dn(p,t,n,R,d,g);o.push(d),l.push(a)}return new Ue(o,l,t,n)}var lc=Vt.prototype.constructor;function ac(){return new lc(this._groups,this._parents)}function sc(e,t){var n,r,i;return function(){var o=rt(this,e),l=(this.style.removeProperty(e),rt(this,e));return o===l?null:o===n&&l===r?i:i=t(n=o,r=l)}}function eo(e){return function(){this.style.removeProperty(e)}}function uc(e,t,n){var r,i=n+"",o;return function(){var l=rt(this,e);return l===i?null:l===r?o:o=t(r=l,n)}}function cc(e,t,n){var r,i,o;return function(){var l=rt(this,e),u=n(this),f=u+"";return u==null&&(f=u=(this.style.removeProperty(e),rt(this,e))),l===f?null:l===r&&f===i?o:(i=f,o=t(r=l,u))}}function fc(e,t){var n,r,i,o="style."+t,l="end."+o,u;return function(){var f=Ae(this,e),s=f.on,a=f.value[o]==null?u||(u=eo(t)):void 0;(s!==n||i!==a)&&(r=(n=s).copy()).on(l,i=a),f.on=r}}function dc(e,t,n){var r=(e+="")=="transform"?mu:ji;return t==null?this.styleTween(e,sc(e,r)).on("end.style."+e,eo(e)):typeof t=="function"?this.styleTween(e,cc(e,r,ar(this,"style."+e,t))).each(fc(this._id,e)):this.styleTween(e,uc(e,r,t),n).on("end.style."+e,null)}function pc(e,t,n){return function(r){this.style.setProperty(e,t.call(this,r),n)}}function hc(e,t,n){var r,i;function o(){var l=t.apply(this,arguments);return l!==i&&(r=(i=l)&&pc(e,l,n)),r}return o._value=t,o}function mc(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;return this.tween(r,hc(e,t,n??""))}function _c(e){return function(){this.textContent=e}}function gc(e){return function(){var t=e(this);this.textContent=t??""}}function vc(e){return this.tween("text",typeof e=="function"?gc(ar(this,"text",e)):_c(e==null?"":e+""))}function yc(e){return function(t){this.textContent=e.call(this,t)}}function wc(e){var t,n;function r(){var i=e.apply(this,arguments);return i!==n&&(t=(n=i)&&yc(i)),t}return r._value=e,r}function xc(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,wc(e))}function bc(){for(var e=this._name,t=this._id,n=to(),r=this._groups,i=r.length,o=0;o<i;++o)for(var l=r[o],u=l.length,f,s=0;s<u;++s)if(f=l[s]){var a=Ce(f,t);dn(f,e,n,s,l,{time:a.time+a.delay+a.duration,delay:0,duration:a.duration,ease:a.ease})}return new Ue(r,this._parents,e,n)}function Ec(){var e,t,n=this,r=n._id,i=n.size();return new Promise(function(o,l){var u={value:l},f={value:function(){--i===0&&o()}};n.each(function(){var s=Ae(this,r),a=s.on;a!==e&&(t=(e=a).copy(),t._.cancel.push(u),t._.interrupt.push(u),t._.end.push(f)),s.on=t}),i===0&&o()})}var Sc=0;function Ue(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}function to(){return++Sc}var Ie=Vt.prototype;Ue.prototype={constructor:Ue,select:ic,selectAll:oc,selectChild:Ie.selectChild,selectChildren:Ie.selectChildren,filter:Zu,merge:Ju,selection:ac,transition:bc,call:Ie.call,nodes:Ie.nodes,node:Ie.node,size:Ie.size,empty:Ie.empty,each:Ie.each,on:tc,attr:Tu,attrTween:Fu,style:dc,styleTween:mc,text:vc,textTween:xc,remove:rc,tween:$u,delay:Hu,duration:Gu,ease:Ku,easeVarying:Qu,end:Ec,[Symbol.iterator]:Ie[Symbol.iterator]};function kc(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var Rc={time:null,delay:0,duration:250,ease:kc};function Cc(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}function $c(e){var t,n;e instanceof Ue?(t=e._id,e=e._name):(t=to(),(n=Rc).time=or(),e=e==null?null:e+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var l=r[o],u=l.length,f,s=0;s<u;++s)(f=l[s])&&dn(f,e,t,s,l,n||Cc(f,t));return new Ue(r,this._parents,e,t)}Vt.prototype.interrupt=ku;Vt.prototype.transition=$c;function Pc(e){if(!e.ok)throw new Error(e.status+" "+e.statusText);if(!(e.status===204||e.status===205))return e.json()}function Vc(e,t){return fetch(e,t).then(Pc)}var ee=1e-6,K=Math.PI,ge=K/2,Gr=K/4,we=K*2,ve=180/K,se=K/180,te=Math.abs,no=Math.atan,Rt=Math.atan2,oe=Math.cos,Mc=Math.exp,Ac=Math.log,le=Math.sin,Nc=Math.sign||function(e){return e>0?1:e<0?-1:0},Ye=Math.sqrt,Ic=Math.tan;function Tc(e){return e>1?0:e<-1?K:Math.acos(e)}function Ct(e){return e>1?ge:e<-1?-ge:Math.asin(e)}function Ee(){}function Jt(e,t){e&&Kr.hasOwnProperty(e.type)&&Kr[e.type](e,t)}var Yr={Feature:function(e,t){Jt(e.geometry,t)},FeatureCollection:function(e,t){for(var n=e.features,r=-1,i=n.length;++r<i;)Jt(n[r].geometry,t)}},Kr={Sphere:function(e,t){t.sphere()},Point:function(e,t){e=e.coordinates,t.point(e[0],e[1],e[2])},MultiPoint:function(e,t){for(var n=e.coordinates,r=-1,i=n.length;++r<i;)e=n[r],t.point(e[0],e[1],e[2])},LineString:function(e,t){In(e.coordinates,t,0)},MultiLineString:function(e,t){for(var n=e.coordinates,r=-1,i=n.length;++r<i;)In(n[r],t,0)},Polygon:function(e,t){Wr(e.coordinates,t)},MultiPolygon:function(e,t){for(var n=e.coordinates,r=-1,i=n.length;++r<i;)Wr(n[r],t)},GeometryCollection:function(e,t){for(var n=e.geometries,r=-1,i=n.length;++r<i;)Jt(n[r],t)}};function In(e,t,n){var r=-1,i=e.length-n,o;for(t.lineStart();++r<i;)o=e[r],t.point(o[0],o[1],o[2]);t.lineEnd()}function Wr(e,t){var n=-1,r=e.length;for(t.polygonStart();++n<r;)In(e[n],t,1);t.polygonEnd()}function We(e,t){e&&Yr.hasOwnProperty(e.type)?Yr[e.type](e,t):Jt(e,t)}function Tn(e){return[Rt(e[1],e[0]),Ct(e[2])]}function ot(e){var t=e[0],n=e[1],r=oe(n);return[r*oe(t),r*le(t),le(n)]}function Ut(e,t){return e[0]*t[0]+e[1]*t[1]+e[2]*t[2]}function jt(e,t){return[e[1]*t[2]-e[2]*t[1],e[2]*t[0]-e[0]*t[2],e[0]*t[1]-e[1]*t[0]]}function wn(e,t){e[0]+=t[0],e[1]+=t[1],e[2]+=t[2]}function Ot(e,t){return[e[0]*t,e[1]*t,e[2]*t]}function Un(e){var t=Ye(e[0]*e[0]+e[1]*e[1]+e[2]*e[2]);e[0]/=t,e[1]/=t,e[2]/=t}function On(e,t){function n(r,i){return r=e(r,i),t(r[0],r[1])}return e.invert&&t.invert&&(n.invert=function(r,i){return r=t.invert(r,i),r&&e.invert(r[0],r[1])}),n}function Dn(e,t){return te(e)>K&&(e-=Math.round(e/we)*we),[e,t]}Dn.invert=Dn;function ro(e,t,n){return(e%=we)?t||n?On(Zr(e),Jr(t,n)):Zr(e):t||n?Jr(t,n):Dn}function Qr(e){return function(t,n){return t+=e,te(t)>K&&(t-=Math.round(t/we)*we),[t,n]}}function Zr(e){var t=Qr(e);return t.invert=Qr(-e),t}function Jr(e,t){var n=oe(e),r=le(e),i=oe(t),o=le(t);function l(u,f){var s=oe(f),a=oe(u)*s,c=le(u)*s,d=le(f),p=d*n+a*r;return[Rt(c*i-p*o,a*n-d*r),Ct(p*i+c*o)]}return l.invert=function(u,f){var s=oe(f),a=oe(u)*s,c=le(u)*s,d=le(f),p=d*i-c*o;return[Rt(c*i+d*o,a*n+p*r),Ct(p*n-a*r)]},l}function Uc(e){e=ro(e[0]*se,e[1]*se,e.length>2?e[2]*se:0);function t(n){return n=e(n[0]*se,n[1]*se),n[0]*=ve,n[1]*=ve,n}return t.invert=function(n){return n=e.invert(n[0]*se,n[1]*se),n[0]*=ve,n[1]*=ve,n},t}function Oc(e,t,n,r,i,o){if(n){var l=oe(t),u=le(t),f=r*n;i==null?(i=t+r*we,o=t-f/2):(i=jr(l,i),o=jr(l,o),(r>0?i<o:i>o)&&(i+=r*we));for(var s,a=i;r>0?a>o:a<o;a-=f)s=Tn([l,-u*oe(a),-u*le(a)]),e.point(s[0],s[1])}}function jr(e,t){t=ot(t),t[0]-=e,Un(t);var n=Tc(-t[1]);return((-t[2]<0?-n:n)+we-ee)%we}function io(){var e=[],t;return{point:function(n,r,i){t.push([n,r,i])},lineStart:function(){e.push(t=[])},lineEnd:Ee,rejoin:function(){e.length>1&&e.push(e.pop().concat(e.shift()))},result:function(){var n=e;return e=[],t=null,n}}}function Bt(e,t){return te(e[0]-t[0])<ee&&te(e[1]-t[1])<ee}function Dt(e,t,n,r){this.x=e,this.z=t,this.o=n,this.e=r,this.v=!1,this.n=this.p=null}function oo(e,t,n,r,i){var o=[],l=[],u,f;if(e.forEach(function(g){if(!((R=g.length-1)<=0)){var R,x=g[0],S=g[R],E;if(Bt(x,S)){if(!x[2]&&!S[2]){for(i.lineStart(),u=0;u<R;++u)i.point((x=g[u])[0],x[1]);i.lineEnd();return}S[0]+=2*ee}o.push(E=new Dt(x,g,null,!0)),l.push(E.o=new Dt(x,null,E,!1)),o.push(E=new Dt(S,g,null,!1)),l.push(E.o=new Dt(S,null,E,!0))}}),!!o.length){for(l.sort(t),ei(o),ei(l),u=0,f=l.length;u<f;++u)l[u].e=n=!n;for(var s=o[0],a,c;;){for(var d=s,p=!0;d.v;)if((d=d.n)===s)return;a=d.z,i.lineStart();do{if(d.v=d.o.v=!0,d.e){if(p)for(u=0,f=a.length;u<f;++u)i.point((c=a[u])[0],c[1]);else r(d.x,d.n.x,1,i);d=d.n}else{if(p)for(a=d.p.z,u=a.length-1;u>=0;--u)i.point((c=a[u])[0],c[1]);else r(d.x,d.p.x,-1,i);d=d.p}d=d.o,a=d.z,p=!p}while(!d.v);i.lineEnd()}}}function ei(e){if(t=e.length){for(var t,n=0,r=e[0],i;++n<t;)r.n=i=e[n],i.p=r,r=i;r.n=i=e[0],i.p=r}}function xn(e){return te(e[0])<=K?e[0]:Nc(e[0])*((te(e[0])+K)%we-K)}function Dc(e,t){var n=xn(t),r=t[1],i=le(r),o=[le(n),-oe(n),0],l=0,u=0,f=new Be;i===1?r=ge+ee:i===-1&&(r=-ge-ee);for(var s=0,a=e.length;s<a;++s)if(d=(c=e[s]).length)for(var c,d,p=c[d-1],g=xn(p),R=p[1]/2+Gr,x=le(R),S=oe(R),E=0;E<d;++E,g=A,x=N,S=b,p=$){var $=c[E],A=xn($),I=$[1]/2+Gr,N=le(I),b=oe(I),_=A-g,C=_>=0?1:-1,y=C*_,m=y>K,V=x*N;if(f.add(Rt(V*C*le(y),S*b+V*oe(y))),l+=m?_+C*we:_,m^g>=n^A>=n){var P=jt(ot(p),ot($));Un(P);var U=jt(o,P);Un(U);var v=(m^_>=0?-1:1)*Ct(U[2]);(r>v||r===v&&(P[0]||P[1]))&&(u+=m^_>=0?1:-1)}}return(l<-1e-6||l<ee&&f<-1e-12)^u&1}function lo(e,t,n,r){return function(i){var o=t(i),l=io(),u=t(l),f=!1,s,a,c,d={point:p,lineStart:R,lineEnd:x,polygonStart:function(){d.point=S,d.lineStart=E,d.lineEnd=$,a=[],s=[]},polygonEnd:function(){d.point=p,d.lineStart=R,d.lineEnd=x,a=Pi(a);var A=Dc(s,r);a.length?(f||(i.polygonStart(),f=!0),oo(a,Fc,A,n,i)):A&&(f||(i.polygonStart(),f=!0),i.lineStart(),n(null,null,1,i),i.lineEnd()),f&&(i.polygonEnd(),f=!1),a=s=null},sphere:function(){i.polygonStart(),i.lineStart(),n(null,null,1,i),i.lineEnd(),i.polygonEnd()}};function p(A,I){e(A,I)&&i.point(A,I)}function g(A,I){o.point(A,I)}function R(){d.point=g,o.lineStart()}function x(){d.point=p,o.lineEnd()}function S(A,I){c.push([A,I]),u.point(A,I)}function E(){u.lineStart(),c=[]}function $(){S(c[0][0],c[0][1]),u.lineEnd();var A=u.clean(),I=l.result(),N,b=I.length,_,C,y;if(c.pop(),s.push(c),c=null,!!b){if(A&1){if(C=I[0],(_=C.length-1)>0){for(f||(i.polygonStart(),f=!0),i.lineStart(),N=0;N<_;++N)i.point((y=C[N])[0],y[1]);i.lineEnd()}return}b>1&&A&2&&I.push(I.pop().concat(I.shift())),a.push(I.filter(Lc))}}return d}}function Lc(e){return e.length>1}function Fc(e,t){return((e=e.x)[0]<0?e[1]-ge-ee:ge-e[1])-((t=t.x)[0]<0?t[1]-ge-ee:ge-t[1])}const ti=lo(function(){return!0},qc,Hc,[-K,-ge]);function qc(e){var t=NaN,n=NaN,r=NaN,i;return{lineStart:function(){e.lineStart(),i=1},point:function(o,l){var u=o>0?K:-K,f=te(o-t);te(f-K)<ee?(e.point(t,n=(n+l)/2>0?ge:-ge),e.point(r,n),e.lineEnd(),e.lineStart(),e.point(u,n),e.point(o,n),i=0):r!==u&&f>=K&&(te(t-r)<ee&&(t-=r*ee),te(o-u)<ee&&(o-=u*ee),n=zc(t,n,o,l),e.point(r,n),e.lineEnd(),e.lineStart(),e.point(u,n),i=0),e.point(t=o,n=l),r=u},lineEnd:function(){e.lineEnd(),t=n=NaN},clean:function(){return 2-i}}}function zc(e,t,n,r){var i,o,l=le(e-n);return te(l)>ee?no((le(t)*(o=oe(r))*le(n)-le(r)*(i=oe(t))*le(e))/(i*o*l)):(t+r)/2}function Hc(e,t,n,r){var i;if(e==null)i=n*ge,r.point(-K,i),r.point(0,i),r.point(K,i),r.point(K,0),r.point(K,-i),r.point(0,-i),r.point(-K,-i),r.point(-K,0),r.point(-K,i);else if(te(e[0]-t[0])>ee){var o=e[0]<t[0]?K:-K;i=n*o/2,r.point(-o,i),r.point(0,i),r.point(o,i)}else r.point(t[0],t[1])}function Bc(e){var t=oe(e),n=2*se,r=t>0,i=te(t)>ee;function o(a,c,d,p){Oc(p,e,n,d,a,c)}function l(a,c){return oe(a)*oe(c)>t}function u(a){var c,d,p,g,R;return{lineStart:function(){g=p=!1,R=1},point:function(x,S){var E=[x,S],$,A=l(x,S),I=r?A?0:s(x,S):A?s(x+(x<0?K:-K),S):0;if(!c&&(g=p=A)&&a.lineStart(),A!==p&&($=f(c,E),(!$||Bt(c,$)||Bt(E,$))&&(E[2]=1)),A!==p)R=0,A?(a.lineStart(),$=f(E,c),a.point($[0],$[1])):($=f(c,E),a.point($[0],$[1],2),a.lineEnd()),c=$;else if(i&&c&&r^A){var N;!(I&d)&&(N=f(E,c,!0))&&(R=0,r?(a.lineStart(),a.point(N[0][0],N[0][1]),a.point(N[1][0],N[1][1]),a.lineEnd()):(a.point(N[1][0],N[1][1]),a.lineEnd(),a.lineStart(),a.point(N[0][0],N[0][1],3)))}A&&(!c||!Bt(c,E))&&a.point(E[0],E[1]),c=E,p=A,d=I},lineEnd:function(){p&&a.lineEnd(),c=null},clean:function(){return R|(g&&p)<<1}}}function f(a,c,d){var p=ot(a),g=ot(c),R=[1,0,0],x=jt(p,g),S=Ut(x,x),E=x[0],$=S-E*E;if(!$)return!d&&a;var A=t*S/$,I=-t*E/$,N=jt(R,x),b=Ot(R,A),_=Ot(x,I);wn(b,_);var C=N,y=Ut(b,C),m=Ut(C,C),V=y*y-m*(Ut(b,b)-1);if(!(V<0)){var P=Ye(V),U=Ot(C,(-y-P)/m);if(wn(U,b),U=Tn(U),!d)return U;var v=a[0],D=c[0],F=a[1],W=c[1],ie;D<v&&(ie=v,v=D,D=ie);var M=D-v,G=te(M-K)<ee,de=G||M<ee;if(!G&&W<F&&(ie=F,F=W,W=ie),de?G?F+W>0^U[1]<(te(U[0]-v)<ee?F:W):F<=U[1]&&U[1]<=W:M>K^(v<=U[0]&&U[0]<=D)){var pe=Ot(C,(-y+P)/m);return wn(pe,b),[U,Tn(pe)]}}}function s(a,c){var d=r?e:K-e,p=0;return a<-d?p|=1:a>d&&(p|=2),c<-d?p|=4:c>d&&(p|=8),p}return lo(l,u,o,r?[0,-e]:[-K,e-K])}function Xc(e,t,n,r,i,o){var l=e[0],u=e[1],f=t[0],s=t[1],a=0,c=1,d=f-l,p=s-u,g;if(g=n-l,!(!d&&g>0)){if(g/=d,d<0){if(g<a)return;g<c&&(c=g)}else if(d>0){if(g>c)return;g>a&&(a=g)}if(g=i-l,!(!d&&g<0)){if(g/=d,d<0){if(g>c)return;g>a&&(a=g)}else if(d>0){if(g<a)return;g<c&&(c=g)}if(g=r-u,!(!p&&g>0)){if(g/=p,p<0){if(g<a)return;g<c&&(c=g)}else if(p>0){if(g>c)return;g>a&&(a=g)}if(g=o-u,!(!p&&g<0)){if(g/=p,p<0){if(g>c)return;g>a&&(a=g)}else if(p>0){if(g<a)return;g<c&&(c=g)}return a>0&&(e[0]=l+a*d,e[1]=u+a*p),c<1&&(t[0]=l+c*d,t[1]=u+c*p),!0}}}}}var Lt=1e9,Ft=-1e9;function Gc(e,t,n,r){function i(s,a){return e<=s&&s<=n&&t<=a&&a<=r}function o(s,a,c,d){var p=0,g=0;if(s==null||(p=l(s,c))!==(g=l(a,c))||f(s,a)<0^c>0)do d.point(p===0||p===3?e:n,p>1?r:t);while((p=(p+c+4)%4)!==g);else d.point(a[0],a[1])}function l(s,a){return te(s[0]-e)<ee?a>0?0:3:te(s[0]-n)<ee?a>0?2:1:te(s[1]-t)<ee?a>0?1:0:a>0?3:2}function u(s,a){return f(s.x,a.x)}function f(s,a){var c=l(s,1),d=l(a,1);return c!==d?c-d:c===0?a[1]-s[1]:c===1?s[0]-a[0]:c===2?s[1]-a[1]:a[0]-s[0]}return function(s){var a=s,c=io(),d,p,g,R,x,S,E,$,A,I,N,b={point:_,lineStart:V,lineEnd:P,polygonStart:y,polygonEnd:m};function _(v,D){i(v,D)&&a.point(v,D)}function C(){for(var v=0,D=0,F=p.length;D<F;++D)for(var W=p[D],ie=1,M=W.length,G=W[0],de,pe,Se=G[0],B=G[1];ie<M;++ie)de=Se,pe=B,G=W[ie],Se=G[0],B=G[1],pe<=r?B>r&&(Se-de)*(r-pe)>(B-pe)*(e-de)&&++v:B<=r&&(Se-de)*(r-pe)<(B-pe)*(e-de)&&--v;return v}function y(){a=c,d=[],p=[],N=!0}function m(){var v=C(),D=N&&v,F=(d=Pi(d)).length;(D||F)&&(s.polygonStart(),D&&(s.lineStart(),o(null,null,1,s),s.lineEnd()),F&&oo(d,u,v,o,s),s.polygonEnd()),a=s,d=p=g=null}function V(){b.point=U,p&&p.push(g=[]),I=!0,A=!1,E=$=NaN}function P(){d&&(U(R,x),S&&A&&c.rejoin(),d.push(c.result())),b.point=_,A&&a.lineEnd()}function U(v,D){var F=i(v,D);if(p&&g.push([v,D]),I)R=v,x=D,S=F,I=!1,F&&(a.lineStart(),a.point(v,D));else if(F&&A)a.point(v,D);else{var W=[E=Math.max(Ft,Math.min(Lt,E)),$=Math.max(Ft,Math.min(Lt,$))],ie=[v=Math.max(Ft,Math.min(Lt,v)),D=Math.max(Ft,Math.min(Lt,D))];Xc(W,ie,e,t,n,r)?(A||(a.lineStart(),a.point(W[0],W[1])),a.point(ie[0],ie[1]),F||a.lineEnd(),N=!1):F&&(a.lineStart(),a.point(v,D),N=!1)}E=v,$=D,A=F}return b}}const Ln=e=>e;var bn=new Be,Fn=new Be,ao,so,qn,zn,Te={point:Ee,lineStart:Ee,lineEnd:Ee,polygonStart:function(){Te.lineStart=Yc,Te.lineEnd=Wc},polygonEnd:function(){Te.lineStart=Te.lineEnd=Te.point=Ee,bn.add(te(Fn)),Fn=new Be},result:function(){var e=bn/2;return bn=new Be,e}};function Yc(){Te.point=Kc}function Kc(e,t){Te.point=uo,ao=qn=e,so=zn=t}function uo(e,t){Fn.add(zn*e-qn*t),qn=e,zn=t}function Wc(){uo(ao,so)}var lt=1/0,en=lt,$t=-lt,tn=$t,nn={point:Qc,lineStart:Ee,lineEnd:Ee,polygonStart:Ee,polygonEnd:Ee,result:function(){var e=[[lt,en],[$t,tn]];return $t=tn=-(en=lt=1/0),e}};function Qc(e,t){e<lt&&(lt=e),e>$t&&($t=e),t<en&&(en=t),t>tn&&(tn=t)}var Hn=0,Bn=0,pt=0,rn=0,on=0,Qe=0,Xn=0,Gn=0,ht=0,co,fo,$e,Pe,be={point:Ge,lineStart:ni,lineEnd:ri,polygonStart:function(){be.lineStart=jc,be.lineEnd=ef},polygonEnd:function(){be.point=Ge,be.lineStart=ni,be.lineEnd=ri},result:function(){var e=ht?[Xn/ht,Gn/ht]:Qe?[rn/Qe,on/Qe]:pt?[Hn/pt,Bn/pt]:[NaN,NaN];return Hn=Bn=pt=rn=on=Qe=Xn=Gn=ht=0,e}};function Ge(e,t){Hn+=e,Bn+=t,++pt}function ni(){be.point=Zc}function Zc(e,t){be.point=Jc,Ge($e=e,Pe=t)}function Jc(e,t){var n=e-$e,r=t-Pe,i=Ye(n*n+r*r);rn+=i*($e+e)/2,on+=i*(Pe+t)/2,Qe+=i,Ge($e=e,Pe=t)}function ri(){be.point=Ge}function jc(){be.point=tf}function ef(){po(co,fo)}function tf(e,t){be.point=po,Ge(co=$e=e,fo=Pe=t)}function po(e,t){var n=e-$e,r=t-Pe,i=Ye(n*n+r*r);rn+=i*($e+e)/2,on+=i*(Pe+t)/2,Qe+=i,i=Pe*e-$e*t,Xn+=i*($e+e),Gn+=i*(Pe+t),ht+=i*3,Ge($e=e,Pe=t)}function ho(e){this._context=e}ho.prototype={_radius:4.5,pointRadius:function(e){return this._radius=e,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){this._line===0&&this._context.closePath(),this._point=NaN},point:function(e,t){switch(this._point){case 0:{this._context.moveTo(e,t),this._point=1;break}case 1:{this._context.lineTo(e,t);break}default:{this._context.moveTo(e+this._radius,t),this._context.arc(e,t,this._radius,0,we);break}}},result:Ee};var Yn=new Be,En,mo,_o,mt,_t,Pt={point:Ee,lineStart:function(){Pt.point=nf},lineEnd:function(){En&&go(mo,_o),Pt.point=Ee},polygonStart:function(){En=!0},polygonEnd:function(){En=null},result:function(){var e=+Yn;return Yn=new Be,e}};function nf(e,t){Pt.point=go,mo=mt=e,_o=_t=t}function go(e,t){mt-=e,_t-=t,Yn.add(Ye(mt*mt+_t*_t)),mt=e,_t=t}let ii,ln,oi,li;class ai{constructor(t){this._append=t==null?vo:rf(t),this._radius=4.5,this._=""}pointRadius(t){return this._radius=+t,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){this._line===0&&(this._+="Z"),this._point=NaN}point(t,n){switch(this._point){case 0:{this._append`M${t},${n}`,this._point=1;break}case 1:{this._append`L${t},${n}`;break}default:{if(this._append`M${t},${n}`,this._radius!==oi||this._append!==ln){const r=this._radius,i=this._;this._="",this._append`m0,${r}a${r},${r} 0 1,1 0,${-2*r}a${r},${r} 0 1,1 0,${2*r}z`,oi=r,ln=this._append,li=this._,this._=i}this._+=li;break}}}result(){const t=this._;return this._="",t.length?t:null}}function vo(e){let t=1;this._+=e[0];for(const n=e.length;t<n;++t)this._+=arguments[t]+e[t]}function rf(e){const t=Math.floor(e);if(!(t>=0))throw new RangeError(`invalid digits: ${e}`);if(t>15)return vo;if(t!==ii){const n=10**t;ii=t,ln=function(i){let o=1;this._+=i[0];for(const l=i.length;o<l;++o)this._+=Math.round(arguments[o]*n)/n+i[o]}}return ln}function si(e,t){let n=3,r=4.5,i,o;function l(u){return u&&(typeof r=="function"&&o.pointRadius(+r.apply(this,arguments)),We(u,i(o))),o.result()}return l.area=function(u){return We(u,i(Te)),Te.result()},l.measure=function(u){return We(u,i(Pt)),Pt.result()},l.bounds=function(u){return We(u,i(nn)),nn.result()},l.centroid=function(u){return We(u,i(be)),be.result()},l.projection=function(u){return arguments.length?(i=u==null?(e=null,Ln):(e=u).stream,l):e},l.context=function(u){return arguments.length?(o=u==null?(t=null,new ai(n)):new ho(t=u),typeof r!="function"&&o.pointRadius(r),l):t},l.pointRadius=function(u){return arguments.length?(r=typeof u=="function"?u:(o.pointRadius(+u),+u),l):r},l.digits=function(u){if(!arguments.length)return n;if(u==null)n=null;else{const f=Math.floor(u);if(!(f>=0))throw new RangeError(`invalid digits: ${u}`);n=f}return t===null&&(o=new ai(n)),l},l.projection(e).digits(n).context(t)}function sr(e){return function(t){var n=new Kn;for(var r in e)n[r]=e[r];return n.stream=t,n}}function Kn(){}Kn.prototype={constructor:Kn,point:function(e,t){this.stream.point(e,t)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};function ur(e,t,n){var r=e.clipExtent&&e.clipExtent();return e.scale(150).translate([0,0]),r!=null&&e.clipExtent(null),We(n,e.stream(nn)),t(nn.result()),r!=null&&e.clipExtent(r),e}function yo(e,t,n){return ur(e,function(r){var i=t[1][0]-t[0][0],o=t[1][1]-t[0][1],l=Math.min(i/(r[1][0]-r[0][0]),o/(r[1][1]-r[0][1])),u=+t[0][0]+(i-l*(r[1][0]+r[0][0]))/2,f=+t[0][1]+(o-l*(r[1][1]+r[0][1]))/2;e.scale(150*l).translate([u,f])},n)}function of(e,t,n){return yo(e,[[0,0],t],n)}function lf(e,t,n){return ur(e,function(r){var i=+t,o=i/(r[1][0]-r[0][0]),l=(i-o*(r[1][0]+r[0][0]))/2,u=-o*r[0][1];e.scale(150*o).translate([l,u])},n)}function af(e,t,n){return ur(e,function(r){var i=+t,o=i/(r[1][1]-r[0][1]),l=-o*r[0][0],u=(i-o*(r[1][1]+r[0][1]))/2;e.scale(150*o).translate([l,u])},n)}var ui=16,sf=oe(30*se);function ci(e,t){return+t?cf(e,t):uf(e)}function uf(e){return sr({point:function(t,n){t=e(t,n),this.stream.point(t[0],t[1])}})}function cf(e,t){function n(r,i,o,l,u,f,s,a,c,d,p,g,R,x){var S=s-r,E=a-i,$=S*S+E*E;if($>4*t&&R--){var A=l+d,I=u+p,N=f+g,b=Ye(A*A+I*I+N*N),_=Ct(N/=b),C=te(te(N)-1)<ee||te(o-c)<ee?(o+c)/2:Rt(I,A),y=e(C,_),m=y[0],V=y[1],P=m-r,U=V-i,v=E*P-S*U;(v*v/$>t||te((S*P+E*U)/$-.5)>.3||l*d+u*p+f*g<sf)&&(n(r,i,o,l,u,f,m,V,C,A/=b,I/=b,N,R,x),x.point(m,V),n(m,V,C,A,I,N,s,a,c,d,p,g,R,x))}}return function(r){var i,o,l,u,f,s,a,c,d,p,g,R,x={point:S,lineStart:E,lineEnd:A,polygonStart:function(){r.polygonStart(),x.lineStart=I},polygonEnd:function(){r.polygonEnd(),x.lineStart=E}};function S(_,C){_=e(_,C),r.point(_[0],_[1])}function E(){c=NaN,x.point=$,r.lineStart()}function $(_,C){var y=ot([_,C]),m=e(_,C);n(c,d,a,p,g,R,c=m[0],d=m[1],a=_,p=y[0],g=y[1],R=y[2],ui,r),r.point(c,d)}function A(){x.point=S,r.lineEnd()}function I(){E(),x.point=N,x.lineEnd=b}function N(_,C){$(i=_,C),o=c,l=d,u=p,f=g,s=R,x.point=$}function b(){n(c,d,a,p,g,R,o,l,i,u,f,s,ui,r),x.lineEnd=A,A()}return x}}var ff=sr({point:function(e,t){this.stream.point(e*se,t*se)}});function df(e){return sr({point:function(t,n){var r=e(t,n);return this.stream.point(r[0],r[1])}})}function pf(e,t,n,r,i){function o(l,u){return l*=r,u*=i,[t+e*l,n-e*u]}return o.invert=function(l,u){return[(l-t)/e*r,(n-u)/e*i]},o}function fi(e,t,n,r,i,o){if(!o)return pf(e,t,n,r,i);var l=oe(o),u=le(o),f=l*e,s=u*e,a=l/e,c=u/e,d=(u*n-l*t)/e,p=(u*t+l*n)/e;function g(R,x){return R*=r,x*=i,[f*R-s*x+t,n-s*R-f*x]}return g.invert=function(R,x){return[r*(a*R-c*x+d),i*(p-c*R-a*x)]},g}function hf(e){return mf(function(){return e})()}function mf(e){var t,n=150,r=480,i=250,o=0,l=0,u=0,f=0,s=0,a,c=0,d=1,p=1,g=null,R=ti,x=null,S,E,$,A=Ln,I=.5,N,b,_,C,y;function m(v){return _(v[0]*se,v[1]*se)}function V(v){return v=_.invert(v[0],v[1]),v&&[v[0]*ve,v[1]*ve]}m.stream=function(v){return C&&y===v?C:C=ff(df(a)(R(N(A(y=v)))))},m.preclip=function(v){return arguments.length?(R=v,g=void 0,U()):R},m.postclip=function(v){return arguments.length?(A=v,x=S=E=$=null,U()):A},m.clipAngle=function(v){return arguments.length?(R=+v?Bc(g=v*se):(g=null,ti),U()):g*ve},m.clipExtent=function(v){return arguments.length?(A=v==null?(x=S=E=$=null,Ln):Gc(x=+v[0][0],S=+v[0][1],E=+v[1][0],$=+v[1][1]),U()):x==null?null:[[x,S],[E,$]]},m.scale=function(v){return arguments.length?(n=+v,P()):n},m.translate=function(v){return arguments.length?(r=+v[0],i=+v[1],P()):[r,i]},m.center=function(v){return arguments.length?(o=v[0]%360*se,l=v[1]%360*se,P()):[o*ve,l*ve]},m.rotate=function(v){return arguments.length?(u=v[0]%360*se,f=v[1]%360*se,s=v.length>2?v[2]%360*se:0,P()):[u*ve,f*ve,s*ve]},m.angle=function(v){return arguments.length?(c=v%360*se,P()):c*ve},m.reflectX=function(v){return arguments.length?(d=v?-1:1,P()):d<0},m.reflectY=function(v){return arguments.length?(p=v?-1:1,P()):p<0},m.precision=function(v){return arguments.length?(N=ci(b,I=v*v),U()):Ye(I)},m.fitExtent=function(v,D){return yo(m,v,D)},m.fitSize=function(v,D){return of(m,v,D)},m.fitWidth=function(v,D){return lf(m,v,D)},m.fitHeight=function(v,D){return af(m,v,D)};function P(){var v=fi(n,0,0,d,p,c).apply(null,t(o,l)),D=fi(n,r-v[0],i-v[1],d,p,c);return a=ro(u,f,s),b=On(t,D),_=On(a,b),N=ci(b,I),U()}function U(){return C=y=null,m}return function(){return t=e.apply(this,arguments),m.invert=t.invert&&V,P()}}function cr(e,t){return[e,Ac(Ic((ge+t)/2))]}cr.invert=function(e,t){return[e,2*no(Mc(t))-ge]};function _f(){return gf(cr).scale(961/we)}function gf(e){var t=hf(e),n=t.center,r=t.scale,i=t.translate,o=t.clipExtent,l=null,u,f,s;t.scale=function(c){return arguments.length?(r(c),a()):r()},t.translate=function(c){return arguments.length?(i(c),a()):i()},t.center=function(c){return arguments.length?(n(c),a()):n()},t.clipExtent=function(c){return arguments.length?(c==null?l=u=f=s=null:(l=+c[0][0],u=+c[0][1],f=+c[1][0],s=+c[1][1]),a()):l==null?null:[[l,u],[f,s]]};function a(){var c=K*r(),d=t(Uc(t.rotate()).invert([0,0]));return o(l==null?[[d[0]-c,d[1]-c],[d[0]+c,d[1]+c]]:e===cr?[[Math.max(d[0]-c,l),u],[Math.min(d[0]+c,f),s]]:[[l,Math.max(d[1]-c,u)],[f,Math.min(d[1]+c,s)]])}return a()}function gt(e,t,n){this.k=e,this.x=t,this.y=n}gt.prototype={constructor:gt,scale:function(e){return e===1?this:new gt(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new gt(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};gt.prototype;const vf={class:"clock-container"},yf={class:"holographic-clock",viewBox:"0 0 100 100"},wf={class:"clock-markers"},xf=["x1","y1","x2","y2"],bf={class:"clock-hands"},Ef=["x2","y2"],Sf=["x2","y2"],kf=["x2","y2"],Rf=["id"],Cf=["id"],$f={__name:"VisualScreen",setup(e){const t=H(null),n=H(null),r=H(0),i=H(!1),o=new Map,l=H({x:0,y:0}),u=H(0),f=H(0),s=H(0),a=H(0);let c;const d=H([{id:"china",url:"https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json",center:[104,35],scaleFactor:.75,duration:5e3},{id:"sichuan",url:"https://geo.datav.aliyun.com/areas_v3/bound/510000_full.json",center:[103,30],scaleFactor:3,duration:5e3},{id:"guangan",url:"https://geo.datav.aliyun.com/areas_v3/bound/511600_full.json",center:[106.6,30.5],scaleFactor:30,duration:5e3},{id:"yuechi",url:"https://geo.datav.aliyun.com/areas_v3/bound/511621.json",center:[106.4,30.5],scaleFactor:50,duration:5e3}]),p={longitude:106.43,latitude:30.55},g=()=>t.value?{width:t.value.clientWidth,height:t.value.clientHeight}:{width:window.innerWidth,height:window.innerHeight},R=()=>{const _=new Date;u.value=_.getHours()%12,f.value=_.getMinutes(),s.value=_.getSeconds(),a.value=_.getMilliseconds()},x=_=>{if(o.has(_.id))return;const C=g(),y=At(`#${_.id}-map`).attr("viewBox",`0 0 ${C.width} ${C.height}`).attr("preserveAspectRatio","xMidYMid meet");Vc(_.url).then(m=>{const V=_f().center(_.center).scale(Math.min(C.width,C.height)*_.scaleFactor).translate([C.width/2,C.height/2]);o.set(_.id,{projection:V,data:m}),y.selectAll(".boundary").data(m.features).enter().append("path").attr("class","boundary").attr("d",si().projection(V)),S(_.id)}).catch(m=>console.error("地图加载失败:",m))},S=_=>{const{projection:C}=o.get(_)||{};if(!C)return;const[y,m]=C([p.longitude,p.latitude]);l.value={x:y,y:m}},E=()=>{const _=g();d.value.forEach(C=>{const{projection:y,data:m}=o.get(C.id)||{};!y||!m||(y.scale(Math.min(_.width,_.height)*C.scaleFactor).translate([_.width/2,_.height/2]),At(`#${C.id}-map`).attr("viewBox",`0 0 ${_.width} ${_.height}`).selectAll(".boundary").attr("d",si().projection(y)),C.id===d.value[r.value].id&&S(C.id))})},$=_=>{if(i.value)return;i.value=!0;const C=d.value.length,y=(r.value+_+C)%C,m=d.value[r.value],V=d.value[y];At(`#${m.id}-container`).transition().duration(1500).style("opacity",0).on("end",()=>{At(`#${V.id}-container`).style("opacity",0).classed("map-visible",!0).transition().duration(1500).style("opacity",1).on("end",()=>{r.value=y,i.value=!1,S(V.id)})})},A=()=>{const{width:C,height:y}=g();for(let m=0;m<100;m++){const V=document.createElement("div");V.className="particle";const P=Math.random()*C,U=Math.random()*y,v=Math.random()*10,D=Math.random()*2+1;V.style.left=`${P}px`,V.style.top=`${U}px`,V.style.animationDelay=`${v}s`,V.style.width=`${D}px`,V.style.height=`${D}px`,n.value.appendChild(V)}};let I;const N=()=>{I=setInterval(()=>{$(1)},d.value[0].duration+1500)};at(()=>{R(),c=setInterval(R,50),d.value.forEach(x),A(),N(),document.addEventListener("fullscreenchange",b)}),$o(()=>{clearInterval(c),clearInterval(I),o.clear(),document.removeEventListener("fullscreenchange",b)});const b=()=>{Xt(()=>{E(),n.value&&(n.value.innerHTML=""),A()})};return Wn([()=>window.innerWidth,()=>window.innerHeight,()=>{var _;return(_=t.value)==null?void 0:_.clientWidth},()=>{var _;return(_=t.value)==null?void 0:_.clientHeight}],()=>{Xt(E)}),(_,C)=>(ne(),fe("div",{class:"holographic-container",ref_key:"container",ref:t},[C[3]||(C[3]=q("div",{class:"slogan-container"},[q("div",null,"对党忠诚  服务人民"),q("div",null,"执法公正  纪律严明")],-1)),C[4]||(C[4]=q("div",{class:"title-container"},[q("div",{class:"holographic-title"},"岳池县公安局情报指挥中心")],-1)),q("div",vf,[(ne(),fe("svg",yf,[C[0]||(C[0]=q("circle",{cx:"50",cy:"50",r:"45",fill:"rgba(0,0,0,0)",stroke:"#00e5ff","stroke-width":"0.5"},null,-1)),C[1]||(C[1]=q("circle",{cx:"50",cy:"50",r:"42",fill:"rgba(5,15,44,0.5)"},null,-1)),q("g",wf,[(ne(),fe(He,null,je(12,y=>q("line",{key:y,x1:50+38*Math.cos((y*30-90)*Math.PI/180),y1:50+38*Math.sin((y*30-90)*Math.PI/180),x2:50+42*Math.cos((y*30-90)*Math.PI/180),y2:50+42*Math.sin((y*30-90)*Math.PI/180),stroke:"#00e5ff","stroke-width":"1.5"},null,8,xf)),64))]),q("g",bf,[q("line",{class:"hour-hand",x1:50,y1:50,x2:50+20*Math.cos((u.value*30+f.value*.5-90)*Math.PI/180),y2:50+20*Math.sin((u.value*30+f.value*.5-90)*Math.PI/180),stroke:"#9e4edd","stroke-width":"3","stroke-linecap":"round"},null,8,Ef),q("line",{class:"minute-hand",x1:50,y1:50,x2:50+30*Math.cos((f.value*6+s.value*.1-90)*Math.PI/180),y2:50+30*Math.sin((f.value*6+s.value*.1-90)*Math.PI/180),stroke:"#ff4d4d","stroke-width":"2","stroke-linecap":"round"},null,8,Sf),q("line",{class:"second-hand",x1:50,y1:50,x2:50+38*Math.cos((s.value*6+a.value*.006-90)*Math.PI/180),y2:50+38*Math.sin((s.value*6+a.value*.006-90)*Math.PI/180),stroke:"#00e5ff","stroke-width":"1","stroke-linecap":"round"},null,8,kf)]),C[2]||(C[2]=q("circle",{cx:"50",cy:"50",r:"2",fill:"#fff"},null,-1))]))]),q("div",{ref_key:"particlesContainer",ref:n,class:"particles-container"},null,512),C[5]||(C[5]=q("div",{class:"hologram-grid"},null,-1)),C[6]||(C[6]=q("div",{class:"scan-line-vertical"},null,-1)),C[7]||(C[7]=q("div",{class:"scan-line-horizontal"},null,-1)),C[8]||(C[8]=q("div",{class:"hologram-frame"},[q("div")],-1)),(ne(!0),fe(He,null,je(d.value,(y,m)=>(ne(),fe("div",{key:y.id,id:`${y.id}-container`,class:Po(["map-container",{"map-visible":r.value===m}])},[(ne(),fe("svg",{id:`${y.id}-map`,class:"map-svg"},null,8,Cf)),r.value===m?(ne(),fe("div",{key:0,class:"location-marker",style:Zn({left:l.value.x+"px",top:l.value.y+"px"})},null,4)):Qn("",!0)],10,Rf))),128))],512))}},Pf={class:"app-management-container"},Vf={class:"clearfix"},Mf={__name:"AppManagement",setup(e){const t=H([]),n=H([]),r=H(0),i=H(!1);H(!1),H("");const o=H(null),l=ze({id:null,name:"",url:"",isPublic:"",roles:[]}),u=ze({name:[{required:!0,message:"请输入应用名称",trigger:"blur"}],url:[{required:!0,message:"请输入应用URL",trigger:"blur"}],isPublic:[{required:!0,message:"请选择可见范围",trigger:"change"}],roles:[{type:"array",required:!0,validator:(x,S,E)=>{S&&S.length>0?E():E(new Error("至少选择一个角色组"))},trigger:"change"}]}),f=x=>{try{return JSON.parse(x).map(E=>{const $=n.value.find(A=>String(A.value)===String(E));return $?$.label:E})}catch(S){return console.error("解析 roleList 失败:",S),[]}},s=async()=>{try{const x=new FormData;x.append("controlCode","query");const S=await ce.post("/api/application_manage.php",x);S.status===1&&(t.value=S.data.application,n.value=S.data.rolelist.map(E=>({value:E.id,label:E.roleName})),r.value=S.data.application.length)}catch(x){console.error("获取应用列表失败:",x),Q.error("获取应用列表失败")}};at(()=>{s()});const a=x=>{switch(x){case"0":return"公开";case"1":return"非第三方人员";case"2":return"民警";case"3":return"授权用户";default:return"未知"}},c=()=>{p(),i.value=!0},d=x=>{if(p(),n.value.length===0){console.error("角色选项未加载完成"),Q.error("角色数据加载中，请稍后再试");return}l.id=x.id,l.name=x.application_name,l.url=x.url,l.isPublic=x.public;try{const S=JSON.parse(x.roleList);l.roles=S.map(E=>String(E)),Xt(()=>{l.roles=[...l.roles]})}catch(S){console.error("解析角色列表失败:",S),l.roles=[]}i.value=!0},p=()=>{o.value&&o.value.resetFields(),l.id=null,l.name="",l.url="",l.isPublic="",l.roles=[]},g=x=>{er.confirm(`确定要删除应用 "${x.application_name}" 吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const S=new FormData;S.append("controlCode","del"),S.append("id",x.id);const E=await ce.post("/api/application_manage.php",S);E.status===1?(Q.success("删除成功"),s()):Q.error("删除失败: "+(E.message||"未知错误"))}catch(S){console.error("删除应用失败:",S),Q.error("删除应用失败: "+S.message)}}).catch(()=>{Q.info("已取消删除")})},R=async()=>{try{await o.value.validate()}catch(x){console.error("表单验证失败:",x);return}try{const x=new FormData;x.append("controlCode",l.id?"modify":"add"),x.append("id",l.id),x.append("application_name",l.name),x.append("url",l.url),x.append("public",l.isPublic),x.append("roleList",JSON.stringify(l.roles));const S=await ce.post("/api/application_manage.php",x);S.status===1?(Q.success("更新成功"),i.value=!1,s()):Q.error(S.message||"更新失败")}catch(x){console.error("更新应用失败:",x),Q.error("更新应用失败: "+x.message)}};return(x,S)=>{const E=L("el-button"),$=L("el-table-column"),A=L("el-tag"),I=L("el-icon"),N=L("el-button-group"),b=L("el-table"),_=L("el-card"),C=L("el-input"),y=L("el-form-item"),m=L("el-option"),V=L("el-select"),P=L("el-checkbox"),U=L("el-checkbox-group"),v=L("el-form"),D=L("el-dialog");return ne(),fe("div",Pf,[h(_,{class:"box-card"},{header:w(()=>[q("div",Vf,[h(E,{style:{float:"right"},round:"",type:"primary",onClick:c},{default:w(()=>S[6]||(S[6]=[J(" 添加应用 ")])),_:1,__:[6]})])]),default:w(()=>[h(b,{data:t.value,stripe:"",border:"",fit:"","highlight-current-row":"",onRowDblclick:d,style:{width:"100%"}},{default:w(()=>[h($,{prop:"application_name",label:"应用名称","min-width":"120"}),h($,{prop:"url",label:"URL","min-width":"150"}),h($,{prop:"public",label:"是否公开",width:"150",align:"center"},{default:w(F=>[J(Ve(a(F.row.public)),1)]),_:1}),h($,{prop:"roles",label:"授权角色组","min-width":"180"},{default:w(F=>[(ne(!0),fe(He,null,je(f(F.row.roleList),W=>(ne(),et(A,{key:W,size:"small",type:"info"},{default:w(()=>[J(Ve(W),1)]),_:2},1024))),128))]),_:1}),h($,{label:"操作",width:"160",align:"center"},{default:w(F=>[h(N,null,{default:w(()=>[h(E,{size:"mini",type:"warning",onClick:W=>d(F.row)},{default:w(()=>[h(I,null,{default:w(()=>[h(re(Jn))]),_:1})]),_:2},1032,["onClick"]),h(E,{size:"mini",type:"danger",onClick:W=>g(F.row)},{default:w(()=>[h(I,null,{default:w(()=>[h(re(jn))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])]),_:1}),h(D,{modelValue:i.value,"onUpdate:modelValue":S[5]||(S[5]=F=>i.value=F),title:l.id?"编辑应用":"添加应用",width:"50%"},{footer:w(()=>[h(E,{onClick:S[4]||(S[4]=F=>i.value=!1)},{default:w(()=>S[7]||(S[7]=[J("取消")])),_:1,__:[7]}),h(E,{type:"primary",onClick:R},{default:w(()=>S[8]||(S[8]=[J("保存")])),_:1,__:[8]})]),default:w(()=>[h(v,{model:l,rules:u,ref_key:"formRef",ref:o,"label-width":"100px"},{default:w(()=>[h(y,{label:"应用名称",prop:"name"},{default:w(()=>[h(C,{modelValue:l.name,"onUpdate:modelValue":S[0]||(S[0]=F=>l.name=F),placeholder:"请输入应用名称"},null,8,["modelValue"])]),_:1}),h(y,{label:"URL",prop:"url"},{default:w(()=>[h(C,{modelValue:l.url,"onUpdate:modelValue":S[1]||(S[1]=F=>l.url=F),placeholder:"请输入应用URL"},null,8,["modelValue"])]),_:1}),h(y,{label:"是否公开",prop:"isPublic"},{default:w(()=>[h(V,{modelValue:l.isPublic,"onUpdate:modelValue":S[2]||(S[2]=F=>l.isPublic=F),placeholder:"请选择是否公开"},{default:w(()=>[h(m,{value:"0",label:"公开"}),h(m,{value:"1",label:"非第三方人员"}),h(m,{value:"2",label:"民警"}),h(m,{value:"3",label:"授权用户"})]),_:1},8,["modelValue"])]),_:1}),h(y,{label:"授权角色组",prop:"roles"},{default:w(()=>[h(U,{modelValue:l.roles,"onUpdate:modelValue":S[3]||(S[3]=F=>l.roles=F)},{default:w(()=>[(ne(!0),fe(He,null,je(n.value,F=>(ne(),et(P,{key:F.value,label:F.value},{default:w(()=>[J(Ve(F.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])}}},Af=an(Mf,[["__scopeId","data-v-bd9df1a3"]]),Nf={class:"role-management-container"},If={class:"toolbar flex justify-end items-center mb-4"},Tf={__name:"RoleManagement",setup(e){const t=H([]),n=H(null),r=async()=>{try{const a=new FormData;a.append("controlCode","query");const c=await ce.post("/api/role_List_manage.php",a);c.status===1?t.value=c.data.map(d=>({id:d.id,name:d.roleName,desc:d.roleDesc})):Q.error(c.message||"获取角色列表失败")}catch(a){console.error("获取角色列表失败:",a),Q.error("获取角色列表失败")}};at(()=>{r()});const i=H(!1),o=ze({id:null,name:"",desc:""}),l=()=>{n.value&&n.value.resetFields(),o.id=null,o.name="",o.desc="",i.value=!0},u=a=>{n.value&&n.value.resetFields(),o.id=a.id,o.name=a.name,o.desc=a.desc,i.value=!0},f=a=>{er.confirm(`确定要删除角色 "${a.name}" 吗？`,"提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const c=new FormData;c.append("controlCode","del"),c.append("id",a.id);const d=await ce.post("/api/role_List_manage.php",c);d.status===1?(Q.success("删除成功"),r()):Q.error(d.message||"删除失败")}catch(c){console.error("删除角色失败:",c),Q.error("删除角色失败: "+c.message)}}).catch(()=>{Q.info("已取消删除")})},s=async()=>{if(!o.name){Q.error("角色名称不能为空");return}try{const a=new FormData;a.append("controlCode",o.id?"modify":"add"),a.append("roleName",o.name),a.append("roleDesc",o.desc),o.id&&a.append("id",o.id);const c=await ce.post("/api/role_List_manage.php",a);c.status===1?(Q.success(o.id?"更新成功":"添加成功"),i.value=!1,r()):Q.error(c.message||(o.id?"更新失败":"添加失败"))}catch(a){console.error("操作失败:",a),Q.error("操作失败: "+a.message)}};return(a,c)=>{const d=L("el-icon"),p=L("el-button"),g=L("el-table-column"),R=L("el-button-group"),x=L("el-table"),S=L("el-input"),E=L("el-form-item"),$=L("el-form"),A=L("el-dialog");return ne(),fe("div",Nf,[q("div",If,[h(p,{type:"primary",onClick:l},{default:w(()=>[h(d,null,{default:w(()=>[h(re(mi))]),_:1}),c[4]||(c[4]=J(" 添加角色 "))]),_:1,__:[4]})]),h(x,{data:t.value,style:{width:"100%"},border:""},{default:w(()=>[h(g,{label:"序号",width:"80",align:"center"},{default:w(I=>[J(Ve(I.$index+1),1)]),_:1}),h(g,{prop:"name",label:"角色名称"}),h(g,{prop:"desc",label:"角色描述"}),h(g,{label:"操作"},{default:w(I=>[h(R,null,{default:w(()=>[h(p,{size:"mini",type:"warning",onClick:N=>u(I.row)},{default:w(()=>[h(d,null,{default:w(()=>[h(re(Jn))]),_:1})]),_:2},1032,["onClick"]),h(p,{size:"mini",type:"danger",onClick:N=>f(I.row)},{default:w(()=>[h(d,null,{default:w(()=>[h(re(jn))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),h(A,{modelValue:i.value,"onUpdate:modelValue":c[3]||(c[3]=I=>i.value=I),width:"20%"},{default:w(()=>[h($,{model:o,"label-width":"100px",ref_key:"formRef",ref:n},{default:w(()=>[h(E,{label:"角色名称",prop:"name"},{default:w(()=>[h(S,{modelValue:o.name,"onUpdate:modelValue":c[0]||(c[0]=I=>o.name=I)},null,8,["modelValue"])]),_:1}),h(E,{label:"角色描述",prop:"desc"},{default:w(()=>[h(S,{modelValue:o.desc,"onUpdate:modelValue":c[1]||(c[1]=I=>o.desc=I),placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),footer:w(()=>[h(p,{onClick:c[2]||(c[2]=I=>i.value=!1)},{default:w(()=>c[5]||(c[5]=[J("取消")])),_:1,__:[5]}),h(p,{type:"primary",onClick:s},{default:w(()=>c[6]||(c[6]=[J("确定")])),_:1,__:[6]})]),_:1},8,["modelValue"])])}}},Uf=an(Tf,[["__scopeId","data-v-2a4abc11"]]),Of=[{path:"/unit-management",name:"UnitManagement",component:Ql},{path:"/user-management",name:"UserManagement",component:da},{path:"/visual-screen",name:"VisualScreen",component:$f},{path:"/app-management",name:"AppManagement",component:Af},{path:"/role-management",name:"RoleManagement",component:Uf}],Df=Il({history:ul(),routes:Of}),pn=Vo(Yl);pn.use(Mo);for(const[e,t]of Object.entries(Ao))pn.component(e,t);pn.use(Df);pn.mount("#app");
