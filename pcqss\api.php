<?php
require_once '../conn_waf.php';
$APP_ID = 32; // 警情质量监督系统

header('Content-Type: application/json; charset=utf-8');

// WAF安全检查
waf_check_all_inputs();

/**
 * 获取警情列表
 */
function getAlarmList() {
    global $conn;

    try {
        // 获取分页参数
        $page = isset($_POST['page']) ? (int)$_POST['page'] : 1;
        $pageSize = isset($_POST['pageSize']) ? (int)$_POST['pageSize'] : 10;

        // 获取搜索参数
        $org = isset($_POST['org']) ? trim($_POST['org']) : '';
        $officer = isset($_POST['officer']) ? trim($_POST['officer']) : '';
        $dateStart = isset($_POST['dateStart']) ? $_POST['dateStart'] : '';
        $dateEnd = isset($_POST['dateEnd']) ? $_POST['dateEnd'] : '';

        // 验证参数
        if ($page < 1) $page = 1;
        if ($pageSize < 1 || $pageSize > 50) $pageSize = 10;

        $offset = ($page - 1) * $pageSize;

        // 构建WHERE条件
        $whereConditions = [];
        $params = [];
        $types = '';

        if (!empty($org)) {
            $whereConditions[] = "governOrgName = ?";
            $params[] = $org;
            $types .= 's';
        }

        if (!empty($officer)) {
            $whereConditions[] = "receiveUserName LIKE ?";
            $params[] = '%' . $officer . '%';
            $types .= 's';
        }

        if (!empty($dateStart)) {
            $whereConditions[] = "alarmTime >= ?";
            $params[] = $dateStart;
            $types .= 's';
        }

        if (!empty($dateEnd)) {
            $whereConditions[] = "alarmTime <= ?";
            $params[] = $dateEnd;
            $types .= 's';
        }

        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }

        // 构建查询SQL
        $sql = "SELECT
                    *
                FROM 32_pcqss_alarm
                $whereClause
                ORDER BY alarmTime DESC
                LIMIT ? OFFSET ?";

        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception('SQL准备失败: ' . $conn->error);
        }

        // 绑定参数
        $params[] = $pageSize;
        $params[] = $offset;
        $types .= 'ii';

        $stmt->bind_param($types, ...$params);
        
        if (!$stmt->execute()) {
            throw new Exception('查询执行失败: ' . $stmt->error);
        }
        
        $result = $stmt->get_result();
        $data = [];
        
        while ($row = $result->fetch_assoc()) {
            // 处理数据格式
            $data[] = [
                'alarmNum' => $row['alarmNum'],
                'alarmContent' => $row['alarmContent'],
                'governOrgName' => $row['governOrgName'],
                'alarmCategoryName' => $row['alarmCategoryName'],
                'alarmTypeName' => $row['alarmTypeName'],
                'alarmSubclassName' => $row['alarmSubclassName'],
                'alarmDetailName' => $row['alarmDetailName'],
                'alarmTime' => $row['alarmTime'],
                'receiveUserName' => $row['receiveUserName'],
                'alarmErrorWord' => $row['alarmErrorWord'],
                'adviceCategory1' => $row['adviceCategory1'],
                'adviceCategory2' => $row['adviceCategory2'],
                'adviceCategory3' => $row['adviceCategory3'],
                'adviceCategory4' => $row['adviceCategory4'],
                'alarmAdvice' => $row['alarmAdvice'],
                'arriveTime' => $row['arriveTime'],
                'receiveTime' => $row['receiveTime'],
                'resultDescribe' => $row['resultDescribe'],
                'feedbackAdvice' => $row['feedbackAdvice'],
                'feedbackError' => $row['feedbackError'],
                'status' => (int)$row['status']
            ];
        }
        
        $stmt->close();
        
        // 获取总记录数（应用相同的搜索条件）
        $countSql = "SELECT COUNT(*) as total FROM 32_pcqss_alarm $whereClause";

        if (!empty($whereConditions)) {
            $countStmt = $conn->prepare($countSql);
            if ($countStmt) {
                $countTypes = substr($types, 0, -2); // 移除最后的'ii'（分页参数）
                $countParams = array_slice($params, 0, -2); // 移除最后两个分页参数
                if (!empty($countParams)) {
                    $countStmt->bind_param($countTypes, ...$countParams);
                }
                $countStmt->execute();
                $countResult = $countStmt->get_result();
                $totalCount = $countResult ? $countResult->fetch_assoc()['total'] : 0;
                $countStmt->close();
            } else {
                $totalCount = 0;
            }
        } else {
            $countResult = $conn->query($countSql);
            $totalCount = $countResult ? $countResult->fetch_assoc()['total'] : 0;
        }
        
        // 返回成功响应
        echo json_encode([
            'status' => 1,
            'message' => '查询成功',
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'page_size' => $pageSize,
                'total_count' => (int)$totalCount,
                'total_pages' => ceil($totalCount / $pageSize)
            ]
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        echo json_encode([
            'status' => 0,
            'message' => '查询失败: ' . $e->getMessage(),
            'data' => []
        ], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * 获取警情统计信息
 */
function getAlarmStatistics() {
    global $conn;
    
    try {
        // 获取总数统计
        $totalSql = "SELECT COUNT(*) as total FROM 32_pcqss_alarm";
        $totalResult = $conn->query($totalSql);
        $total = $totalResult ? $totalResult->fetch_assoc()['total'] : 0;
        
        // 获取已优化数量
        $optimizedSql = "SELECT COUNT(*) as optimized FROM 32_pcqss_alarm WHERE status = 1";
        $optimizedResult = $conn->query($optimizedSql);
        $optimized = $optimizedResult ? $optimizedResult->fetch_assoc()['optimized'] : 0;
        
        // 获取待优化数量
        $pendingSql = "SELECT COUNT(*) as pending FROM 32_pcqss_alarm WHERE status = 0";
        $pendingResult = $conn->query($pendingSql);
        $pending = $pendingResult ? $pendingResult->fetch_assoc()['pending'] : 0;
        
        // 获取各派出所统计
        $unitStatsSql = "SELECT 
                            governOrgName,
                            COUNT(*) as total,
                            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as optimized,
                            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending
                        FROM 32_pcqss_alarm 
                        WHERE governOrgName IS NOT NULL AND governOrgName != ''
                        GROUP BY governOrgName 
                        ORDER BY total DESC";
        
        $unitStatsResult = $conn->query($unitStatsSql);
        $unitStats = [];
        
        if ($unitStatsResult) {
            while ($row = $unitStatsResult->fetch_assoc()) {
                $unitStats[] = [
                    'unit_name' => $row['governOrgName'],
                    'total' => (int)$row['total'],
                    'optimized' => (int)$row['optimized'],
                    'pending' => (int)$row['pending'],
                    'optimization_rate' => $row['total'] > 0 ? round(($row['optimized'] / $row['total']) * 100, 2) : 0
                ];
            }
        }
        
        // 计算优化率
        $optimizationRate = $total > 0 ? round(($optimized / $total) * 100, 2) : 0;
        
        echo json_encode([
            'status' => 1,
            'message' => '统计查询成功',
            'data' => [
                'total' => (int)$total,
                'optimized' => (int)$optimized,
                'pending' => (int)$pending,
                'optimization_rate' => $optimizationRate,
                'unit_statistics' => $unitStats
            ]
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        echo json_encode([
            'status' => 0,
            'message' => '统计查询失败: ' . $e->getMessage(),
            'data' => []
        ], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * 获取派出所列表
 */
function getOrganizations() {
    global $conn;

    try {
        $sql = "SELECT DISTINCT governOrgName FROM 32_pcqss_alarm
                WHERE governOrgName IS NOT NULL AND governOrgName != ''
                ORDER BY governOrgName";

        $result = $conn->query($sql);
        $organizations = [];

        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $organizations[] = $row['governOrgName'];
            }
        }

        echo json_encode([
            'status' => 1,
            'message' => '查询成功',
            'data' => $organizations
        ], JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        echo json_encode([
            'status' => 0,
            'message' => '查询失败: ' . $e->getMessage(),
            'data' => []
        ], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * 重新优化警情
 */
function reoptimizeAlarm() {
    global $conn;

    try {
        $alarmNum = $_POST['alarmNum'] ?? '';

        if (empty($alarmNum)) {
            throw new Exception('警情编号不能为空');
        }

        // 检查警情是否存在
        $checkSql = "SELECT status FROM 32_pcqss_alarm WHERE alarmNum = ?";
        $checkStmt = $conn->prepare($checkSql);

        if (!$checkStmt) {
            throw new Exception('SQL准备失败: ' . $conn->error);
        }

        $checkStmt->bind_param('s', $alarmNum);

        if (!$checkStmt->execute()) {
            throw new Exception('查询执行失败: ' . $checkStmt->error);
        }

        $result = $checkStmt->get_result();
        $alarm = $result->fetch_assoc();

        if (!$alarm) {
            throw new Exception('未找到指定的警情记录');
        }

        if ($alarm['status'] != 1) {
            throw new Exception('只有已优化的警情才能重新优化');
        }

        $checkStmt->close();

        // 更新状态为待优化
        $updateSql = "UPDATE 32_pcqss_alarm SET status = 0 WHERE alarmNum = ?";
        $updateStmt = $conn->prepare($updateSql);

        if (!$updateStmt) {
            throw new Exception('SQL准备失败: ' . $conn->error);
        }

        $updateStmt->bind_param('s', $alarmNum);

        if (!$updateStmt->execute()) {
            throw new Exception('更新执行失败: ' . $updateStmt->error);
        }

        $updateStmt->close();

        echo json_encode([
            'status' => 1,
            'message' => '重新优化请求已提交，状态已更新为待优化',
            'data' => []
        ], JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        echo json_encode([
            'status' => 0,
            'message' => '操作失败: ' . $e->getMessage(),
            'data' => []
        ], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * 获取警情详情
 */
function getAlarmDetail() {
    global $conn;
    
    try {
        $alarmNum = $_POST['alarmNum'] ?? '';
        
        if (empty($alarmNum)) {
            throw new Exception('警情编号不能为空');
        }
        
        $sql = "SELECT * FROM 32_pcqss_alarm WHERE alarmNum = ?";
        $stmt = $conn->prepare($sql);
        
        if (!$stmt) {
            throw new Exception('SQL准备失败: ' . $conn->error);
        }
        
        $stmt->bind_param('s', $alarmNum);
        
        if (!$stmt->execute()) {
            throw new Exception('查询执行失败: ' . $stmt->error);
        }
        
        $result = $stmt->get_result();
        $data = $result->fetch_assoc();
        
        if (!$data) {
            throw new Exception('未找到指定的警情记录');
        }
        
        $stmt->close();
        
        echo json_encode([
            'status' => 1,
            'message' => '查询成功',
            'data' => $data
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        echo json_encode([
            'status' => 0,
            'message' => '查询失败: ' . $e->getMessage(),
            'data' => []
        ], JSON_UNESCAPED_UNICODE);
    }
}

// 主逻辑处理
try {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'getAlarmList':
            getAlarmList();
            break;

        case 'getAlarmStatistics':
            getAlarmStatistics();
            break;

        case 'getOrganizations':
            getOrganizations();
            break;

        case 'getAlarmDetail':
            getAlarmDetail();
            break;

        case 'reoptimizeAlarm':
            reoptimizeAlarm();
            break;

        default:
            echo json_encode([
                'status' => 0,
                'message' => '无效的操作类型',
                'data' => []
            ], JSON_UNESCAPED_UNICODE);
            break;
    }
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 0,
        'message' => '系统错误: ' . $e->getMessage(),
        'data' => []
    ], JSON_UNESCAPED_UNICODE);
}
?>
