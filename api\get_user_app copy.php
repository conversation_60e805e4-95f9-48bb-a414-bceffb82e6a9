<?php
header('Content-Type: application/json');
require_once '../conn_waf.php';


// 数据库连接
try {
    $user_id = $_SESSION['user_id'];
    // 假设这里有一个方法可以检查用户是否为管理员
    // 您需要根据实际情况替换这个方法


    if ( $_SESSION['is_admin']) {
        // 管理员拥有所有权限
        $query = "SELECT
            a.id,
            a.application_name,
            a.url,
            a.public,
            1 AS has_permission
        FROM
            5_application a
        WHERE
            a.id > 1
        ORDER BY
            a.id;";
        $params = [];
    } else {
        // 普通用户按原逻辑查询权限
        $query = "SELECT
            a.id,
            a.application_name,
            a.url,
            a.public,
            CASE WHEN ur.userId IS NOT NULL THEN 1 ELSE 0 END AS has_permission
        FROM
            5_application a
        LEFT JOIN
            3_user_Role ur ON a.id = ur.appId AND ur.userId = ?
        WHERE
            a.id > 1
        ORDER BY
            a.id;";
        $params = [$user_id];
    }

    $stmt = $conn->prepare($query);
    if (!empty($params)) {
        $types = str_repeat('i', count($params));
        $stmt->bind_param($types, ...$params);
    }

    $stmt->execute();
    $result = $stmt->get_result();
    $apps = $result->fetch_all(MYSQLI_ASSOC);

    echo json_encode(['status' => 1, 'message'=>"查询成功", 'data' => $apps]);
} catch (Exception $e) {
    echo json_encode(['status' => 0, 'message' => $e->getMessage()]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>