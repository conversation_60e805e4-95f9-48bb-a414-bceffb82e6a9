import{_ as N,r as c,b as v,o as P,l as r,f as S,q as L,g as n,m as $,j as l,k as t,x as w,e as E,E as U,T as q,U as B,V as T}from"./index-xpKufulQ.js";const h="/assets/login-box-bg-CL6i7T2F.svg",j={class:"login-container"},D={class:"login-content"},R={class:"login-right"},z={__name:"Login",setup(g){const m=c(null),o=v({id_number:"",password:"",rememberMe:!1}),V=v({id_number:[{required:!0,message:"",trigger:"blur"}],password:[{required:!0,message:"",trigger:"blur"}]}),u=c(!1),d=c(!1),I=()=>{d.value=!d.value};P(()=>{const s=localStorage.getItem("savedIdNumber"),e=localStorage.getItem("savedPassword");s&&(o.id_number=s),e&&(o.password=e,o.rememberMe=!0)});const x=s=>{console.log("表单被点击:",s.target)},_=s=>{console.log(`${s} 输入框获得焦点`),console.log(`${s} 当前值:`,o[s])},k=s=>{var e;console.log("输入内容变化:",(e=s.target)==null?void 0:e.value)},y=async()=>{try{u.value=!0;const s=new FormData;s.append("id_number",o.id_number),s.append("password",o.password);const e=await E.post("/api/login.php",s);o.rememberMe?(localStorage.setItem("savedIdNumber",o.id_number),localStorage.setItem("savedPassword",o.password)):(localStorage.removeItem("savedIdNumber"),localStorage.removeItem("savedPassword")),U.success(`${e.user.name} 欢迎回来！`),console.log("登录成功:",e),window.location.href="index.html"}finally{u.value=!1}};return(s,e)=>{const b=r("el-input"),i=r("el-form-item"),f=r("el-button"),C=r("el-checkbox"),F=r("el-form"),M=r("el-card");return L(),S("div",j,[e[9]||(e[9]=n("div",{class:"header-logo"},[n("img",{src:$,class:"logo"}),n("h2",null,"CloudPivot")],-1)),n("div",D,[e[8]||(e[8]=n("div",{class:"login-left"},[n("img",{src:h,class:"login-img"})],-1)),n("div",R,[l(M,{class:"login-card"},{default:t(()=>[e[7]||(e[7]=n("h2",{class:"login-title"},"登录",-1)),l(F,{ref_key:"loginFormRef",ref:m,model:o,rules:V,"label-width":"80px",onClick:x},{default:t(()=>[l(i,{label:"身份证号",prop:"id_number"},{default:t(()=>[l(b,{modelValue:o.id_number,"onUpdate:modelValue":e[0]||(e[0]=a=>o.id_number=a),placeholder:"请输入身份证号",onFocus:e[1]||(e[1]=a=>_("id_number")),onInput:k},null,8,["modelValue"])]),_:1}),l(i,{label:"密码",prop:"password"},{default:t(()=>[l(b,{modelValue:o.password,"onUpdate:modelValue":e[2]||(e[2]=a=>o.password=a),type:d.value?"text":"password",placeholder:"请输入密码",onFocus:e[3]||(e[3]=a=>_("password"))},{suffix:t(()=>[l(f,{icon:d.value?"View":"Hide",onClick:I,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),l(i,{class:"form-item"},{default:t(()=>[l(C,{modelValue:o.rememberMe,"onUpdate:modelValue":e[4]||(e[4]=a=>o.rememberMe=a)},{default:t(()=>e[5]||(e[5]=[w("记住我")])),_:1,__:[5]},8,["modelValue"])]),_:1}),l(i,null,{default:t(()=>[l(f,{type:"primary",round:"",loading:u.value,onClick:y,class:"login-button"},{default:t(()=>e[6]||(e[6]=[w(" 登录 ")])),_:1,__:[6]},8,["loading"])]),_:1})]),_:1},8,["model","rules"])]),_:1,__:[7]})])])])}}},A=N(z,[["__scopeId","data-v-2c18ceb4"]]),p=q(A);p.use(B);for(const[g,m]of Object.entries(T))p.component(g,m);p.mount("#app");
